<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
       Schema::create('conversations', function (Blueprint $table) {
            $table->id();
            $table->uuid()->unique(); // Unique identifier for the conversation

            // Basic conversation info
            $table->string('title')->nullable();

            // Bot reference
            $table->unsignedBigInteger('bot_id');
            $table->foreign('bot_id')->references('id')->on('bots')->onDelete('cascade');

            // Polymorphic owner relationship (User hoặc Guest)
            $table->morphs('owner'); // Tạo owner_id và owner_type

            // Conversation status
            $table->string('status')->default('active')->comment('active, archived');

            // Basic tracking
            $table->unsignedInteger('message_count')->default(0);
            $table->timestamp('last_message_at')->nullable();

            // Timestamps
            $table->timestamps();

            // Essential indexes
            $table->index('status');
            $table->index(['bot_id', 'status']);
            $table->index(['owner_id', 'owner_type']);
            $table->index('last_message_at');
        });
    
        Schema::create('conversation_knowledge_bases', function (Blueprint $table) {
            $table->foreignId('conversation_id')->constrained()->cascadeOnDelete();
            $table->foreignId('knowledge_base_id')->constrained()->cascadeOnDelete();
            $table->primary(['conversation_id', 'knowledge_base_id']);
        });
    
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conversations');
    }
};
