<?php

namespace Modules\ChatBot\Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Modules\User\Models\User;
use Tests\TestCase;

class PromptGeneratorControllerTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
        Config::set('services.google.api_key', 'test-api-key');
    }

    /** @test */
    public function it_can_get_prompt_types()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/auth/prompts/types');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'prompt_types' => [
                        'system' => [
                            'name',
                            'description',
                            'input',
                            'example'
                        ],
                        'getting_message' => [
                            'name',
                            'description',
                            'input',
                            'example'
                        ],
                        'starting_message' => [
                            'name',
                            'description',
                            'input',
                            'example'
                        ]
                    ],
                    'total'
                ]
            ]);

        $this->assertEquals(3, $response->json('data.total'));
    }

    /** @test */
    public function it_can_generate_system_prompt()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => 'Bạn là một trợ lý AI chuyên về giáo dục toán học. Tôi có thể giúp bạn giải quyết các bài toán và giải thích khái niệm toán học một cách dễ hiểu.'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/system', [
                'bot_role' => 'Giáo viên dạy Toán lớp 12'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'bot_role',
                    'system_prompt'
                ]
            ]);

        $this->assertEquals('Giáo viên dạy Toán lớp 12', $response->json('data.bot_role'));
        $this->assertNotEmpty($response->json('data.system_prompt'));
    }

    /** @test */
    public function it_validates_system_prompt_request()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/system', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['bot_role']);
    }

    /** @test */
    public function it_can_generate_greeting_message()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => 'Xin chào! Tôi là MathBot, sẵn sàng giúp bạn học toán!'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/greeting', [
                'bot_name' => 'MathBot'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'bot_name',
                    'greeting_message'
                ]
            ]);

        $this->assertEquals('MathBot', $response->json('data.bot_name'));
        $this->assertStringContainsString('MathBot', $response->json('data.greeting_message'));
    }

    /** @test */
    public function it_validates_greeting_message_request()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/greeting', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['bot_name']);
    }

    /** @test */
    public function it_can_generate_starting_questions()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => '["Bạn có thể giải phương trình bậc hai không?", "Làm thế nào để tính đạo hàm?", "Giải thích về tích phân là gì?", "Hướng dẫn giải hệ phương trình tuyến tính?"]'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/questions', [
                'system_prompt' => 'Bạn là trợ lý AI chuyên về toán học'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'system_prompt',
                    'questions',
                    'raw_response'
                ]
            ]);

        $questions = $response->json('data.questions');
        $this->assertIsArray($questions);
        $this->assertGreaterThan(0, count($questions));
    }

    /** @test */
    public function it_validates_starting_questions_request()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/questions', []);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['system_prompt']);
    }

    /** @test */
    public function it_can_generate_all_prompts()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => 'Generated content'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/all', [
                'bot_role' => 'Giáo viên dạy Toán',
                'bot_name' => 'MathBot'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'bot_role',
                    'bot_name',
                    'system_prompt',
                    'greeting_message',
                    'starting_questions'
                ]
            ]);

        $this->assertEquals('Giáo viên dạy Toán', $response->json('data.bot_role'));
        $this->assertEquals('MathBot', $response->json('data.bot_name'));
    }

    /** @test */
    public function it_validates_generate_all_prompts_request()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/all', [
                'bot_role' => 'Test Role'
                // Missing bot_name
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['bot_name']);
    }

    /** @test */
    public function it_can_test_gemini_connection()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => 'Xin chào! Tôi có thể giúp bạn.'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/auth/prompts/test-gemini');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'test_prompt',
                    'response',
                    'status'
                ]
            ]);

        $this->assertEquals('connected', $response->json('data.status'));
    }

    /** @test */
    public function it_handles_gemini_connection_failure()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'error' => [
                    'code' => 401,
                    'message' => 'Invalid API key'
                ]
            ], 401)
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/auth/prompts/test-gemini');

        $response->assertStatus(500)
            ->assertJson([
                'success' => false
            ]);

        $this->assertStringContainsString('connection failed', $response->json('message'));
    }

    /** @test */
    public function it_requires_authentication()
    {
        $response = $this->getJson('/api/v1/auth/prompts/types');
        $response->assertStatus(401);

        $response = $this->postJson('/api/v1/auth/prompts/system', [
            'bot_role' => 'Test Role'
        ]);
        $response->assertStatus(401);
    }

    /** @test */
    public function it_handles_api_errors_gracefully()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'error' => [
                    'code' => 500,
                    'message' => 'Internal server error'
                ]
            ], 500)
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/system', [
                'bot_role' => 'Test Role'
            ]);

        $response->assertStatus(500)
            ->assertJson([
                'success' => false
            ]);
    }

    /** @test */
    public function it_handles_invalid_json_in_starting_questions()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => 'This is not valid JSON'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/auth/prompts/questions', [
                'system_prompt' => 'Test prompt'
            ]);

        $response->assertStatus(200);
        
        $questions = $response->json('data.questions');
        $this->assertIsArray($questions);
        $this->assertEquals(['This is not valid JSON'], $questions);
    }
}
