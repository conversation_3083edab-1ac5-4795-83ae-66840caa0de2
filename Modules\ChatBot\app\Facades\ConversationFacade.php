<?php

namespace Modules\ChatBot\Facades;

use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Facade;
use Modules\ChatBot\Models\Conversation;

/**
 * Conversation Facade
 *
 * Provides convenient access to conversation functionality through a static interface.
 * Only exposes public/user-level methods. Administrative methods should be accessed
 * through direct service injection.
 *
 * @method static Conversation|string createOrUpdateConversation(array $data)
 * @method static Conversation archiveConversation(Conversation $conversation)
 * @method static Conversation reactivateConversation(Conversation $conversation)
 * @method static Conversation completeConversation(Conversation $conversation)
 * @method static Conversation updateConversation(Conversation $conversation, array $data)
 * @method static void deleteConversation(Conversation $conversation)
 * @method static LengthAwarePaginator getUserConversations(int $userId, string $userType, array $filters = [], int $perPage = 15)
 * @method static LengthAwarePaginator getBotConversations(int $botId, array $filters = [], int $perPage = 15)
 * @method static LengthAwarePaginator searchConversations(string $query, int $userId = null, string $userType = null, int $perPage = 15)
 * @method static Collection getRecentConversations(int $userId, string $userType, int $limit = 5)
 * @method static int getActiveConversationsCount(int $userId, string $userType)
 *
 * @see \Modules\ChatBot\Services\ConversationService
 */
class ConversationFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'conversation.service';
    }
}
