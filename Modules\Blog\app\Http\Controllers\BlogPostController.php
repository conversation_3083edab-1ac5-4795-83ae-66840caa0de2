<?php

namespace Modules\Blog\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Traits\ResponseTrait;
use Modules\Blog\Facades\BlogFacade;

class BlogPostController extends Controller
{
    use ResponseTrait;

    /**
     * Display a list of published blog posts (with pagination).
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'category_id' => 'nullable|integer|exists:blog_categories,id'
        ]);
        
        $posts = BlogFacade::getPostsByCategoryId($request->category_id, currentLocale());

        $items = $posts->items();

        return response()->json([
            'success' => true,
            'message' => __('Blog posts retrieved successfully.'),
            'data' => collect($items)->map(function ($post) {
                return [
                    'id' => $post['id'] ?? null,
                    'title' => $post['title'] ?? '',
                    'slug' => $post['slug'] ?? '',
                    'summary' => $post['summary'] ?? '',
                    'image' => $post['image'] ?? null,
                    'published_at' => $post['published_at'] ?? null,
                    'author' => isset($post['author']) && $post['author'] ? [
                        'id' => $post['author']['id'] ?? null,
                        'name' => $post['author']['first_name'] . ' ' . $post['author']['last_name'] ?? ''
                    ] : null
                ];
            }),
            'total' => $posts->total()
        ]);
    }

    /**
     * Display a specific blog post by slug.
     *
     * @param string $slug
     * @return JsonResponse
     */
    public function show(string $slug): JsonResponse
    {
        $locale = currentLocale();
        $post = BlogFacade::getPostBySlug($slug, $locale);

        if (!$post) {
            return $this->notFoundResponse(null, 'Post not found');
        }

        return $this->successResponse($post);
    }

    /**
     * Display a list of featured blog posts.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function featured(Request $request): JsonResponse
    {
        $request->validate([
            'category_id' => 'nullable|integer|exists:blog_categories,id',
        ]);
        return $this->successResponse(BlogFacade::getFeaturePosts(currentLocale(), $request->category_id));
    }

    /**
     * Display a list of most popular blog posts.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function popular(Request $request): JsonResponse
    {
        $request->validate([
            'category_id' => 'nullable|integer|exists:blog_categories,id',
        ]);

        return $this->successResponse(BlogFacade::getTopPosts(currentLocale(), $request->category_id));
    }

    /**
     * Display a list of latest blog posts.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function latest(Request $request): JsonResponse
    {
        $request->validate([
            'category_id' => 'nullable|integer|exists:blog_categories,id',
        ]);
        return $this->successResponse(BlogFacade::getLatestPosts(currentLocale(), $request->category_id));
    }
}
