<?php

namespace Modules\Blog\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use PHPUnit\Framework\Attributes\Test;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Models\BlogCategoryTranslation;
use Modules\Blog\Models\BlogPostTranslation;
use Modules\Blog\Services\BlogService;
use Modules\Blog\Enums\BlogStatus;
use Modules\User\Models\User;

class BlogServiceTest extends TestCase
{
    use RefreshDatabase;

    protected BlogService $blogService;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Blog module
        $this->artisan('migrate', ['--path' => 'Modules/Blog/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);

        $this->blogService = new BlogService();
    }

    #[Test]
    public function it_can_be_instantiated()
    {
        $this->assertInstanceOf(BlogService::class, $this->blogService);
    }

    #[Test]
    public function it_can_get_root_categories()
    {
        // Create active root categories
        $activeCategory1 = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);
        $activeCategory2 = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);
        
        // Create inactive category
        $inactiveCategory = BlogCategory::factory()->create(['status' => BlogStatus::Inactive, 'parent_id' => null]);
        
        // Create child category
        $childCategory = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => $activeCategory1->id]);

        // Create translations
        BlogCategoryTranslation::factory()->create(['category_id' => $activeCategory1->id, 'locale' => 'en']);
        BlogCategoryTranslation::factory()->create(['category_id' => $activeCategory2->id, 'locale' => 'en']);
        BlogCategoryTranslation::factory()->create(['category_id' => $inactiveCategory->id, 'locale' => 'en']);
        BlogCategoryTranslation::factory()->create(['category_id' => $childCategory->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = $this->blogService->getRootCategories('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
        $this->assertTrue($result->contains('id', $activeCategory1->id));
        $this->assertTrue($result->contains('id', $activeCategory2->id));
        $this->assertFalse($result->contains('id', $inactiveCategory->id));
        $this->assertFalse($result->contains('id', $childCategory->id));
    }

    #[Test]
    public function it_returns_empty_collection_for_no_categories()
    {
        App::setLocale('en');
        $result = $this->blogService->getRootCategories('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(0, $result);
    }

    #[Test]
    public function it_can_get_root_categories_with_specific_locale()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);
        BlogCategoryTranslation::factory()->create(['category_id' => $category->id, 'locale' => 'vi']);

        App::setLocale('vi');
        $result = $this->blogService->getRootCategories('vi');

        $this->assertCount(1, $result);
        $this->assertEquals('vi', $result->first()->translations->first()->locale);
    }

    #[Test]
    public function it_can_get_category_by_slug()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active]);
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'en',
            'slug' => 'test-category'
        ]);

        App::setLocale('en');
        $result = $this->blogService->getCategoryBySlug('test-category', 'en');

        $this->assertInstanceOf(BlogCategory::class, $result);
        $this->assertEquals($category->id, $result->id);
    }

    #[Test]
    public function it_returns_null_when_category_not_found_by_slug()
    {
        App::setLocale('en');
        $result = $this->blogService->getCategoryBySlug('non-existent-slug', 'en');

        $this->assertNull($result);
    }

    #[Test]
    public function it_returns_null_for_inactive_category()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Inactive]);
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'en',
            'slug' => 'inactive-category'
        ]);

        App::setLocale('en');
        $result = $this->blogService->getCategoryBySlug('inactive-category', 'en');

        $this->assertNull($result);
    }

    #[Test]
    public function it_can_get_posts_by_category_id()
    {
        $user = User::factory()->create();
        $category = BlogCategory::factory()->create();
        $post1 = BlogPost::factory()->create(['status' => BlogStatus::Published, 'author_id' => $user->id, 'published_at' => now()->subDay()]);
        $post2 = BlogPost::factory()->create(['status' => BlogStatus::Published, 'author_id' => $user->id, 'published_at' => now()->subDays(2)]);
        $post3 = BlogPost::factory()->create(['status' => BlogStatus::Draft, 'author_id' => $user->id]); // Should not be included

        // Attach posts to category
        $post1->categories()->attach($category->id);
        $post2->categories()->attach($category->id);
        $post3->categories()->attach($category->id);

        // Create translations
        BlogPostTranslation::factory()->create(['post_id' => $post1->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $post2->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $post3->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = $this->blogService->getPostsByCategoryId($category->id, 'en');

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertCount(2, $result->items()); // Only published posts
    }

    #[Test]
    public function it_can_get_feature_posts()
    {
        $user = User::factory()->create();
        
        // Create published featured posts
        $post1 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDays(1),
            'featured' => true
        ]);
        $post2 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDays(2),
            'featured' => true
        ]);
        
        // Create draft post (should not be included)
        $draftPost = BlogPost::factory()->create(['status' => BlogStatus::Draft, 'author_id' => $user->id]);

        // Create translations
        BlogPostTranslation::factory()->create(['post_id' => $post1->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $post2->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $draftPost->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = $this->blogService->getFeaturePosts('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
        
        // Should be ordered by published_at desc
        $this->assertEquals($post1->id, $result->first()->id);
        $this->assertEquals($post2->id, $result->last()->id);
    }

    #[Test]
    public function it_can_get_post_by_slug()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published, 
            'author_id' => $user->id,
            'published_at' => now()->subDay()
        ]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'test-post'
        ]);

        App::setLocale('en');
        $result = $this->blogService->getPostBySlug('test-post', 'en');

        $this->assertInstanceOf(BlogPost::class, $result);
        $this->assertEquals($post->id, $result->id);
    }

    #[Test]
    public function it_returns_null_when_post_not_found_by_slug()
    {
        App::setLocale('en');
        $result = $this->blogService->getPostBySlug('non-existent-slug', 'en');

        $this->assertNull($result);
    }

    #[Test]
    public function it_returns_null_for_draft_post()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create(['status' => BlogStatus::Draft, 'author_id' => $user->id]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'draft-post'
        ]);

        App::setLocale('en');
        $result = $this->blogService->getPostBySlug('draft-post', 'en');

        $this->assertNull($result);
    }

    #[Test]
    public function it_returns_null_for_future_published_post()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->addDay()
        ]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'future-post'
        ]);

        App::setLocale('en');
        $result = $this->blogService->getPostBySlug('future-post', 'en');

        $this->assertNull($result);
    }

    #[Test]
    public function it_handles_empty_results_gracefully()
    {
        App::setLocale('en');
        $result = $this->blogService->getFeaturePosts('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(0, $result);
    }

    #[Test]
    public function it_loads_translations_for_current_locale_only()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDay()
        ]);

        // Create translations for multiple locales
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'english-post',
            'title' => 'English Title'
        ]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'vi',
            'slug' => 'vietnamese-post',
            'title' => 'Vietnamese Title'
        ]);

        App::setLocale('en');
        $result = $this->blogService->getPostBySlug('english-post', 'en');

        $this->assertInstanceOf(BlogPost::class, $result);
        $this->assertTrue($result->relationLoaded('translations'));
        $this->assertCount(1, $result->translations);
        $this->assertEquals('en', $result->translations->first()->locale);
        $this->assertEquals('English Title', $result->translations->first()->title);
    }

    #[Test]
    public function it_loads_author_relationship()
    {
        $user = User::factory()->create(['first_name' => 'John', 'last_name' => 'Doe']);
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDay()
        ]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'test-post'
        ]);

        App::setLocale('en');
        $result = $this->blogService->getPostBySlug('test-post', 'en');

        $this->assertInstanceOf(BlogPost::class, $result);
        $this->assertTrue($result->relationLoaded('author'));
        $this->assertEquals('John', $result->author->first_name);
        $this->assertEquals('Doe', $result->author->last_name);
    }

    #[Test]
    public function it_can_get_top_posts()
    {
        $user = User::factory()->create();

        $post1 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDays(1)
        ]);
        $post2 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDays(2)
        ]);

        // Create translations
        BlogPostTranslation::factory()->create(['post_id' => $post1->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $post2->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = $this->blogService->getTopPosts('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    #[Test]
    public function it_can_get_latest_posts()
    {
        $user = User::factory()->create();

        $post1 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDays(1)
        ]);
        $post2 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDays(2)
        ]);

        // Create translations
        BlogPostTranslation::factory()->create(['post_id' => $post1->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $post2->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = $this->blogService->getLatestPosts('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(2, $result);
    }

    #[Test]
    public function it_can_get_active_category_hierarchy()
    {
        // Create parent category
        $parent = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);

        // Create child categories
        $child1 = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => $parent->id]);
        $child2 = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => $parent->id]);

        // Create grandchild
        $grandchild = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => $child1->id]);

        // Create translations
        BlogCategoryTranslation::factory()->create(['category_id' => $parent->id, 'locale' => 'en', 'name' => 'Parent']);
        BlogCategoryTranslation::factory()->create(['category_id' => $child1->id, 'locale' => 'en', 'name' => 'Child 1']);
        BlogCategoryTranslation::factory()->create(['category_id' => $child2->id, 'locale' => 'en', 'name' => 'Child 2']);
        BlogCategoryTranslation::factory()->create(['category_id' => $grandchild->id, 'locale' => 'en', 'name' => 'Grandchild']);

        App::setLocale('en');
        $result = $this->blogService->getActiveCategoryHierarchy('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1, $result); // Only one root category

        // Check hierarchy structure
        $rootCategory = $result->first();
        $this->assertEquals($parent->id, $rootCategory->id);
        $this->assertCount(2, $rootCategory->children); // Two child categories
    }
}
