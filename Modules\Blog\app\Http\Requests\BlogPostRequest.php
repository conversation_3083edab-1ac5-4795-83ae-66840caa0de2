<?php

namespace Modules\Blog\Http\Requests;

use Modules\Blog\Models\BlogCategoryTranslation;
use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Blog\Enums\BlogStatus;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Models\BlogPostTranslation;
use Modules\Language\Models\Language;
use Modules\Page\Models\Page;
use Illuminate\Validation\Rule;

/**
 * @property mixed $id
 * @property mixed $post_id
 */
class BlogPostRequest extends BaseFormRequest
{
    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $postId = $this->route('post')?->id;

        return [
            'layout' => [
                'nullable',
                'string',
                'max:255',
            ],
            'status' => [
                'required',
                'string',
                Rule::in(array_column(BlogStatus::cases(), 'value'))
            ],
            'published_at' => [
                'nullable',
                'date',
            ],

            // Category relationships
            'categories' => [
                'nullable',
                'array',
            ],
            'categories.*' => [
                'integer',
                'exists:blog_categories,id',
            ],

            // Translations validation
            'translations' => [
                'required',
                'array',
                'min:1',
            ],
            'translations.*.locale' => [
                'required',
                'string',
                'max:10',
                function ($attribute, $value, $fail) {
                    $language = Language::where('code', $value)->first();
                    if ($language && $language->status !== 'active') {
                        $fail("The language '{$value}' is not active.");
                    }
                },
            ],
            'translations.*.title' => [
                'required',
                'string',
                'max:255',
            ],
            'translations.*.slug' => [
                'nullable',
                'string',
                'max:512',
                'alpha_dash',
                function ($attribute, $value, $fail) {
                    $index = (int)str_replace(['translations.', '.slug'], '', $attribute);
                    $locale = $this->input("translations.{$index}.locale");

                    if ($locale && $value) {
                        $query = BlogPostTranslation::query()
                            ->where('slug', $value)
                            ->where('locale', $locale);

                        if ($this->id) {
                            $query->where('post_id', '!=', $this->id);
                        }

                        if ($query->exists()) {
                            $fail(__("The slug ':value' is already used for locale '$locale'.", ['value' => $value, 'locale' => $locale]));
                        }
                    }
                },
            ],
            'translations.*.content' => [
                'required',
                'string',
            ],
            'translations.*.summary' => [
                'nullable',
                'string',
                'max:1000',
            ],
            'translations.*.image' => [
                'nullable',
                'string',
                'max:255',
            ],
            'translations.*.meta_title' => [
                'nullable',
                'string',
                'max:512',
            ],
            'translations.*.meta_description' => [
                'nullable',
                'string',
                'max:1000',
            ],
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'layout' => 'layout',
            'author_id' => 'author',
            'category_ids' => 'categories',
            'translations.*.locale' => 'locale',
            'translations.*.title' => 'title',
            'translations.*.slug' => 'slug',
            'translations.*.content' => 'content',
            'translations.*.summary' => 'summary',
            'translations.*.image' => 'image',
            'translations.*.meta_title' => 'meta title',
            'translations.*.meta_description' => 'meta description',
        ];
    }
}
