<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bots', function (Blueprint $table) {
            // --- ID and Identifiers ---
            $table->id();
            $table->uuid()->unique()->comment('Bot\'s UUID for secure external access');

            // --- Basic Information ---
            $table->string('name', 120)->comment('The name of the Bot');
            $table->string('logo')->nullable();
            $table->text('description')->nullable()->comment('Description of the Bot\'s function and purpose');

            // --- Polymorphic relationship for the Owner ---
            // This command will create 2 columns:
            // 1. `owner_id` (unsignedBigInteger): ID of the User or Team
            // 2. `owner_type` (string): Class name, e.g., Models\User or Models\Team
            $table->morphs('owner');

            // --- Relationship with the AI Model ---
            $table->foreignId('model_ai_id')
                ->constrained('model_ai')
                ->cascadeOnDelete()
                ->comment('The AI Model this Bot uses');

            // --- Core Bot Configuration ---
            $table->text('system_prompt')->comment('The system prompt that shapes the Bot\'s behavior');
            $table->text('greeting_message')->nullable()->comment('The greeting message the Bot sends when a conversation starts');
            $table->json('starter_messages')->nullable()->comment('The conversation starter message the Bot sends to start a conversation');
            $table->text('closing_message')->nullable()->comment('The closing message the Bot sends when a conversation ends');
            $table->json('parameters')->nullable()->comment('Model parameters like temperature, max_tokens, etc.');
            $table->enum('tool_calling_mode', ['auto', 'none', 'required'])
                ->default('none')
                ->comment('Tool calling mode');

            $table->string('visibility', 20)->default('private'); // public, private
            $table->string('bot_type', 20)->default('personal'); // personal, organization
            $table->boolean('is_shareable')->default(false)->comment('Whether the bot can be shared with other users');

            // --- Status and Additional Data ---
            $table->enum('status', ['draft', 'review', 'active', 'paused', 'banned'])
                ->default('draft')
                ->comment('The operational status of the Bot');
            $table->json('metadata')->nullable()->comment('Additional data in JSON format');

            // --- Timestamps ---
            $table->timestamps();
            $table->softDeletes();

            // --- Indexes for query optimization ---
            // Index for the polymorphic relationship
            $table->index(['owner_id', 'owner_type']);
            // Index for other common queries
            $table->index(['status']);
        });

        Schema::create('knowledge_bases', function (Blueprint $table) {
            $table->id();
            $table->uuid()->unique();
            $table->morphs('owner');
            $table->string('name')->comment('Original file name or a title for the text source');
            $table->enum('type', ['file', 'text'])->comment('Type of the knowledge source');
            $table->string('storage_path')->nullable()->comment('Path to the file in storage');
            $table->longText('content')->nullable()->comment('Content for text-based sources');
            $table->enum('status', ['pending', 'processing', 'ready', 'error'])->default('pending');
            $table->json('metadata')->nullable()->comment('File size, mime type, number of chunks, etc.');
            $table->timestamps();

            // Indexes
            $table->index(['owner_id', 'owner_type']);
            $table->index('status');
        });

        Schema::create('bot_knowledge_bases', function (Blueprint $table) {
            $table->foreignId('bot_id')->constrained()->cascadeOnDelete();
            $table->foreignId('knowledge_base_id')->constrained()->cascadeOnDelete();
            $table->primary(['bot_id', 'knowledge_base_id']);
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bot_knowledge_bases');
        Schema::dropIfExists('knowledge_bases');
        Schema::dropIfExists('bots');
    }
};
