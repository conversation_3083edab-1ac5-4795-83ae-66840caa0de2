<?php

namespace Modules\ChatBot\Services;

use Illuminate\Support\Facades\Log;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Events\MessageUpdated;
use Modules\ChatBot\Events\MessageStatusChanged;

class ChatBroadcastService
{
    /**
     * Broadcast message status change.
     */
    public function broadcastStatusChange(
        Message $message,
        MessageStatus $previousStatus,
        MessageStatus $newStatus
    ): void {
        try {
            broadcast(new MessageStatusChanged($message, $previousStatus, $newStatus));

            Log::info('Message status change broadcasted', [
                'message_id' => $message->id,
                'conversation_id' => $message->conversation_id,
                'previous_status' => $previousStatus->value,
                'new_status' => $newStatus->value,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast status change', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Broadcast message update.
     */
    public function broadcastMessageUpdate(Message $message): void
    {
        try {
            broadcast(new MessageUpdated($message));

            Log::info('Message update broadcasted', [
                'message_id' => $message->id,
                'conversation_id' => $message->conversation_id,
                'status' => $message->status->value,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast message update', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Broadcast message completion with full data.
     */
    public function broadcastMessageCompletion(Message $message): void
    {
        try {
            // Broadcast both status change and message update
            $this->broadcastStatusChange(
                $message,
                MessageStatus::PROCESSING,
                MessageStatus::COMPLETED
            );

            $this->broadcastMessageUpdate($message);

            Log::info('Message completion broadcasted', [
                'message_id' => $message->id,
                'conversation_id' => $message->conversation_id,
                'content_length' => strlen($message->content ?? ''),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast message completion', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Broadcast message failure.
     */
    public function broadcastMessageFailure(Message $message, string $error): void
    {
        try {
            $this->broadcastStatusChange(
                $message,
                MessageStatus::PROCESSING,
                MessageStatus::FAILED
            );

            Log::info('Message failure broadcasted', [
                'message_id' => $message->id,
                'conversation_id' => $message->conversation_id,
                'error' => $error,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast message failure', [
                'message_id' => $message->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Broadcast typing indicator.
     */
    public function broadcastTypingIndicator(Conversation $conversation, bool $isTyping = true): void
    {
        try {
            broadcast(new \Modules\ChatBot\Events\TypingIndicator($conversation, $isTyping));

            Log::debug('Typing indicator broadcasted', [
                'conversation_id' => $conversation->id,
                'is_typing' => $isTyping,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast typing indicator', [
                'conversation_id' => $conversation->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if broadcasting is enabled.
     */
    public function isBroadcastingEnabled(): bool
    {
        return config('chatbot.broadcasting.enabled', true);
    }

    /**
     * Get broadcasting configuration.
     */
    public function getBroadcastingConfig(): array
    {
        return config('chatbot.broadcasting', []);
    }
}
