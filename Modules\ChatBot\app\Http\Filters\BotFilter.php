<?php

namespace Modules\ChatBot\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class BotFilter extends AbstractFilter
{
    /**
     * Define the filters available for this model.
     */
    protected function filters(): array
    {
        return [
            // Basic field filters
            'status' => 'exact',
            // Search filters
            'name' => 'like',

            // Date range filters
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],

            // AI Model relationship filters
            'model_name' => [
                'type' => 'exact',
                'relation' => 'aiModel',
                'column' => 'key'
            ],
            'model_provider' => [
                'type' => 'exact',
                'relation' => 'aiModel',
                'column' => 'provider_id'
            ],
            'is_trashed' => 'trashed'
        ];
    }
}
