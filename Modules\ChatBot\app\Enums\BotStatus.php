<?php

namespace Modules\ChatBot\Enums;

enum BotStatus: string
{
    case Draft = 'draft';
    case Review = 'review';
    case Active = 'active';
    case Paused = 'paused';
    case Banned = 'banned';

    /**
     * Get all status values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get status label for display.
     */
    public function label(): string
    {
        return match ($this) {
            self::Draft => 'Draft',
            self::Review => 'Under Review',
            self::Active => 'Active',
            self::Paused => 'Paused',
            self::Banned => 'Banned',
        };
    }

    /**
     * Get status color for UI.
     */
    public function color(): string
    {
        return match ($this) {
            self::Draft => 'gray',
            self::Review => 'yellow',
            self::Active => 'green',
            self::Paused => 'orange',
            self::Banned => 'red',
        };
    }

    /**
     * Check if status is active.
     */
    public function isActive(): bool
    {
        return $this === self::Active;
    }

    /**
     * Check if status allows editing.
     */
    public function isEditable(): bool
    {
        return in_array($this, [self::Draft, self::Review, self::Paused]);
    }

    /**
     * Get publicly visible statuses.
     */
    public static function publicStatuses(): array
    {
        return [self::Active->value];
    }
}
