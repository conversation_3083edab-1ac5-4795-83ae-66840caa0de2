<?php

namespace Modules\ChatBot\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Events\MessageReceived;
use Modules\ChatBot\Facades\AIFacade;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Message;

class ProcessChatMessage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Message $message;
    protected Message $question;
    protected Bot $bot;

    /**
     * Create a new job instance.
     */
    public function __construct(Bot $bot, Message $message, Message $question)
    {
        $this->bot = $bot;
        $this->message = $message;
        $this->question = $question;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('ProcessChatMessage started', [
                'bot_id' => $this->bot->id,
                'message_id' => $this->message->id,
                'question_id' => $this->question->id,
            ]);

            // Get conversation context
            $context = $this->getConversationContext($this->message);

            // Process with AI
            AIFacade::getAssistantPrompts($this->bot, $this->question, $this->message, $context);

            Log::info('ProcessChatMessage completed', [
                'bot_id' => $this->bot->id,
                'message_id' => $this->message->id,
            ]);

        } catch (Exception $e) {
            Log::error('ProcessChatMessage failed', [
                'bot_id' => $this->bot->id ?? 'unknown',
                'message_id' => $this->message->id ?? 'unknown',
                'question_id' => $this->question->id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Lấy 10 tin nhắn cuối cùng trong cuộc hội thoại để làm ngữ cảnh.
     */
    private function getConversationContext(Message $message): Collection
    {
        return Message::where('conversation_id', $message->conversation_id)
            ->where('id', '!=', $message->id)
            ->whereIn('role', ['user', 'assistant'])
            ->whereIn('status', [MessageStatus::COMPLETED, MessageStatus::PENDING])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->reverse();
    }

    /**
     * Xử lý khi job thất bại.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('ProcessChatMessage Job Failed', [
            'message_id' => $this->message->id,
            'question_id' => $this->question->id,
            'bot_id' => $this->bot->id,
            'error' => $exception->getMessage(),
        ]);

        // Update question message status only (do not modify user's original content)
        $this->question->update([
            'status' => MessageStatus::FAILED,
            'error_message' => 'Processing failed. Please try again.',
        ]);

        // Update assistant message
        $this->message->update([
            'status' => MessageStatus::FAILED,
            'error_message' => 'Processing failed. Please try again.',
            'completed_at' => now(),
        ]);

        broadcast(new MessageReceived($this->message));
    }
}
