<?php

namespace Modules\ChatBot\Enums;

enum ConversationStatus: string
{
    case ACTIVE = 'active';
    case COMPLETED = 'completed';
    case ARCHIVED = 'archived';

    /**
     * Get all enum values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get the label for the status.
     */
    public function label(): string
    {
        return match ($this) {
            self::ACTIVE => 'Đang hoạt động',
            self::COMPLETED => 'Đã hoàn thành',
            self::ARCHIVED => 'Đã lưu trữ',
        };
    }

    /**
     * Get the color for the status.
     */
    public function color(): string
    {
        return match ($this) {
            self::ACTIVE => 'success',
            self::COMPLETED => 'info',
            self::ARCHIVED => 'secondary',
        };
    }

    /**
     * Get the icon for the status.
     */
    public function icon(): string
    {
        return match ($this) {
            self::ACTIVE => 'fas fa-comments',
            self::COMPLETED => 'fas fa-check-circle',
            self::ARCHIVED => 'fas fa-archive',
        };
    }

    /**
     * Check if the conversation can be edited.
     */
    public function isEditable(): bool
    {
        return $this === self::ACTIVE;
    }

    /**
     * Check if the conversation can be archived.
     */
    public function canBeArchived(): bool
    {
        return in_array($this, [self::ACTIVE, self::COMPLETED]);
    }

    /**
     * Check if the conversation can be reactivated.
     */
    public function canBeReactivated(): bool
    {
        return in_array($this, [self::COMPLETED, self::ARCHIVED]);
    }
}
