<?php

namespace Modules\ChatBot\Facades;

use Illuminate\Support\Facades\Facade;
use Mo<PERSON>les\ChatBot\Models\Bot;

/**
 * AI Facade
 *
 * Provides convenient access to AI functionality through a static interface.
 * Only exposes public/user-level methods. Administrative methods should be accessed
 * through direct service injection.
 *
 * @method static string|null botGeneralPrompts()
 * @method static array generateResponse(Bot $bot, array $context, array $options = [])
 * @method static array executeToolCalls(array $toolCalls, Bot $bot)
 * @method static array prepareRequestData(Bot $bot, array $context, array $options)
 *
 * @see \Modules\ChatBot\Services\AIService
 */
class AIFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'ai.service';
    }
}
