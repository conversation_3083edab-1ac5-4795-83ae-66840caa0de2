<?php

namespace Modules\ChatBot\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\User\Models\User;

class KnowledgeBaseSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first user for seeding
        $user = User::first();

        if (!$user) {
            $this->command->warn('Skipping knowledge base seeding: No users found. Please seed User module first.');
            return;
        }

        $knowledgeBases = [
            [
                'uuid' => Str::uuid(),
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'name' => 'Company FAQ',
                'type' => 'text',
                'storage_path' => null,
                'content' => 'Q: What are your business hours?
A: We are open Monday to Friday from 9 AM to 6 PM.

Q: How can I contact customer support?
A: You can reach us via <NAME_EMAIL> or call us at +1-234-567-8900.

Q: What is your return policy?
A: We offer a 30-day return policy for all products in original condition.

Q: Do you offer international shipping?
A: Yes, we ship to most countries worldwide. Shipping costs vary by location.',
                'status' => 'ready',
                'metadata' => [
                    'category' => 'customer_support',
                    'tags' => ['faq', 'support', 'general'],
                    'version' => '1.0',
                    'word_count' => 85,
                    'chunk_count' => 4,
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'name' => 'Product Documentation',
                'type' => 'text',
                'storage_path' => null,
                'content' => 'Product Features:
- Advanced AI-powered chatbot
- Multi-language support
- Real-time analytics
- Custom integrations
- 24/7 availability

Installation Guide:
1. Download the software package
2. Run the installer
3. Configure your settings
4. Test the connection
5. Go live

Troubleshooting:
- Check internet connection
- Verify API credentials
- Review error logs
- Contact support if needed',
                'status' => 'ready',
                'metadata' => [
                    'category' => 'documentation',
                    'tags' => ['product', 'features', 'installation', 'troubleshooting'],
                    'version' => '1.0',
                    'word_count' => 65,
                    'chunk_count' => 3,
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'name' => 'API Reference',
                'type' => 'text',
                'storage_path' => null,
                'content' => 'API Endpoints:

GET /api/v1/bots
- List all bots
- Parameters: page, limit, search
- Response: JSON array of bot objects

POST /api/v1/bots
- Create a new bot
- Body: name, description, system_prompt
- Response: Created bot object

GET /api/v1/conversations/{id}
- Get conversation details
- Parameters: include (messages, bot)
- Response: Conversation object

POST /api/v1/conversations/{id}/messages
- Send a message
- Body: content, content_type
- Response: Message object

Authentication:
- Use Bearer token in Authorization header
- Token format: Bearer {your_api_token}
- Tokens expire after 24 hours',
                'status' => 'ready',
                'metadata' => [
                    'category' => 'technical',
                    'tags' => ['api', 'reference', 'endpoints', 'authentication'],
                    'version' => '1.0',
                    'word_count' => 95,
                    'chunk_count' => 5,
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'name' => 'Training Data Sample',
                'type' => 'file',
                'storage_path' => 'knowledge_bases/training_data_sample.pdf',
                'content' => null,
                'status' => 'processing',
                'metadata' => [
                    'category' => 'training',
                    'tags' => ['training', 'sample', 'pdf'],
                    'version' => '1.0',
                    'file_size' => 2048576, // 2MB
                    'mime_type' => 'application/pdf',
                    'original_name' => 'training_data_sample.pdf',
                ],
            ],
        ];

        foreach ($knowledgeBases as $kbData) {
            $kb = KnowledgeBase::create($kbData);
            
            // Attach to some bots
            $bots = Bot::take(2)->get();
            foreach ($bots as $bot) {
                $bot->knowledgeBases()->attach($kb->id);
            }
        }

        $this->command->info('Successfully seeded ' . count($knowledgeBases) . ' knowledge bases.');
    }
}
