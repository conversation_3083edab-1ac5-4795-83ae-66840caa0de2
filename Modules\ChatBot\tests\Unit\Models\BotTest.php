<?php

namespace Modules\ChatBot\Tests\Unit\Models;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShare;
use Modules\ChatBot\Enums\BotType;
use Modules\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Enums\BotVisibility;
use Modules\ModelAI\Models\ModelAI;
use Modules\User\Models\User;

class BotTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test data
        $this->user = User::factory()->create();
        $this->aiModel = ModelAI::factory()->create([
            'status' => 'active',
            'function_calling' => true,
        ]);
    }

    /** @test */
    public function it_generates_uuid_on_creation()
    {
        $bot = Bot::create([
            'name' => 'Test Bot',
            'description' => 'A test bot',
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'ai_model_id' => $this->aiModel->id,
            'system_prompt' => 'You are a helpful assistant.',
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'visibility' => BotVisibility::PRIVATE,
        ]);

        $this->assertNotNull($bot->uuid);
        $this->assertTrue(\Illuminate\Support\Str::isUuid($bot->uuid));
    }

    /** @test */
    public function it_uses_uuid_as_route_key()
    {
        $bot = Bot::factory()->create();
        
        $this->assertEquals('uuid', $bot->getRouteKeyName());
    }

    /** @test */
    public function it_belongs_to_ai_model()
    {
        $bot = Bot::factory()->create([
            'ai_model_id' => $this->aiModel->id,
        ]);

        $this->assertInstanceOf(ModelAI::class, $bot->aiModel);
        $this->assertEquals($this->aiModel->id, $bot->aiModel->id);
    }

    /** @test */
    public function it_has_owner_relationship()
    {
        $bot = Bot::factory()->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
        ]);

        $this->assertInstanceOf(User::class, $bot->owner);
        $this->assertEquals($this->user->id, $bot->owner->id);
    }

    /** @test */
    public function it_can_check_if_personal_bot()
    {
        $personalBot = Bot::factory()->create([
            'bot_type' => BotType::PERSONAL,
        ]);

        $orgBot = Bot::factory()->create([
            'bot_type' => BotType::ORGANIZATION,
        ]);

        $this->assertTrue($personalBot->isPersonal());
        $this->assertFalse($orgBot->isPersonal());
        $this->assertFalse($personalBot->isOrganization());
        $this->assertTrue($orgBot->isOrganization());
    }

    /** @test */
    public function it_can_check_if_active()
    {
        $activeBot = Bot::factory()->create([
            'status' => BotStatus::Active,
        ]);

        $draftBot = Bot::factory()->create([
            'status' => BotStatus::Draft,
        ]);

        $this->assertTrue($activeBot->isActive());
        $this->assertFalse($draftBot->isActive());
    }

    /** @test */
    public function it_can_check_if_public()
    {
        $publicBot = Bot::factory()->create([
            'visibility' => BotVisibility::PUBLIC,
        ]);

        $privateBot = Bot::factory()->create([
            'visibility' => BotVisibility::PRIVATE,
        ]);

        $this->assertTrue($publicBot->isPublic());
        $this->assertFalse($privateBot->isPublic());
    }

    /** @test */
    public function only_personal_active_shareable_bots_can_be_shared()
    {
        // Personal, active, shareable bot - can be shared
        $shareableBot = Bot::factory()->create([
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'is_shareable' => true,
        ]);

        // Organization bot - cannot be shared
        $orgBot = Bot::factory()->create([
            'bot_type' => BotType::ORGANIZATION,
            'status' => BotStatus::Active,
            'is_shareable' => true,
        ]);

        // Inactive bot - cannot be shared
        $inactiveBot = Bot::factory()->create([
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Draft,
            'is_shareable' => true,
        ]);

        // Non-shareable bot - cannot be shared
        $nonShareableBot = Bot::factory()->create([
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'is_shareable' => false,
        ]);

        $this->assertTrue($shareableBot->canBeShared());
        $this->assertFalse($orgBot->canBeShared());
        $this->assertFalse($inactiveBot->canBeShared());
        $this->assertFalse($nonShareableBot->canBeShared());
    }

    /** @test */
    public function it_can_share_with_user()
    {
        $bot = Bot::factory()->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'is_shareable' => true,
        ]);

        $targetUser = User::factory()->create();
        
        $share = $bot->shareWithUser($targetUser->id);

        $this->assertInstanceOf(BotShare::class, $share);
        $this->assertEquals($bot->id, $share->bot_id);
        $this->assertEquals($targetUser->id, $share->user_id);
        $this->assertEquals($targetUser->id, $share->shareable_id);
        $this->assertEquals(get_class($targetUser), $share->shareable_type);
    }

    /** @test */
    public function it_can_check_user_access()
    {
        $bot = Bot::factory()->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'visibility' => BotVisibility::PRIVATE,
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'is_shareable' => true,
        ]);

        $otherUser = User::factory()->create();

        // Owner can access
        $this->assertTrue($bot->canBeAccessedBy($this->user->id));

        // Other user cannot access private bot
        $this->assertFalse($bot->canBeAccessedBy($otherUser->id));

        // Share with other user
        $share = $bot->shareWith($otherUser->id);
        $this->assertNotNull($share, 'Bot should be shareable');

        // Now other user can access
        $this->assertTrue($bot->canBeAccessedBy($otherUser->id));
    }

    /** @test */
    public function it_can_get_user_permissions()
    {
        $bot = Bot::factory()->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'is_shareable' => true,
        ]);

        $otherUser = User::factory()->create();

        // Owner has admin permission
        $this->assertEquals('admin', $bot->getUserPermission($this->user->id));

        // Other user has no permission
        $this->assertNull($bot->getUserPermission($otherUser->id));

        // Share with other user
        $share = $bot->shareWith($otherUser->id);
        $this->assertNotNull($share, 'Bot should be shareable');

        // Shared user has read permission
        $this->assertEquals('read', $bot->getUserPermission($otherUser->id));
    }

    /** @test */
    public function it_can_get_sharing_statistics()
    {
        $bot = Bot::factory()->create([
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'is_shareable' => true,
        ]);

        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        // Share with users
        $bot->shareWith($user1->id);
        $bot->shareWith($user2->id);

        $stats = $bot->getSharingStats();

        $this->assertEquals(2, $stats['total_shares']);
        $this->assertEquals(2, $stats['shared_users_count']);
        $this->assertTrue($stats['is_shareable']);
        $this->assertTrue($stats['sharing_enabled']);
        $this->assertEquals(BotType::PERSONAL->value, $stats['bot_type']);
    }
}
