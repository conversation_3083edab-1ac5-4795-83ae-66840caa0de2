<?php

namespace Modules\ChatBot\Tests\Feature;

use <PERSON><PERSON>les\ChatBot\Models\Conversation;
use Mo<PERSON>les\ChatBot\Models\Message;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\ChatBot\Enums\MessageRole;

class ConversationApiTest extends ChatBotTestCase
{
    /** @test */
    public function it_can_list_user_conversations()
    {
        $this->authenticateUser();

        // Create additional conversations
        $this->createTestConversation(['title' => 'Conversation 1']);
        $this->createTestConversation(['title' => 'Conversation 2']);

        $response = $this->getJson('/api/v1/conversations?' . http_build_query([
            'user_id' => $this->user->id,
            'user_type' => User::class,
        ]), $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'title',
                    'bot_id',
                    'user_id',
                    'user_type',
                    'status',
                    'message_count',
                    'created_at',
                    'bot',
                ],
            ],
            'meta' => [
                'current_page',
                'per_page',
                'total',
            ],
        ]);

        $this->assertCount(3, $response->json('data')); // Including the one from setUp
    }

    /** @test */
    public function it_can_filter_conversations_by_status()
    {
        $this->authenticateUser();

        $this->createTestConversation(['status' => ConversationStatus::ACTIVE]);
        $this->createTestConversation(['status' => ConversationStatus::COMPLETED]);
        $this->createTestConversation(['status' => ConversationStatus::ARCHIVED]);

        $response = $this->getJson('/api/v1/conversations?' . http_build_query([
            'user_id' => $this->user->id,
            'user_type' => User::class,
            'status' => 'active',
        ]), $this->getApiHeaders());

        $this->assertApiResponse($response);
        
        $conversations = $response->json('data');
        $this->assertCount(2, $conversations); // Including the one from setUp
        
        foreach ($conversations as $conversation) {
            $this->assertEquals('active', $conversation['status']);
        }
    }

    /** @test */
    public function it_can_create_a_conversation()
    {
        $this->authenticateUser();

        $conversationData = [
            'title' => 'New Support Chat',
            'bot_id' => $this->bot->id,
            'user_id' => $this->user->id,
            'user_type' => User::class,
        ];

        $response = $this->postJson('/api/v1/conversations', $conversationData, $this->getApiHeaders());

        $this->assertApiResponse($response, 201);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'title',
                'bot_id',
                'user_id',
                'user_type',
                'status',
                'bot',
                'user',
            ],
        ]);

        $this->assertDatabaseHasConversation([
            'title' => 'New Support Chat',
            'bot_id' => $this->bot->id,
            'user_id' => $this->user->id,
            'status' => 'active',
        ]);
    }

    /** @test */
    public function it_validates_conversation_creation_data()
    {
        $this->authenticateUser();

        // Missing required fields
        $response = $this->postJson('/api/v1/conversations', [], $this->getApiHeaders());

        $this->assertValidationError($response, 'bot_id');
        $this->assertValidationError($response, 'user_id');
        $this->assertValidationError($response, 'user_type');
    }

    /** @test */
    public function it_cannot_create_conversation_with_inactive_bot()
    {
        $this->authenticateUser();

        $inactiveBot = Bot::factory()->create(['status' => 'draft']);

        $conversationData = [
            'title' => 'Test Chat',
            'bot_id' => $inactiveBot->id,
            'user_id' => $this->user->id,
            'user_type' => User::class,
        ];

        $response = $this->postJson('/api/v1/conversations', $conversationData, $this->getApiHeaders());

        $this->assertApiResponse($response, 500, false);
        $response->assertJsonFragment(['message' => 'Bot is not active and cannot be used for conversations.']);
    }

    /** @test */
    public function it_can_show_conversation_details()
    {
        $this->authenticateUser();

        // Add some messages to the conversation
        $this->createTestMessage(['role' => MessageRole::USER, 'content' => 'Hello']);
        $this->createTestMessage(['role' => MessageRole::ASSISTANT, 'content' => 'Hi there!']);

        $response = $this->getJson("/api/v1/conversations/{$this->conversation->id}", $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'id',
                'title',
                'bot',
                'user',
                'messages' => [
                    '*' => [
                        'id',
                        'role',
                        'content',
                        'created_at',
                    ],
                ],
            ],
        ]);

        $this->assertEquals($this->conversation->id, $response->json('data.id'));
    }

    /** @test */
    public function it_can_update_conversation()
    {
        $this->authenticateUser();

        $updateData = [
            'title' => 'Updated Conversation Title',
        ];

        $response = $this->putJson("/api/v1/conversations/{$this->conversation->id}", $updateData, $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonFragment(['title' => 'Updated Conversation Title']);

        $this->assertDatabaseHasConversation([
            'id' => $this->conversation->id,
            'title' => 'Updated Conversation Title',
        ]);
    }

    /** @test */
    public function it_can_complete_conversation()
    {
        $this->authenticateUser();

        $response = $this->patchJson("/api/v1/conversations/{$this->conversation->id}/complete", [], $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonFragment(['status' => 'completed']);

        $this->assertDatabaseHasConversation([
            'id' => $this->conversation->id,
            'status' => 'completed',
        ]);
    }

    /** @test */
    public function it_can_archive_conversation()
    {
        $this->authenticateUser();

        $response = $this->patchJson("/api/v1/conversations/{$this->conversation->id}/archive", [], $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonFragment(['status' => 'archived']);

        $this->assertDatabaseHasConversation([
            'id' => $this->conversation->id,
            'status' => 'archived',
        ]);
    }

    /** @test */
    public function it_can_reactivate_completed_conversation()
    {
        $this->authenticateUser();

        // First complete the conversation
        $this->conversation->update(['status' => ConversationStatus::COMPLETED]);

        $response = $this->patchJson("/api/v1/conversations/{$this->conversation->id}/reactivate", [], $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonFragment(['status' => 'active']);

        $this->assertDatabaseHasConversation([
            'id' => $this->conversation->id,
            'status' => 'active',
        ]);
    }

    /** @test */
    public function it_cannot_reactivate_active_conversation()
    {
        $this->authenticateUser();

        $response = $this->patchJson("/api/v1/conversations/{$this->conversation->id}/reactivate", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 400, false);
        $response->assertJsonFragment(['message' => 'Conversation cannot be reactivated in its current status.']);
    }

    /** @test */
    public function it_can_delete_conversation()
    {
        $this->authenticateUser();

        // Add some messages
        $this->createTestMessage(['role' => MessageRole::USER]);
        $this->createTestMessage(['role' => MessageRole::ASSISTANT]);

        $conversationId = $this->conversation->id;

        $response = $this->deleteJson("/api/v1/conversations/{$conversationId}", [], $this->getApiHeaders());

        $this->assertApiResponse($response);

        $this->assertDatabaseMissing('conversations', ['id' => $conversationId]);
        $this->assertDatabaseMissing('messages', ['conversation_id' => $conversationId]);
    }

    /** @test */
    public function it_can_get_conversation_statistics()
    {
        $this->authenticateUser();

        // Create messages with different roles and tokens
        $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'User message 1',
        ]);
        $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'User message 2',
        ]);
        $this->createTestMessage([
            'role' => MessageRole::ASSISTANT,
            'content' => 'Assistant response',
            'total_tokens' => 100,
            'cost' => 0.01,
            'response_time_ms' => 1500,
        ]);

        $response = $this->getJson("/api/v1/conversations/{$this->conversation->id}/stats", $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_messages',
                'user_messages',
                'assistant_messages',
                'total_tokens',
                'total_cost',
                'average_response_time',
                'first_message_at',
                'last_message_at',
            ],
        ]);

        $stats = $response->json('data');
        $this->assertEquals(3, $stats['total_messages']);
        $this->assertEquals(2, $stats['user_messages']);
        $this->assertEquals(1, $stats['assistant_messages']);
        $this->assertEquals(100, $stats['total_tokens']);
        $this->assertEquals(0.01, $stats['total_cost']);
    }

    /** @test */
    public function it_can_search_conversations()
    {
        $this->authenticateUser();

        // Create conversations with searchable content
        $conv1 = $this->createTestConversation(['title' => 'Customer Support Issue']);
        $conv2 = $this->createTestConversation(['title' => 'Technical Question']);

        // Add message with searchable content
        Message::factory()->create([
            'conversation_id' => $conv2->id,
            'content' => 'This is about customer support',
        ]);

        $response = $this->getJson('/api/v1/conversations/search?' . http_build_query([
            'query' => 'customer support',
            'user_id' => $this->user->id,
            'user_type' => User::class,
        ]), $this->getApiHeaders());

        $this->assertApiResponse($response);
        
        $conversations = $response->json('data');
        $this->assertGreaterThanOrEqual(2, count($conversations));
    }

    /** @test */
    public function it_can_get_recent_conversations()
    {
        $this->authenticateUser();

        // Create conversations with different timestamps
        $this->createTestConversation([
            'title' => 'Recent Chat 1',
            'last_message_at' => now()->subHour(),
        ]);
        $this->createTestConversation([
            'title' => 'Recent Chat 2',
            'last_message_at' => now()->subMinutes(30),
        ]);

        $response = $this->getJson('/api/v1/conversations/recent?' . http_build_query([
            'user_id' => $this->user->id,
            'user_type' => User::class,
            'limit' => 5,
        ]), $this->getApiHeaders());

        $this->assertApiResponse($response);
        
        $conversations = $response->json('data');
        $this->assertLessThanOrEqual(5, count($conversations));
        
        // Should be ordered by last_message_at desc
        if (count($conversations) > 1) {
            $firstTime = strtotime($conversations[0]['last_message_at']);
            $secondTime = strtotime($conversations[1]['last_message_at']);
            $this->assertGreaterThanOrEqual($secondTime, $firstTime);
        }
    }

    /** @test */
    public function it_can_get_active_conversations_count()
    {
        $this->authenticateUser();

        // Create additional active conversations
        $this->createTestConversation(['status' => ConversationStatus::ACTIVE]);
        $this->createTestConversation(['status' => ConversationStatus::ACTIVE]);
        $this->createTestConversation(['status' => ConversationStatus::COMPLETED]);

        $response = $this->getJson('/api/v1/conversations/active-count?' . http_build_query([
            'user_id' => $this->user->id,
            'user_type' => User::class,
        ]), $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonStructure([
            'success',
            'data' => ['count'],
        ]);

        $this->assertEquals(3, $response->json('data.count')); // Including the one from setUp
    }
}
