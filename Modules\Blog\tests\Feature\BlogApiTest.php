<?php

namespace Modules\Blog\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use PHPUnit\Framework\Attributes\Test;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Models\BlogPostTranslation;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Models\BlogCategoryTranslation;
use Modules\Blog\Enums\BlogStatus;
use Modules\User\Models\User;

class BlogApiTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Blog module
        $this->artisan('migrate', ['--path' => 'Modules/Blog/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_can_get_featured_posts_via_api()
    {
        $user = User::factory()->create();
        
        $featuredPost = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'featured' => true,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);
        
        $regularPost = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'featured' => false,
            'published_at' => now()->subDays(2),
            'author_id' => $user->id,
        ]);

        // Create translations
        BlogPostTranslation::factory()->create(['post_id' => $featuredPost->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $regularPost->id, 'locale' => 'en']);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/featured');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'status',
                    'featured',
                    'author_id',
                    'published_at',
                    'author'
                ]
            ]
        ]);

        $this->assertCount(1, $response->json('data'));
        $this->assertEquals($featuredPost->id, $response->json('data.0.id'));
        $this->assertTrue($response->json('data.0.featured'));
    }

    #[Test]
    public function it_can_get_latest_posts_via_api()
    {
        $user = User::factory()->create();
        
        $post1 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);
        
        $post2 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDays(2),
            'author_id' => $user->id,
        ]);

        // Create translations
        BlogPostTranslation::factory()->create(['post_id' => $post1->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $post2->id, 'locale' => 'en']);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/latest');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'status',
                    'author_id',
                    'published_at',
                    'author'
                ]
            ]
        ]);

        $this->assertCount(2, $response->json('data'));
        // Should be ordered by published_at desc
        $this->assertEquals($post1->id, $response->json('data.0.id'));
        $this->assertEquals($post2->id, $response->json('data.1.id'));
    }

    #[Test]
    public function it_can_get_top_posts_via_api()
    {
        $user = User::factory()->create();
        
        $post1 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);
        
        $post2 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDays(2),
            'author_id' => $user->id,
        ]);

        // Create translations
        BlogPostTranslation::factory()->create(['post_id' => $post1->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $post2->id, 'locale' => 'en']);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/top');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'status',
                    'author_id',
                    'published_at',
                    'author'
                ]
            ]
        ]);

        $this->assertCount(2, $response->json('data'));
    }

    #[Test]
    public function it_filters_posts_by_locale()
    {
        $user = User::factory()->create();
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active]);

        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        // Attach post to category
        $post->categories()->attach($category->id);

        // Only create English translation
        BlogPostTranslation::factory()->create(['post_id' => $post->id, 'locale' => 'en']);

        // Request Vietnamese posts (should be empty)
        App::setLocale('vi');
        $response = $this->getJson("/api/v1/blog/posts?category_id={$category->id}");
        $response->assertStatus(200);
        $this->assertCount(0, $response->json('data'));

        // Request English posts (should have 1)
        App::setLocale('en');
        $response = $this->getJson("/api/v1/blog/posts?category_id={$category->id}");
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
    }

    #[Test]
    public function it_validates_category_id_parameter()
    {
        App::setLocale('en');

        // Test with invalid category_id
        $response = $this->getJson('/api/v1/blog/posts?category_id=invalid');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['category_id']);

        // Test with non-existent category_id
        $response = $this->getJson('/api/v1/blog/posts?category_id=99999');
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['category_id']);
    }

    #[Test]
    public function it_filters_categories_by_locale()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);

        // Only create English translation
        BlogCategoryTranslation::factory()->create(['category_id' => $category->id, 'locale' => 'en']);

        // Request Vietnamese categories (should be empty)
        App::setLocale('vi');
        $response = $this->getJson('/api/v1/blog/categories');
        $response->assertStatus(200);
        $this->assertCount(0, $response->json('data'));

        // Request English categories (should have 1)
        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/categories');
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
    }

    #[Test]
    public function it_paginates_posts_correctly()
    {
        $user = User::factory()->create();
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active]);

        // Create 15 posts
        for ($i = 0; $i < 15; $i++) {
            $post = BlogPost::factory()->create([
                'status' => BlogStatus::Published,
                'published_at' => now()->subDays($i),
                'author_id' => $user->id,
            ]);

            // Attach post to category
            $post->categories()->attach($category->id);

            BlogPostTranslation::factory()->create(['post_id' => $post->id, 'locale' => 'en']);
        }

        App::setLocale('en');
        $response = $this->getJson("/api/v1/blog/posts?category_id={$category->id}&per_page=10");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'title',
                    'slug',
                    'summary',
                    'image',
                    'published_at',
                    'author'
                ]
            ],
            'total'
        ]);

        $this->assertCount(10, $response->json('data'));
        $this->assertEquals(15, $response->json('total'));
    }

    #[Test]
    public function it_handles_category_hierarchy_in_api()
    {
        // Create parent category
        $parent = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);
        
        // Create child categories
        $child1 = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => $parent->id]);
        $child2 = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => $parent->id]);

        // Create translations
        BlogCategoryTranslation::factory()->create(['category_id' => $parent->id, 'locale' => 'en', 'name' => 'Parent']);
        BlogCategoryTranslation::factory()->create(['category_id' => $child1->id, 'locale' => 'en', 'name' => 'Child 1']);
        BlogCategoryTranslation::factory()->create(['category_id' => $child2->id, 'locale' => 'en', 'name' => 'Child 2']);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/categories');

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data')); // Only one root category
        
        $rootCategory = $response->json('data.0');
        $this->assertEquals($parent->id, $rootCategory['id']);
        $this->assertCount(2, $rootCategory['children']); // Two child categories
    }

    #[Test]
    public function it_handles_post_category_relationships_in_api()
    {
        $user = User::factory()->create();
        $category1 = BlogCategory::factory()->create(['status' => BlogStatus::Active]);
        $category2 = BlogCategory::factory()->create(['status' => BlogStatus::Active]);
        
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        // Attach post to multiple categories
        $post->categories()->attach([$category1->id, $category2->id]);

        // Create translations
        BlogCategoryTranslation::factory()->create(['category_id' => $category1->id, 'locale' => 'en', 'slug' => 'tech']);
        BlogCategoryTranslation::factory()->create(['category_id' => $category2->id, 'locale' => 'en', 'slug' => 'business']);
        BlogPostTranslation::factory()->create(['post_id' => $post->id, 'locale' => 'en']);

        App::setLocale('en');
        
        // Get category with posts - first category
        $response = $this->getJson('/api/v1/blog/categories/tech');
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data.posts'));
        $this->assertEquals($post->id, $response->json('data.posts.0.id'));

        // Get category with posts - second category
        $response = $this->getJson('/api/v1/blog/categories/business');
        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data.posts'));
        $this->assertEquals($post->id, $response->json('data.posts.0.id'));
    }

    #[Test]
    public function it_returns_consistent_error_responses()
    {
        App::setLocale('en');
        
        // Test non-existent post
        $response = $this->getJson('/api/v1/blog/posts/non-existent');
        $response->assertStatus(404);
        $response->assertJsonStructure(['success', 'message']);
        $this->assertFalse($response->json('success'));

        // Test non-existent category
        $response = $this->getJson('/api/v1/blog/categories/non-existent');
        $response->assertStatus(404);
        $response->assertJsonStructure(['success', 'message']);
        $this->assertFalse($response->json('success'));
    }

    #[Test]
    public function it_returns_consistent_success_responses()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'test-post'
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/test-post');

        $response->assertStatus(200);
        $response->assertJsonStructure(['success', 'message', 'data']);
        $this->assertTrue($response->json('success'));
        $this->assertIsString($response->json('message'));
        $this->assertIsArray($response->json('data'));
    }
}
