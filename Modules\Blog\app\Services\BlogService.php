<?php

namespace Modules\Blog\Services;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Cache;
use Modules\Blog\Http\Filters\BlogPostFilter;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Models\BlogPost;

class BlogService
{
    private const CACHE_TAG = 'blog';

    /*
     * :::::::::::::::::::::::::::::::::::::
     * :::::::::::::::: BLOG CATEGORIES :::::::::::
     * :::::::::::::::::::::::::::::::::::::
     *
     */

    /**
     * Get the root categories of the blog.
     *
     * @param string $locale
     * @return Collection
     */
    public function getRootCategories(string $locale): Collection
    {
        if (!enabledCache()) {
            return BlogCategory::getRootCategories($locale);
        }
        return Cache::tags([self::CACHE_TAG])->remember(
            key: 'categories.root.' . $locale,
            ttl: cacheTTL(),
            callback: fn() => BlogCategory::getRootCategories($locale)
        );
    }

    /**
     * Get the active category hierarchy for the given locale.
     * This method builds a tree structure of categories.
     *
     * @param string $locale
     * @return Collection
     */
    public function getActiveCategoryHierarchy(string $locale): Collection
    {
        if (!enabledCache()) {
            return $this->buildActiveCategoryHierarchy($locale);
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: 'categories.active_hierarchy.' . $locale,
            ttl: cacheTTL(),
            callback: fn() => $this->buildActiveCategoryHierarchy($locale)
        );
    }

    /**
     * The core logic for building the category tree.
     * This method is kept private to the class.
     *
     * @param string $locale
     * @return Collection
     */
    protected function buildActiveCategoryHierarchy(string $locale): Collection
    {
        $allCategories = BlogCategory::getAllCategories($locale);

        if ($allCategories->isEmpty()) {
            return collect();
        }

        $categoriesById = $allCategories->keyBy('id')->each(function (BlogCategory $category) {
            $category->setRelation('children', collect());
        });

        $tree = collect();

        foreach ($allCategories as $category) {
            if ($category->parent_id && $categoriesById->has($category->parent_id)) {
                $categoriesById->get($category->parent_id)->children->push($category);
            } else {
                $tree->push($category);
            }
        }

        return $tree;
    }

    /**
     * Get all blog categories with translations.
     *
     * @param string $slug
     * @param string $locale
     * @return BlogCategory|null
     */
    public function getCategoryBySlug(string $slug, string $locale): ?BlogCategory
    {
        if (!enabledCache()) {
            return BlogCategory::getCategoryBySlug($slug, $locale);
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: 'category.slug.' . $slug . '.' . $locale,
            ttl: cacheTTL(),
            callback: fn() => BlogCategory::getCategoryBySlug($slug, $locale)
        );
    }

    /*
     *  :::::::::::::::::::::::::::::::::::
     *  :::::::::::::::: BLOG POSTS :::::::::::
     *  :::::::::::::::::::::::::::::::::::
     *
     */


    /**
     * Get all blog posts with pagination and filtering.
     *
     * @param int $categoryId
     * @param string $locale
     * @return LengthAwarePaginator
     */
    public function getPostsByCategoryId(int $categoryId, string $locale): LengthAwarePaginator
    {
        if (!enabledCache()) {
            return BlogPost::getPostsByCategoryId($categoryId, $locale);
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: 'posts.category.' . $categoryId. '.' . request()->get('page', 1)  . '.' . $locale,
            ttl: cacheTTL(),
            callback: fn() => BlogPost::getPostsByCategoryId($categoryId, $locale)
        );
    }

    /**
     * Get a blog post by its slug and locale.
     *
     * @param string $slug
     * @param string $locale
     * @return BlogPost|null
     */
    public function getPostBySlug(string $slug, string $locale): ?BlogPost
    {
        if (!enabledCache()) {
            return BlogPost::getPostBySlug($slug, $locale);
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: 'post.slug.' . $slug . '.' . $locale,
            ttl: cacheTTL(),
            callback: fn() => BlogPost::getPostBySlug($slug, $locale)
        );
    }

    /**
     * Get paginated blog posts with optional filters.
     *
     * @param string $locale
     * @param int|null $categoryId
     * @return Collection
     */
    public function getFeaturePosts(string $locale, int $categoryId = null): Collection
    {
        if (!enabledCache()) {
            return BlogPost::getPostsByFeature($locale, $categoryId);
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: "posts.featured.".request()->get('limit', 10).".{$categoryId}.{$locale}",
            ttl: cacheTTL(),
            callback: fn() => BlogPost::getPostsByFeature($locale, $categoryId)
        );
    }

    /**
     * Get the top posts based on the published date.
     *
     * @param string $locale
     * @param int|null $categoryId
     * @return Collection
     */
    public function getTopPosts(string $locale, int $categoryId = null): Collection
    {
        if (!enabledCache()) {
            return BlogPost::getPostsByTopViews($locale, $categoryId);
        }
        return Cache::tags([self::CACHE_TAG])->remember(
            key: "posts.top.".request()->get('limit', 10).".{$categoryId}.{$locale}",
            ttl: cacheTTL(),
            callback: fn() => BlogPost::getPostsByTopViews($locale, $categoryId)
        );
    }

    /**
     * Get the latest posts based on the published date.
     *
     * @param string $locale
     * @param int|null $categoryId
     * @return Collection
     */
    public function getLatestPosts(string $locale, int $categoryId = null): Collection
    {

        if (!enabledCache()) {
            return BlogPost::getLatestPosts($locale, $categoryId);
        }

        return Cache::tags([self::CACHE_TAG])->remember(
            key: "posts.latest.".request()->get('limit', 10).".{$categoryId}.{$locale}",
            ttl: cacheTTL(),
            callback: fn() => BlogPost::getLatestPosts($locale, $categoryId)
        );
    }


}
