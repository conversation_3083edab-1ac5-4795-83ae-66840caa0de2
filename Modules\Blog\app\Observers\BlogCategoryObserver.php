<?php

namespace Modules\Blog\Observers;

use Modules\Blog\Models\BlogCategory;
use Illuminate\Support\Facades\Cache;

class BlogCategoryObserver
{
    /**
     * Handle the BlogCategory "created" event.
     */
    public function created(BlogCategory $blogCategory): void
    {
        $this->clearCache();
    }

    /**
     * Handle the BlogCategory "updated" event.
     */
    public function updated(BlogCategory $blogCategory): void
    {
        $this->clearCache();
    }

    /**
     * Handle the BlogCategory "deleted" event.
     */
    public function deleted(BlogCategory $blogCategory): void
    {
        $this->clearCache();
    }

    /**
     * Handle the BlogCategory "restored" event.
     */
    public function restored(BlogCategory $blogCategory): void
    {
        $this->clearCache();
    }

    /**
     * Handle the BlogCategory "force deleted" event.
     */
    public function forceDeleted(BlogCategory $blogCategory): void
    {
        $this->clearCache();
    }

    /**
     * Clear blog-related cache
     */
    private function clearCache(): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                Cache::tags(['blog'])->flush();
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
        }
    }
}
