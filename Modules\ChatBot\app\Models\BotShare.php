<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;
use Modules\User\Models\User;

class BotShare extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'bot_id',
        'user_id',
        'shareable_id',
        'shareable_type',
        'status',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the bot that is shared.
     */
    public function bot(): BelongsTo
    {
        return $this->belongsTo(Bot::class);
    }

    /**
     * Get the user who received the bot share.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the shareable entity (polymorphic relationship).
     */
    public function shareable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to filter by user.
     */
    public function scopeForUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope a query to filter by bot.
     */
    public function scopeForBot(Builder $query, int $botId): Builder
    {
        return $query->where('bot_id', $botId);
    }

    /**
     * Scope a query to filter by shareable type.
     */
    public function scopeForShareableType(Builder $query, string $type): Builder
    {
        return $query->where('shareable_type', $type);
    }

    /**
     * Check if the share exists for a specific bot, user and shareable entity.
     */
    public static function exists(int $botId, int $userId, int $shareableId, string $shareableType): bool
    {
        return static::where('bot_id', $botId)
                    ->where('user_id', $userId)
                    ->where('shareable_id', $shareableId)
                    ->where('shareable_type', $shareableType)
                    ->exists();
    }

    /**
     * Create or get existing share.
     */
    public static function createOrGet(int $botId, int $userId, int $shareableId, string $shareableType): self
    {
        return static::firstOrCreate([
            'bot_id' => $botId,
            'user_id' => $userId,
            'shareable_id' => $shareableId,
            'shareable_type' => $shareableType,
        ]);
    }
}
