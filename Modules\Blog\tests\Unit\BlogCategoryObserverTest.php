<?php

namespace Modules\Blog\Tests\Unit;

use Tests\TestCase;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Enums\BlogStatus;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;

class BlogCategoryObserverTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_clears_cache_when_category_created()
    {
        Cache::shouldReceive('tags')
            ->with(['blog'])
            ->andReturnSelf()
            ->atLeast(1);

        Cache::shouldReceive('flush')
            ->atLeast(1);

        $category = BlogCategory::factory()->create();

        // Just verify the category was created
        $this->assertInstanceOf(BlogCategory::class, $category);
    }

    #[Test]
    public function it_clears_cache_when_category_updated()
    {
        $category = BlogCategory::factory()->create();

        Cache::shouldReceive('tags')
            ->with(['blog'])
            ->andReturnSelf()
            ->atLeast(1);

        Cache::shouldReceive('flush')
            ->atLeast(1);

        $category->update(['status' => BlogStatus::Inactive]);

        // Just verify the update worked
        $this->assertEquals(BlogStatus::Inactive, $category->status);
    }

    #[Test]
    public function it_clears_cache_when_category_deleted()
    {
        $category = BlogCategory::factory()->create();

        Cache::shouldReceive('tags')
            ->with(['blog'])
            ->andReturnSelf()
            ->atLeast(1);

        Cache::shouldReceive('flush')
            ->atLeast(1);

        $category->delete();

        // Just verify the delete worked
        $this->assertTrue($category->trashed());
    }

    #[Test]
    public function it_handles_cache_errors_gracefully()
    {
        Cache::shouldReceive('tags')
            ->with(['blog'])
            ->andThrow(new \Exception('Cache error'));

        // Should not throw exception
        $category = BlogCategory::factory()->create();

        $this->assertInstanceOf(BlogCategory::class, $category);
    }
}
