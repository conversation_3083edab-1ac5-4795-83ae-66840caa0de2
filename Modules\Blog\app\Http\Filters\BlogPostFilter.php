<?php

namespace Modules\Blog\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Modules\Blog\Enums\BlogStatus;
use Modules\Core\Abstracts\AbstractFilter;

class BlogPostFilter extends AbstractFilter
{
    /**
     * Define the filters available for this model.
     */
    protected function filters(): array
    {
        return [
            // Basic field filters
            'status' => 'exact',
            'author_id' => 'exact',
            'is_trashed' => 'exact',

            // Date range filters (following project standard pattern)
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],
            'published_from' => ['type' => 'from', 'column' => 'published_at'],
            'published_to' => ['type' => 'to', 'column' => 'published_at'],

            // Author-based filters
            'author_name' => [
                'type' => 'like',
                'relation' => 'author',
                'column' => 'name'
            ],
            'author_email' => [
                'type' => 'like',
                'relation' => 'author',
                'column' => 'email'
            ],

            // Translation-based filters
            'title' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'title'
            ],
            'slug' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'slug'
            ],
            'locale' => [
                'type' => 'exact',
                'relation' => 'translations',
                'column' => 'locale'
            ],
            'summary' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'summary'
            ],
            'content' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'content'
            ],
            'meta_title' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'meta_title'
            ],
            'meta_description' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'meta_description'
            ],
            'meta_keywords' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'meta_keywords'
            ],

            // Special filters
            'search' => 'search'
        ];
    }
}
