<?php

namespace Modules\Blog\Observers;

use Modules\Blog\Models\BlogPost;
use Illuminate\Support\Facades\Cache;

class BlogPostObserver
{
    /**
     * Handle the BlogPost "created" event.
     */
    public function created(BlogPost $blogPost): void
    {
        $this->clearCache();
    }

    /**
     * Handle the BlogPost "updated" event.
     */
    public function updated(BlogPost $blogPost): void
    {
        $this->clearCache();
    }

    /**
     * Handle the BlogPost "deleted" event.
     */
    public function deleted(BlogPost $blogPost): void
    {
        $this->clearCache();
    }

    /**
     * Handle the BlogPost "restored" event.
     */
    public function restored(BlogPost $blogPost): void
    {
        $this->clearCache();
    }

    /**
     * Handle the BlogPost "force deleted" event.
     */
    public function forceDeleted(BlogPost $blogPost): void
    {
        $this->clearCache();
    }

    /**
     * Clear blog-related cache
     */
    private function clearCache(): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                Cache::tags(['blog'])->flush();
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
        }
    }
}
