<?php

namespace Modules\ChatBot\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Event;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Jobs\ChatEmbeddingJob;
use Modules\ChatBot\Events\MessageStatusChanged;
use Modules\ChatBot\Events\MessageUpdated;
use Modules\ChatBot\Services\MessageService;
use Modules\User\Models\User;

class EmbeddingChatFlowTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private Bot $bot;
    private Conversation $conversation;
    private MessageService $messageService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create();
        $this->bot = Bot::factory()->create(['owner_id' => $this->user->id]);
        $this->conversation = Conversation::factory()->create([
            'user_id' => $this->user->id,
            'bot_id' => $this->bot->id,
        ]);
        
        $this->messageService = app(MessageService::class);
    }

    /** @test */
    public function it_can_generate_ai_response_with_embedding()
    {
        Queue::fake();
        Event::fake();

        // Create user message
        $userMessage = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::USER,
            'content' => 'What is Laravel?',
            'status' => MessageStatus::COMPLETED,
        ]);

        // Generate AI response with embedding
        $assistantMessage = $this->messageService->generateAIResponseWithEmbedding(
            $this->conversation,
            ['use_embedding' => true]
        );

        // Assert message was created
        $this->assertInstanceOf(Message::class, $assistantMessage);
        $this->assertEquals(MessageStatus::PENDING, $assistantMessage->status);
        $this->assertEquals(MessageRole::ASSISTANT, $assistantMessage->role);
        $this->assertTrue($assistantMessage->metadata['embedding_enabled'] ?? false);

        // Assert job was dispatched
        Queue::assertPushed(ChatEmbeddingJob::class, function ($job) use ($assistantMessage, $userMessage) {
            return $job->messageId === $assistantMessage->id &&
                   $job->question === $userMessage->content &&
                   $job->conversationId === $this->conversation->id &&
                   $job->botId === $this->bot->id;
        });
    }

    /** @test */
    public function it_can_handle_embedding_job_execution()
    {
        Event::fake();
        Http::fake([
            config('chatbot.python.embedding_url') => Http::response([
                'success' => true,
                'message' => 'Request received',
                'request_id' => 'test-123',
            ], 200),
        ]);

        // Create assistant message
        $message = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::PENDING,
        ]);

        // Execute job
        $job = new ChatEmbeddingJob(
            $message->id,
            'What is Laravel?',
            $this->conversation->id,
            $this->bot->id
        );

        $job->handle();

        // Assert message status updated
        $message->refresh();
        $this->assertEquals(MessageStatus::PROCESSING, $message->status);
        $this->assertNotNull($message->metadata['embedding_started_at']);

        // Assert HTTP request was made
        Http::assertSent(function ($request) {
            return $request->url() === config('chatbot.python.embedding_url') &&
                   $request['question'] === 'What is Laravel?' &&
                   $request['conversation_id'] === $this->conversation->id;
        });

        // Assert status change event was fired
        Event::assertDispatched(MessageStatusChanged::class);
    }

    /** @test */
    public function it_can_handle_webhook_from_python_service()
    {
        Event::fake();

        // Create processing message
        $message = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::PROCESSING,
        ]);

        // Mock AI response
        $this->mock(\Modules\ChatBot\Services\AIService::class, function ($mock) {
            $mock->shouldReceive('generateResponse')
                ->once()
                ->andReturn([
                    'content' => 'Laravel is a PHP framework...',
                    'usage' => ['prompt_tokens' => 100, 'completion_tokens' => 50, 'total_tokens' => 150],
                    'cost' => 0.001,
                    'response_time_ms' => 1500,
                ]);
        });

        // Simulate webhook payload
        $webhookData = [
            'message_id' => $message->id,
            'conversation_id' => $this->conversation->id,
            'bot_id' => $this->bot->id,
            'context_data' => [
                ['content' => 'Laravel documentation excerpt...'],
                ['content' => 'Laravel tutorial content...'],
            ],
            'relevant_documents' => [
                [
                    'title' => 'Laravel Documentation',
                    'content' => 'Laravel is a web application framework...',
                    'score' => 0.95,
                ],
            ],
            'metadata' => [
                'processing_time' => 2.5,
                'similarity_scores' => [0.95, 0.87],
            ],
        ];

        // Send webhook request
        $response = $this->postJson(
            route('webhooks.embedding.result'),
            $webhookData,
            ['X-Webhook-Signature' => $this->generateWebhookSignature($webhookData)]
        );

        // Assert response
        $response->assertOk()
                ->assertJson(['success' => true]);

        // Assert message was updated
        $message->refresh();
        $this->assertEquals(MessageStatus::COMPLETED, $message->status);
        $this->assertEquals('Laravel is a PHP framework...', $message->content);
        $this->assertNotNull($message->metadata['context_data']);
        $this->assertNotNull($message->metadata['relevant_documents']);

        // Assert events were fired
        Event::assertDispatched(MessageUpdated::class);
        Event::assertDispatched(MessageStatusChanged::class);
    }

    /** @test */
    public function it_validates_webhook_signature()
    {
        $message = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::PROCESSING,
        ]);

        $webhookData = [
            'message_id' => $message->id,
            'conversation_id' => $this->conversation->id,
            'bot_id' => $this->bot->id,
            'context_data' => [],
        ];

        // Test with invalid signature
        $response = $this->postJson(
            route('webhooks.embedding.result'),
            $webhookData,
            ['X-Webhook-Signature' => 'invalid-signature']
        );

        $response->assertStatus(401)
                ->assertJson(['success' => false, 'message' => 'Invalid signature']);
    }

    /** @test */
    public function it_handles_webhook_validation_errors()
    {
        // Test with missing required fields
        $response = $this->postJson(route('webhooks.embedding.result'), [
            'message_id' => 999, // Non-existent message
        ]);

        $response->assertStatus(422); // Validation error
    }

    /** @test */
    public function it_handles_job_failures_gracefully()
    {
        Http::fake([
            config('chatbot.python.embedding_url') => Http::response([], 500),
        ]);

        $message = Message::factory()->create([
            'conversation_id' => $this->conversation->id,
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::PENDING,
        ]);

        $job = new ChatEmbeddingJob(
            $message->id,
            'What is Laravel?',
            $this->conversation->id,
            $this->bot->id
        );

        $this->expectException(\Exception::class);
        $job->handle();

        // Assert message status updated to failed
        $message->refresh();
        $this->assertEquals(MessageStatus::FAILED, $message->status);
        $this->assertNotNull($message->metadata['error']);
    }

    /**
     * Generate webhook signature for testing.
     */
    private function generateWebhookSignature(array $data): string
    {
        $secret = config('chatbot.webhook.secret', 'test-secret');
        $payload = json_encode($data);
        return 'sha256=' . hash_hmac('sha256', $payload, $secret);
    }
}
