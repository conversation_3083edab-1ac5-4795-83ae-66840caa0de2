<?php

namespace Modules\ChatBot\Enums;

enum ToolCallingMode: string
{
    case Auto = 'auto';
    case None = 'none';
    case Required = 'required';

    /**
     * Get all mode values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get mode label for display.
     */
    public function label(): string
    {
        return match ($this) {
            self::Auto => 'Auto',
            self::None => 'None',
            self::Required => 'Required',
        };
    }

    /**
     * Get mode description.
     */
    public function description(): string
    {
        return match ($this) {
            self::Auto => 'AI decides when to use tools',
            self::None => 'No tool calling allowed',
            self::Required => 'Must use tools for responses',
        };
    }
}
