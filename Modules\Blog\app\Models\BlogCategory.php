<?php

namespace Modules\Blog\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Modules\Blog\Enums\BlogStatus;
use Modules\Blog\Database\Factories\BlogCategoryFactory;
use Illuminate\Support\Facades\App;

class BlogCategory extends Model
{
    use HasFactory, SoftDeletes, Translatable;

    /**
     * The table associated with the model.
     */
    protected $table = 'blog_categories';

    /**
     * The attributes that are translatable.
     *
     * @var array<string>
     */
    public array $translatedAttributes = [
        'name',
        'slug',
        'image',
        'description',
        'meta_title',
        'meta_description'
    ];

    /**
     * The foreign key for translations.
     */
    protected $translationForeignKey = 'category_id';


    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'parent_id',
        'layout',
        'path',
        'status',
        'depth'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'parent_id' => 'integer',
        'depth' => 'integer', // Thêm cast cho depth
        'status' => BlogStatus::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<string>
     */
    protected $hidden = [
        'deleted_at'
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): BlogCategoryFactory
    {
        return BlogCategoryFactory::new();
    }

    /**
     * Get the parent category.
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(BlogCategory::class, 'parent_id');
    }

    /**
     * Get child categories.
     */
    public function children(): HasMany
    {
        return $this->hasMany(BlogCategory::class, 'parent_id')->orderBy('path');
    }

    /**
     * Get all descendant categories recursively.
     * This method loads children and their descendants.
     */
    public function descendants(): HasMany
    {
        return $this->children()->with('descendants');
    }

    /**
     * Get blog posts in this category.
     */
    public function posts(): BelongsToMany
    {
        return $this->belongsToMany(BlogPost::class, 'blog_category_posts', 'category_id', 'post_id');
    }

    /**
     * Scope a query to only include draft categories.
     */
    public function scopeDraft(Builder $query): Builder
    {
        return $query->where('status', BlogStatus::Draft);
    }

    /**
     * Scope a query to only include active categories.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', BlogStatus::Active);
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, BlogStatus|string $status): Builder
    {
        if ($status instanceof BlogStatus) {
            return $query->where('status', $status->value); // Ensure using value for Enum
        }
        return $query->where('status', $status);
    }

    /**
     * Check if this category is a root category.
     */
    public function isRoot(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * Check if this category has children.
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }


    /**
     * Get the depth/level of this category in the hierarchy (from stored 'depth' column).
     */
    public function getDepth(): int
    {
        return $this->depth ?? 0;
    }

    public static function getRootCategories(string $locale): Collection
    {
        return self::query()
            ->whereHas('translations', fn(Builder $query) => $query->where('locale', $locale))
            ->with(['translations' => fn($query) => $query->where('locale', $locale)])
            ->active()
            ->whereNull('parent_id')
            ->orderBy('id')
            ->get()
            ->map(function (BlogCategory $category) {
                if ($translation = $category->translations->first()) {
                    $category->fill($translation->only([
                        'name', 'slug', 'image', 'description',
                        'meta_title', 'meta_description'
                    ]));
                }
                return $category->makeHidden('translations');
            });
    }

    public static function getAllCategories(string $locale): Collection
    {
        return self::query()
            ->whereHas('translations', fn(Builder $query) => $query->where('locale', $locale))
            ->with(['translations' => fn($query) => $query->where('locale', $locale)])
            ->active()
            ->orderBy('path')
            ->get()
            ->map(function (BlogCategory $category) {
                if ($translation = $category->translations->first()) {
                    $category->fill($translation->only([
                        'name', 'slug', 'image', 'description',
                        'meta_title', 'meta_description'
                    ]));
                }
                return $category->makeHidden('translations');
            });
    }

    /**
     * Get the category by slug and locale.
     * This method retrieves the category based on the slug and locale,
     * and fills the model with the translation data.
     * @param string $slug The slug of the category
     * @param string $locale The locale for the translation
     * @return BlogCategory|null Returns the category if found, otherwise null
     */
    public static function getCategoryBySlug(string $slug, string $locale): ?BlogCategory
    {
        $category = self::query()
            ->whereHas('translations', fn(Builder $query) => $query->where('slug', $slug)->where('locale', $locale))
            ->with(['translations' => fn($query) => $query->where('locale', $locale)])
            ->active()
            ->first();

        if ($category && $translation = $category->translations->first()) {
            $category->fill($translation->only([
                'name', 'slug', 'image', 'description',
                'meta_title', 'meta_description'
            ]));
        }
        return $category?->makeHidden('translations');
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Set path and depth for hierarchical structure
        $updatePathAndDepth = function (BlogCategory $category) {
            if ($category->parent_id) {
                $parent = static::find($category->parent_id);
                if ($parent) {
                    $category->path = $parent->path ? $parent->path . '/' . $parent->id : (string)$parent->id;
                    $category->depth = ($parent->depth ?? 0) + 1;
                } else {
                    $category->path = null;
                    $category->depth = 0;
                }
            } else {
                // Root category
                $category->path = null;
                $category->depth = 0;
            }
        };

        static::creating($updatePathAndDepth);

        static::updating(function (BlogCategory $category) use ($updatePathAndDepth) {
            if ($category->isDirty('parent_id')) {
                $updatePathAndDepth($category);
            }
        });
    }
}
