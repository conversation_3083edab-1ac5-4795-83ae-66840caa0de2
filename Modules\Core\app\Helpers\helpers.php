<?php

use Illuminate\Support\Str;
use Mo<PERSON>les\Payment\Helpers\PaymentHelper;
use Modules\Setting\Facades\SettingFacade;
use Modules\User\Models\User;

if (!function_exists('setting')) {
    /**
     * Get a setting value from the database with caching support.
     *
     * This function provides a convenient way to retrieve settings from the Setting module.
     * It supports both simple key lookup and group.key notation.
     *
     * @param string $key Setting key or group.key notation
     * @param mixed|null $default Default value if setting not found
     * @return mixed The setting value cast to appropriate type, or default value
     */
    function setting(string $key, mixed $default = null): mixed
    {
        $value = null;

        if (str_contains($key, '.')) {
            [$groupKey, $settingKey] = explode('.', $key, 2);
            $value = SettingFacade::getSetting($groupKey, $settingKey);
        }
        // Return the value if found, otherwise return default
        return $value !== null ? $value : $default;

    }
}


if (!function_exists('settingGroup')) {
    /**
     * Get a setting value from the database with caching support.
     *
     * This function provides a convenient way to retrieve settings from the Setting module.
     * It supports both simple key lookup and group.key notation.
     *
     * @param string $groupKey
     * @param mixed|null $default Default value if setting not found
     * @return mixed The setting value cast to appropriate type, or default value
     */
    function settingGroup(string $groupKey, mixed $default = null): mixed
    {
        $settings = SettingFacade::getSettings()->get($groupKey);
        return $settings !== null ? $settings : $default;

    }
}

if (!function_exists('enabledCache')) {
    /**
     * Check if caching is enabled in settings.
     *
     * @return bool
     */
    function enabledCache(): bool
    {
        return (bool) config('setting.cache_enabled', true);
    }
}

if (!function_exists('cacheTTL')) {
    /**
     * Get cache TTL from settings.
     *
     * @return int
     */
    function cacheTTL(): int
    {
        return (int) config('setting.cache_ttl', 3600);
    }
}

if (!function_exists('uuid')) {
    /**
     * Generate a UUID v4.
     *
     * @return string
     */
    function uuid(): string
    {
        return (string) Str::uuid();
    }
}

if (!function_exists('format_bytes')) {
    /**
     * Format bytes into human readable format.
     *
     * @param int $bytes Number of bytes
     * @param int $precision Decimal precision
     * @return string
     */
    function format_bytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }
}

if (!function_exists('truncate_text')) {
    /**
     * Truncate text to specified length with ellipsis.
     *
     * @param string $text Text to truncate
     * @param int $length Maximum length
     * @param string $suffix Suffix to append
     * @return string
     */
    function truncate_text(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }

        return mb_substr($text, 0, $length - mb_strlen($suffix)) . $suffix;
    }
}

if (!function_exists('clean_html')) {
    /**
     * Clean HTML content by removing dangerous tags and attributes.
     *
     * @param string $html HTML content to clean
     * @param array $allowedTags Allowed HTML tags
     * @return string
     */
    function clean_html(string $html, array $allowedTags = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'a', 'img']): string
    {
        $allowedTagsString = '<' . implode('><', $allowedTags) . '>';
        return strip_tags($html, $allowedTagsString);
    }
}

if (!function_exists('get_file_extension')) {
    /**
     * Get file extension from filename or path.
     *
     * @param string $filename Filename or path
     * @return string
     */
    function get_file_extension(string $filename): string
    {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    }
}

if (!function_exists('is_image_file')) {
    /**
     * Check if file is an image based on extension.
     *
     * @param string $filename Filename or path
     * @return bool
     */
    function is_image_file(string $filename): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'];
        return in_array(get_file_extension($filename), $imageExtensions, true);
    }
}

if (!function_exists('format_date_for_humans')) {
    /**
     * Format date for human reading.
     *
     * @param \Carbon\Carbon|string $date Date to format
     * @param string $format Format string
     * @return string
     */
    function format_date_for_humans($date, string $format = 'M j, Y'): string
    {
        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        }

        return $date->format($format);
    }
}

if (!function_exists('time_ago')) {
    /**
     * Get human readable time difference.
     *
     * @param \Carbon\Carbon|string $date Date to compare
     * @return string
     */
    function time_ago($date): string
    {
        if (is_string($date)) {
            $date = \Carbon\Carbon::parse($date);
        }

        return $date->diffForHumans();
    }
}

if (!function_exists('setting_bool')) {
    /**
     * Get a boolean setting value.
     *
     * @param string $key Setting key
     * @param bool $default Default value
     * @return bool
     */
    function setting_bool(string $key, bool $default = false): bool
    {
        $value = setting($key, $default);

        if (is_bool($value)) {
            return $value;
        }

        if (is_string($value)) {
            return in_array(strtolower($value), ['true', '1', 'yes', 'on'], true);
        }

        return (bool) $value;
    }
}

if (!function_exists('setting_int')) {
    /**
     * Get an integer setting value.
     *
     * @param string $key Setting key
     * @param int $default Default value
     * @return int
     */
    function setting_int(string $key, int $default = 0): int
    {
        return (int) setting($key, $default);
    }
}

if (!function_exists('setting_float')) {
    /**
     * Get a float setting value.
     *
     * @param string $key Setting key
     * @param float $default Default value
     * @return float
     */
    function setting_float(string $key, float $default = 0.0): float
    {
        return (float) setting($key, $default);
    }
}

if (!function_exists('setting_array')) {
    /**
     * Get an array setting value.
     *
     * @param string $key Setting key
     * @param array $default Default value
     * @return array
     */
    function setting_array(string $key, array $default = []): array
    {
        $value = setting($key, $default);

        if (is_array($value)) {
            return $value;
        }

        if (is_string($value)) {
            $decoded = json_decode($value, true);
            return is_array($decoded) ? $decoded : $default;
        }

        return $default;
    }
}

if (!function_exists('app_name')) {
    /**
     * Get application name from settings or config.
     *
     * @return string
     */
    function app_name(): string
    {
        return setting('general.app_name', config('app.name', 'Laravel ProCMS'));
    }
}

if (!function_exists('user_currency')) {
    /**
     * Get user's preferred currency.
     *
     * @param User|null $user
     * @return string
     */
    function user_currency($user = null): string
    {
        return PaymentHelper::getUserCurrency($user);
    }
}

if (!function_exists('format_payment_amount')) {
    /**
     * Format amount with user's preferred currency.
     *
     * @param float $amount
     * @param string|null $currency
     * @param User|null $user
     * @return string
     */
    function format_payment_amount(float $amount, ?string $currency = null, $user = null): string
    {
        return \Modules\Payment\Helpers\PaymentHelper::formatAmountForUser($amount, $currency, $user);
    }
}

if (!function_exists('app_url')) {
    /**
     * Get application URL from settings or config.
     *
     * @return string
     */
    function app_url(): string
    {
        return setting('general.app_url', config('app.url', 'http://localhost'));
    }
}

if (!function_exists('site_logo')) {
    /**
     * Get site logo URL from settings.
     *
     * @param string $default Default logo path
     * @return string
     */
    function site_logo(string $default = '/images/logo.png'): string
    {
        return setting('general.site_logo', $default);
    }
}

if (!function_exists('site_favicon')) {
    /**
     * Get site favicon URL from settings.
     *
     * @param string $default Default favicon path
     * @return string
     */
    function site_favicon(string $default = '/favicon.ico'): string
    {
        return setting('general.site_favicon', $default);
    }
}

if (!function_exists('currentLocale')) {
    /**
     * Get site description from settings.
     *
     * @return string
     */
    function currentLocale(): string
    {
        return app()->getLocale();
    }
}

if (!function_exists('useCamelCase')) {
    function useCamelCase(array $array): array
    {
        $camelCaseArray = [];
        foreach ($array as $key => $value) {
            $camelCaseKey = lcfirst(str_replace(' ', '', ucwords(str_replace('_', ' ', $key))));
            $camelCaseArray[$camelCaseKey] = $value;
        }
        return $camelCaseArray;
    }
}
