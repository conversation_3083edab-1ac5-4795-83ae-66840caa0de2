<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShare;
use Modules\ChatBot\Models\BotShareLink;
use Modules\Core\Traits\ResponseTrait;
use Modules\User\Models\User;
use Carbon\Carbon;

class BotShareController extends Controller
{
    use ResponseTrait;



    /**
     * Share a bot with a user.
     */
    public function shareBot(Request $request, string $botUuid): JsonResponse
    {
        $request->validate([
            'user_email' => 'required|email|exists:users,email',
            'shareable_type' => 'nullable|string',
            'shareable_id' => 'nullable|integer',
        ]);

        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $botUuid)->firstOrFail();

            // Organization bots don't use share system
            if ($bot->isOrganization()) {
                return $this->errorResponse('Organization bots use member system instead of sharing. Please add users as organization members.', 400);
            }

            // Check if user can share this bot (includes both permission and shareability checks)
            if (!$bot->canBeSharedBy($user->id)) {
                return $this->errorResponse('You do not have permission to share this bot or this bot cannot be shared', 403);
            }

            // Find target user
            $targetUser = User::where('email', $request->user_email)->first();

            if (!$targetUser) {
                return $this->errorResponse('User not found', 404);
            }

            if ($targetUser->id === $user->id) {
                return $this->errorResponse('You cannot share a bot with yourself', 400);
            }

            // Determine shareable entity
            $shareableType = $request->shareable_type ?: User::class;
            $shareableId = $request->shareable_id ?: $targetUser->id;

            // Create share
            $share = $bot->shareWith($targetUser->id, $shareableId, $shareableType);

            if (!$share) {
                return $this->errorResponse('Failed to share bot', 500);
            }

            $share->load(['bot', 'user', 'shareable']);

            return $this->successResponse(
                $share,
                'Bot shared successfully',
                201
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Bot not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Create a share link for a bot.
     */
    public function createShareLink(Request $request, string $botUuid): JsonResponse
    {
        $request->validate([
            'expires_in_hours' => 'nullable|integer|min:1|max:8760', // Max 1 year
            'expires_in_days' => 'nullable|integer|min:1|max:365',
            'shareable_type' => 'nullable|string',
            'shareable_id' => 'nullable|integer',
        ]);

        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $botUuid)->firstOrFail();

            // Organization bots don't use share system
            if ($bot->isOrganization()) {
                return $this->errorResponse('Organization bots use member system instead of sharing. Please add users as organization members.', 400);
            }

            // Check if user can share this bot (includes both permission and shareability checks)
            if (!$bot->canBeSharedBy($user->id)) {
                return $this->errorResponse('You do not have permission to share this bot or this bot cannot be shared', 403);
            }

            // Calculate expiration
            $expiresAt = null;
            if ($request->expires_in_hours) {
                $expiresAt = now()->addHours($request->expires_in_hours);
            } elseif ($request->expires_in_days) {
                $expiresAt = now()->addDays($request->expires_in_days);
            }

            // Determine shareable entity (default to User)
            $shareableType = $request->shareable_type ?: User::class;
            $shareableId = $request->shareable_id ?: $user->id;

            // Create share link
            $shareLink = $bot->createShareLink($shareableId, $shareableType, $expiresAt);

            if (!$shareLink) {
                return $this->errorResponse('Failed to create share link', 500);
            }

            $shareLink->load(['bot', 'shareable']);

            return $this->successResponse(
                $shareLink->getStats(),
                'Share link created successfully',
                201
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Bot not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get all shares for a bot.
     */
    public function getBotShares(Request $request, string $botUuid): JsonResponse
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $botUuid)->firstOrFail();

            // Check if user can view shares
            if (!$bot->userCan($user->id, 'admin')) {
                return $this->errorResponse('You do not have permission to view shares', 403);
            }

            $perPage = $request->get('per_page', 15);
            
            $shares = $bot->shares()
                         ->with(['user', 'shareable'])
                         ->paginate($perPage);

            return $this->successResponse(
                [
                    'data' => $shares->items(),
                    'meta' => $this->getPaginationMeta($shares)
                ],
                'Bot shares retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Bot not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get all share links for a bot.
     */
    public function getBotShareLinks(Request $request, string $botUuid): JsonResponse
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
            'active_only' => 'nullable|boolean',
        ]);

        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $botUuid)->firstOrFail();

            // Check if user can view share links
            if (!$bot->userCan($user->id, 'admin')) {
                return $this->errorResponse('You do not have permission to view share links', 403);
            }

            $perPage = $request->get('per_page', 15);
            $activeOnly = $request->get('active_only', false);
            
            $query = $bot->shareLinks()->with(['shareable']);
            
            if ($activeOnly) {
                $query->active();
            }

            $shareLinks = $query->paginate($perPage);

            return $this->successResponse(
                [
                    'data' => $shareLinks->items(),
                    'meta' => $this->getPaginationMeta($shareLinks)
                ],
                'Bot share links retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Bot not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Revoke a user share.
     */
    public function revokeShare(Request $request, string $botUuid, int $shareId): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $botUuid)->firstOrFail();

            // Check if user can manage shares
            if (!$bot->userCan($user->id, 'admin')) {
                return $this->errorResponse('You do not have permission to manage shares', 403);
            }

            $share = BotShare::where('id', $shareId)
                            ->where('bot_id', $bot->id)
                            ->firstOrFail();

            $share->delete();

            return $this->successResponse(
                null,
                'Share removed successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Share not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Revoke a share link.
     */
    public function revokeShareLink(Request $request, string $botUuid, int $linkId): JsonResponse
    {
        try {
            $user = auth()->user();
            $bot = Bot::where('uuid', $botUuid)->firstOrFail();

            // Check if user can manage share links
            if (!$bot->userCan($user->id, 'admin')) {
                return $this->errorResponse('You do not have permission to manage share links', 403);
            }

            $shareLink = BotShareLink::where('id', $linkId)
                                   ->where('bot_id', $bot->id)
                                   ->firstOrFail();

            $shareLink->delete();

            return $this->successResponse(
                null,
                'Share link removed successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Share link not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Access a shared bot via share link (public access).
     */
    public function accessSharedBot(string $token): JsonResponse
    {
        try {
            $shareLink = BotShareLink::with(['bot', 'shareable'])
                                   ->where('token', $token)
                                   ->active()
                                   ->firstOrFail();

            $bot = $shareLink->bot;

            // Check if bot is still shareable
            if (!$bot->canBeShared()) {
                return $this->errorResponse('This bot is no longer available for sharing', 403);
            }

            return $this->successResponse([
                'bot' => [
                    'uuid' => $bot->uuid,
                    'name' => $bot->name,
                    'description' => $bot->description,
                    'logo_url' => $bot->logo_url,
                    'greeting_message' => $bot->greeting_message,
                    'starter_messages' => $bot->starter_messages,
                ],
                'share_info' => [
                    'token' => $shareLink->token,
                    'expires_at' => $shareLink->expires_at,
                    'shareable_type' => $shareLink->shareable_type,
                ]
            ], 'Shared bot accessed successfully');

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Share link not found or expired', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Get shared bot info (public access).
     */
    public function getSharedBotInfo(string $token): JsonResponse
    {
        try {
            $shareLink = BotShareLink::with(['bot'])
                                   ->where('token', $token)
                                   ->active()
                                   ->firstOrFail();

            $bot = $shareLink->bot;

            return $this->successResponse([
                'name' => $bot->name,
                'description' => $bot->description,
                'logo_url' => $bot->logo_url,
                'is_available' => $bot->canBeShared(),
                'expires_at' => $shareLink->expires_at,
            ], 'Shared bot info retrieved successfully');

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Share link not found or expired', 404);
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get pagination meta data.
     */
    private function getPaginationMeta($paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'last_page' => $paginator->lastPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
        ];
    }
}
