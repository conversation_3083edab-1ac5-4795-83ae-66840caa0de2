<?php

namespace Modules\ChatBot\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Broadcast;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\ChatBot\Models\Query;
use Modules\ChatBot\Events\KnowledgeBaseProcessed;
use Modules\ChatBot\Events\QueryResultsReceived;
use Modules\ChatBot\Jobs\LLMProcessingJob;
use Modules\Core\Traits\ResponseTrait;

class WebhookController extends Controller
{
    use ResponseTrait;

    /**
     * Handle RAG processing webhook from Python service.
     */
    public function ingest(Request $request): JsonResponse
    {
        try {
            // Validate outer structure
            $validator = Validator::make($request->all(), [
                'taskId' => 'required|uuid',
                'type' => 'required|string|in:file',
                'ownerId' => 'required|integer',
                'timestamp' => 'required|date',
                'results' => 'required|array|min:1',
                'results.*.fileId' => 'required|integer|exists:knowledge_bases,id',
                'results.*.status' => 'required|string|in:ready,error',
                'results.*.chunkCount' => 'nullable|integer|min:0',
                'results.*.error' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                Log::warning('Invalid webhook payload received', [
                    'payload' => $request->all(),
                    'errors' => $validator->errors()->toArray(),
                ]);

                return $this->errorResponse($validator->errors(), 'Invalid payload', 400);
            }

            $processedResults = [];

            foreach ($request->input('results') as $result) {
                $fileId = $result['fileId'];
                $status = $result['status'];
                $chunkCount = $result['chunkCount'] ?? 0;
                $error = $result['error'] ?? null;

                $knowledgeBase = KnowledgeBase::find($fileId);
                if (!$knowledgeBase) {
                    Log::warning('Knowledge base not found for result', [
                        'file_id' => $fileId,
                        'task_id' => $request->input('taskId'),
                    ]);

                    $processedResults[] = [
                        'file_id' => $fileId,
                        'status' => 'not_found'
                    ];
                    continue;
                }

                $updated = $knowledgeBase->updateProcessingResult($status, $chunkCount, $error);

                if (!$updated) {
                    Log::error('Failed to update knowledge base processing result', [
                        'file_id' => $fileId,
                        'status' => $status,
                        'chunk_count' => $chunkCount,
                        'error' => $error,
                    ]);

                    $processedResults[] = [
                        'file_id' => $fileId,
                        'status' => 'update_failed'
                    ];
                    continue;
                }

                // Broadcast event if needed
                $this->broadcastKnowledgeBaseProcessed($knowledgeBase, $status, $chunkCount, $error);

                Log::info('RAG processing result handled successfully', [
                    'file_id' => $fileId,
                    'task_id' => $request->input('taskId'),
                    'status' => $status,
                    'chunk_count' => $chunkCount,
                    'uuid' => $knowledgeBase->uuid,
                ]);

                $processedResults[] = [
                    'file_id' => $fileId,
                    'status' => 'processed'
                ];
            }

            return $this->successResponse([
                'message' => 'Webhook processed',
                'task_id' => $request->input('taskId'),
                'processed' => $processedResults,
            ]);
        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'payload' => $request->all(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $this->errorResponse(null, 'Internal server error', 500);
        }
    }
    /**
     * Handle query results from Python service.
     */
    public function query(Request $request): JsonResponse
    {
        try {
            // Validate webhook payload
            $validator = Validator::make($request->all(), [
                'taskId' => 'required|string',
                'type' => 'required|string|in:query',
                'queryId' => 'required|string'
            ]);

            if ($validator->fails()) {
                Log::warning('Query webhook validation failed', [
                    'errors' => $validator->errors(),
                    'payload' => $request->all(),
                ]);

                return $this->errorResponse($validator->errors(), 'Invalid payload', 400);
            }

            $queryId = $request->input('queryId');
            $results = $request->input('results');
            $timestamp = $request->input('timestamp');

            Log::info('Query webhook received', [
                'query_id' => $queryId,
                'results_count' => count($results),
                'timestamp' => $timestamp,
            ]);

            // Find the query
            $query = Query::where('uuid', $queryId)->first();

            if (!$query) {
                Log::warning('Query not found for webhook', [
                    'query_id' => $queryId,
                ]);

                return $this->errorResponse('Query not found', 404);
            }

            // Update query with RAG results (not completed yet - waiting for LLM)
            $query->update([
                'status' => 'rag_completed',
                'metadata' => array_merge($query->metadata ?? [], [
                    'rag_webhook_received_at' => now()->toISOString(),
                    'python_timestamp' => $timestamp,
                    'rag_results_count' => count($results),
                    'rag_results' => $results, // Store RAG results temporarily
                ])
            ]);

            // Dispatch LLM processing job with RAG results
            LLMProcessingJob::dispatch($query, $results);

            Log::info('Query RAG webhook processed, LLM job dispatched', [
                'query_id' => $queryId,
                'query_db_id' => $query->id,
                'rag_results_count' => count($results),
                'status' => 'rag_completed',
            ]);

            return $this->successResponse([
                'message' => 'RAG results received, LLM processing dispatched',
                'query_id' => $queryId,
                'status' => 'rag_completed',
                'next_step' => 'llm_processing',
            ]);

        } catch (\Exception $e) {
            Log::error('Query webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all(),
            ]);

            return $this->errorResponse(null, 'Internal server error', 500);
        }
    }

    /**
     * Broadcast knowledge base processing completion to user via WebSocket.
     */
    private function broadcastKnowledgeBaseProcessed(KnowledgeBase $knowledgeBase, string $status, int $chunkCount, ?string $error): void
    {
        try {
            $broadcastData = [
                'knowledge_base_id' => $knowledgeBase->uuid,
                'name' => $knowledgeBase->name,
                'type' => $knowledgeBase->type,
                'status' => $status,
                'chunk_count' => $chunkCount,
                'error' => $error,
                'timestamp' => now()->toISOString(),
                'metadata' => $knowledgeBase->metadata,
                'bot_ids' => $knowledgeBase->bots()->pluck('bots.id')->toArray(),
            ];

            // Fire the event which will handle broadcasting
            event(new KnowledgeBaseProcessed($knowledgeBase, $broadcastData));

            Log::info('Knowledge base processing event fired', [
                'knowledge_base_id' => $knowledgeBase->uuid,
                'status' => $status,
                'chunk_count' => $chunkCount,
                'owner_id' => $knowledgeBase->owner_id,
                'bot_count' => count($broadcastData['bot_ids']),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast knowledge base processing', [
                'knowledge_base_id' => $knowledgeBase->uuid,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Broadcast query results to user via WebSocket.
     */
    private function broadcastQueryResults(Query $query): void
    {
        try {
            $broadcastData = [
                'query_id' => $query->uuid,
                'question' => $query->question,
                'results' => $query->getFormattedResults(),
                'timestamp' => now()->toISOString(),
                'bot_id' => $query->bot_id,
                'conversation_id' => $query->conversation_id,
                'message_id' => $query->message_id,
                'status' => $query->status,
                'metadata' => $query->metadata,
            ];

            // Fire the event which will handle broadcasting
            event(new QueryResultsReceived($query, $broadcastData));

            Log::info('Query results event fired', [
                'query_id' => $query->uuid,
                'conversation_id' => $query->conversation_id,
                'owner_id' => $query->owner_id,
                'results_count' => count($query->results ?? []),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to broadcast query results', [
                'query_id' => $query->uuid,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Health check endpoint for webhook service.
     */
    public function health(): JsonResponse
    {
        return $this->successResponse([
            'status' => 'healthy',
            'timestamp' => now()->toISOString(),
            'service' => 'RAG Webhook Handler',
        ]);
    }
}
