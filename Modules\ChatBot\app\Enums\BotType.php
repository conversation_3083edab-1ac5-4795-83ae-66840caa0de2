<?php

namespace Modules\ChatBot\Enums;

enum BotType: string
{
    case PERSONAL = 'personal';
    case ORGANIZATION = 'organization';

    /**
     * Get the label for the bot type.
     */
    public function label(): string
    {
        return match ($this) {
            self::PERSONAL => 'Cá nhân',
            self::ORGANIZATION => 'Tổ chức',
        };
    }

    /**
     * Get the description for the bot type.
     */
    public function description(): string
    {
        return match ($this) {
            self::PERSONAL => 'Bot cá nhân có thể được chia sẻ với bất kỳ người dùng nào thông qua share links',
            self::ORGANIZATION => 'Bot tổ chức được truy cập bởi thành viên và khách mời của tổ chức',
        };
    }

    /**
     * Get the color for the bot type.
     */
    public function color(): string
    {
        return match ($this) {
            self::PERSONAL => 'blue',
            self::ORGANIZATION => 'purple',
        };
    }

    /**
     * Check if the bot type allows sharing via share system.
     * Organization bots use member system instead.
     */
    public function isShareable(): bool
    {
        return match ($this) {
            self::PERSONAL => true,      // Personal bots use share system
            self::ORGANIZATION => false, // Organization bots use member system
        };
    }

    /**
     * Check if the bot type is personal.
     */
    public function isPersonal(): bool
    {
        return $this === self::PERSONAL;
    }

    /**
     * Check if the bot type is organization.
     */
    public function isOrganization(): bool
    {
        return $this === self::ORGANIZATION;
    }

    /**
     * Get all bot type options.
     */
    public static function options(): array
    {
        return [
            self::PERSONAL->value => self::PERSONAL->label(),
            self::ORGANIZATION->value => self::ORGANIZATION->label(),
        ];
    }
}
