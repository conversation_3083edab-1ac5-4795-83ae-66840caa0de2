<?php

namespace Modules\ChatBot\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\KnowledgeBase;

class RAGFileProcessingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 3;
    public int $retryAfter = 60;
    public int $timeout = 300;

    public function __construct(
        public array $knowledgeBaseIds,
        public int $ownerId,
        public string $ownerType,
        public string $operation = 'process', // 'process', 'retrain', 'bot_creation', 'standalone_upload'
        public ?int $botId = null
    ) {
        $this->onQueue(config('chatbot.rag.queue_name', 'rag-processing'));
    }

    public function handle(): void
    {
        try {
            $taskId = Str::uuid();

            // Load knowledge bases
            $knowledgeBases = KnowledgeBase::whereIn('id', $this->knowledgeBaseIds)
                ->where('owner_id', $this->ownerId)
                ->where('owner_type', $this->ownerType)
                ->get();

            if ($knowledgeBases->isEmpty()) {
                return;
            }

            // Update status to processing
            $knowledgeBases->each(function ($kb) use ($taskId) {
                $kb->update([
                    'status' => 'processing',
                    'metadata' => array_merge($kb->metadata ?? [], [
                        'processing_started_at' => now()->toISOString(),
                        'operation' => $this->operation,
                        'job_instance_id' => $this->job->uuid(),
                        'task_id' => $taskId
                    ])
                ]);
            });

            // Prepare payload
            $payload = $this->buildPayload($taskId, $knowledgeBases);

            // Send to Python service
            $this->pushToPythonQueue($payload, 'rag-processing');


            Log::info('RAG processing job pushed to Python queue successfully', [
                'task_id' => $taskId,
                'knowledge_base_ids' => $this->knowledgeBaseIds,
                'operation' => $this->operation,
                'bot_id' => $this->botId,
            ]);

        } catch (\Exception $e) {
            Log::error('RAG processing job failed', [
                'knowledge_base_ids' => $this->knowledgeBaseIds,
                'operation' => $this->operation,
                'error' => $e->getMessage(),
            ]);

            // Update status to error
            KnowledgeBase::whereIn('id', $this->knowledgeBaseIds)
                ->update([
                    'status' => 'error',
                    'metadata->error' => $e->getMessage(),
                    'metadata->failed_at' => now()->toISOString(),
                ]);

            throw $e;
        }
    }

    private function buildPayload(string $taskId, $knowledgeBases): array
    {
        $files = $knowledgeBases->map(function ($kb) {
            return [
                'fileId' => $kb->id,
                'storagePath' => $this->getStoragePath($kb) ? Storage::disk('local')->path($this->getStoragePath($kb)) : '',
                'url' => $this->getFileUrl($kb),
            ];
        })->toArray();

        return [
            'taskId' => $taskId,
            'type' => 'file',
            'operation' => $this->operation,
            'ownerId' => $this->ownerId,
            'collection' => 'documents',
            'files' => $files,
            'webhookUrl' => route('api.api.webhook.ingest'),
        ];
    }

    private function getStoragePath(KnowledgeBase $kb): string
    {
        return $kb->storage_path ?? '';
    }

    private function getFileUrl(KnowledgeBase $kb): string
    {
        $storagePath = $this->getStoragePath($kb);

        return $storagePath ? route('knowledge-base.download', ['id' => $kb->uuid]) : '';
    }

    private function pushToPythonQueue(array $payload, string $queueName): void
    {
        // Push payload to Redis queue for Python service to consume
        Redis::lpush($queueName, json_encode($payload));

        Log::info('Payload pushed to Redis queue', [
            'queue' => $queueName,
            'task_id' => $payload['taskId'],
            'payload_size' => strlen(json_encode($payload))
        ]);
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('RAG processing job failed permanently', [
            'knowledge_base_ids' => $this->knowledgeBaseIds,
            'owner_id' => $this->ownerId,
            'operation' => $this->operation,
            'bot_id' => $this->botId,
            'error' => $exception->getMessage(),
        ]);

        // Update all knowledge bases to error status
        KnowledgeBase::whereIn('id', $this->knowledgeBaseIds)
            ->update([
                'status' => 'error',
                'metadata->error' => $exception->getMessage(),
                'metadata->failed_at' => now()->toISOString(),
                'metadata->retry_count' => $this->attempts(),
            ]);
    }
}
