<?php

namespace Modules\ChatBot\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\ChatBot\Enums\ContentType;
use Modules\Core\Http\Requests\BaseFormRequest;

class UpdateMessageRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'content' => 'sometimes|required|string|max:50000',
            'content_type' => ['nullable', Rule::enum(ContentType::class)],
            'attachments' => 'nullable|array|max:10',
            'attachments.*' => 'file|max:10240', // 10MB per file
            'metadata' => 'nullable|array',
            'quality_score' => 'nullable|integer|min:1|max:5',
            'is_helpful' => 'nullable|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'content.required' => 'Nội dung tin nhắn là bắt buộc.',
            'content.max' => 'Nội dung tin nhắn không được vượt quá 50,000 ký tự.',
            'attachments.max' => 'Không được đính kèm quá 10 file.',
            'attachments.*.file' => 'Tệp đính kèm không hợp lệ.',
            'attachments.*.max' => 'Mỗi tệp đính kèm không được vượt quá 10MB.',
            'quality_score.integer' => 'Điểm chất lượng phải là số nguyên.',
            'quality_score.min' => 'Điểm chất lượng phải từ 1 đến 5.',
            'quality_score.max' => 'Điểm chất lượng phải từ 1 đến 5.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $message = $this->route('message'); // Assuming route parameter is 'message'
            
            if ($message) {
                $user = auth()->user();
                
                // Check if user can edit this message
                if ($message->conversation->user_id !== $user->id || 
                    $message->conversation->user_type !== get_class($user)) {
                    $validator->errors()->add('message', 'Bạn không có quyền chỉnh sửa tin nhắn này.');
                }
                
                // Check if message is editable (only user messages can be edited)
                if (!$message->isFromUser()) {
                    $validator->errors()->add('message', 'Chỉ có thể chỉnh sửa tin nhắn của người dùng.');
                }
                
                // Check if conversation is still editable
                if (!$message->conversation->status->isEditable()) {
                    $validator->errors()->add('message', 'Cuộc trò chuyện này không thể chỉnh sửa.');
                }
            }

            // Validate content based on content type
            if ($this->content_type && $this->attachments) {
                $contentType = ContentType::from($this->content_type);
                
                if ($contentType === ContentType::TEXT && !empty($this->attachments)) {
                    $validator->errors()->add('attachments', 'Tin nhắn văn bản không được có tệp đính kèm.');
                }
                
                if ($contentType !== ContentType::TEXT && empty($this->attachments)) {
                    $validator->errors()->add('attachments', 'Loại nội dung này yêu cầu tệp đính kèm.');
                }
            }
        });
    }
}
