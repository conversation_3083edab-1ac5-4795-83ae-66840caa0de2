<?php

namespace Modules\ChatBot\Facades;

use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Facade;
use Modules\ChatBot\Models\Message;

/**
 * @method static LengthAwarePaginator getConversationMessages(string $conversationUuid, int $perPage = 50)
 * @method static Message createUserMessage(array $data)
 * @method static Message generateAIResponse(\Modules\ChatBot\Models\Conversation $conversation, array $options = [])
 * @method static Message updateMessage(Message $message, array $data)
 * @method static bool deleteMessage(Message $message)
 * @method static Message retryMessage(Message $message)
 * @method static array buildConversationContext(\Modules\ChatBot\Models\Conversation $conversation)
 * @method static array getMessageStats(array $filters = [])
 *
 * @see \Modules\ChatBot\Services\MessageService
 */
class MessageFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'chatbot.message';
    }
}
