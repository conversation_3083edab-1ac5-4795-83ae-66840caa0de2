<?php

namespace Modules\Blog\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Blog\Models\BlogCategoryTranslation;
use Modules\Blog\Models\BlogCategory;

class BlogCategoryTranslationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = BlogCategoryTranslation::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'category_id' => BlogCategory::factory(),
            'locale' => 'en',
            'name' => $this->faker->words(2, true),
            'slug' => $this->faker->unique()->slug(2),
            'image' => $this->faker->optional(0.4)->imageUrl(400, 300, 'business'),
            'description' => $this->faker->optional(0.7)->paragraph(3),
            'meta_title' => $this->faker->sentence(3),
            'meta_description' => $this->faker->paragraph(2),
        ];
    }

    /**
     * Indicate that the translation is in Vietnamese.
     */
    public function vietnamese(): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => 'vi',
            'name' => $this->faker->words(2, true),
            'slug' => $this->faker->unique()->slug(2),
        ]);
    }

    /**
     * Indicate that the translation is in English.
     */
    public function english(): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => 'en',
        ]);
    }

    /**
     * Set a specific category.
     */
    public function forCategory(BlogCategory $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category_id' => $category->id,
        ]);
    }

    /**
     * Set a specific locale.
     */
    public function locale(string $locale): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => $locale,
        ]);
    }

    /**
     * Set a specific slug.
     */
    public function slug(string $slug): static
    {
        return $this->state(fn (array $attributes) => [
            'slug' => $slug,
        ]);
    }
}
