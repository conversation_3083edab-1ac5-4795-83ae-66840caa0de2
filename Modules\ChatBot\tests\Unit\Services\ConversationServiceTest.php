<?php

namespace Modules\ChatBot\Tests\Unit\Services;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Services\ConversationService;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\ChatBot\Enums\BotStatus;
use Modules\Core\Exceptions\BusinessException;
use App\Models\User;

class ConversationServiceTest extends TestCase
{
    use RefreshDatabase;

    private ConversationService $conversationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate', ['--database' => 'testing']);
        $this->conversationService = app(ConversationService::class);
    }

    /** @test */
    public function it_can_create_a_conversation()
    {
        $user = User::factory()->create();
        $bot = Bot::factory()->create(['status' => BotStatus::Active]);

        $data = [
            'title' => 'Test Conversation',
            'bot_id' => $bot->id,
            'user_id' => $user->id,
            'user_type' => User::class,
        ];

        $conversation = $this->conversationService->createConversation($data);

        $this->assertInstanceOf(Conversation::class, $conversation);
        $this->assertEquals('Test Conversation', $conversation->title);
        $this->assertEquals($bot->id, $conversation->bot_id);
        $this->assertEquals($user->id, $conversation->user_id);
        $this->assertEquals(ConversationStatus::ACTIVE, $conversation->status);
    }

    /** @test */
    public function it_generates_title_when_not_provided()
    {
        $user = User::factory()->create();
        $bot = Bot::factory()->create(['status' => BotStatus::Active]);

        $data = [
            'bot_id' => $bot->id,
            'user_id' => $user->id,
            'user_type' => User::class,
        ];

        $conversation = $this->conversationService->createConversation($data);

        $this->assertNotNull($conversation->title);
        $this->assertStringContainsString($bot->name, $conversation->title);
    }

    /** @test */
    public function it_throws_exception_when_bot_is_not_active()
    {
        $user = User::factory()->create();
        $bot = Bot::factory()->create(['status' => BotStatus::Draft]);

        $data = [
            'bot_id' => $bot->id,
            'user_id' => $user->id,
            'user_type' => User::class,
        ];

        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('Bot is not active and cannot be used for conversations.');

        $this->conversationService->createConversation($data);
    }

    /** @test */
    public function it_can_get_user_conversations()
    {
        $user = User::factory()->create();
        $bot = Bot::factory()->create();

        // Create conversations for the user
        Conversation::factory()->count(3)->create([
            'user_id' => $user->id,
            'user_type' => User::class,
            'bot_id' => $bot->id,
        ]);

        // Create conversation for another user
        $otherUser = User::factory()->create();
        Conversation::factory()->create([
            'user_id' => $otherUser->id,
            'user_type' => User::class,
            'bot_id' => $bot->id,
        ]);

        $conversations = $this->conversationService->getUserConversations(
            $user->id,
            User::class,
            [],
            10
        );

        $this->assertCount(3, $conversations->items());
        $this->assertTrue($conversations->every(fn($conv) => $conv->user_id === $user->id));
    }

    /** @test */
    public function it_can_filter_user_conversations_by_status()
    {
        $user = User::factory()->create();
        $bot = Bot::factory()->create();

        Conversation::factory()->create([
            'user_id' => $user->id,
            'user_type' => User::class,
            'bot_id' => $bot->id,
            'status' => ConversationStatus::ACTIVE,
        ]);

        Conversation::factory()->create([
            'user_id' => $user->id,
            'user_type' => User::class,
            'bot_id' => $bot->id,
            'status' => ConversationStatus::COMPLETED,
        ]);

        $activeConversations = $this->conversationService->getUserConversations(
            $user->id,
            User::class,
            ['status' => ConversationStatus::ACTIVE],
            10
        );

        $this->assertCount(1, $activeConversations->items());
        $this->assertEquals(ConversationStatus::ACTIVE, $activeConversations->items()[0]->status);
    }

    /** @test */
    public function it_can_update_conversation()
    {
        $conversation = Conversation::factory()->create(['title' => 'Old Title']);

        $updatedConversation = $this->conversationService->updateConversation(
            $conversation,
            ['title' => 'New Title']
        );

        $this->assertEquals('New Title', $updatedConversation->title);
    }

    /** @test */
    public function it_can_complete_conversation()
    {
        $conversation = Conversation::factory()->active()->create();

        $completedConversation = $this->conversationService->completeConversation($conversation);

        $this->assertEquals(ConversationStatus::COMPLETED, $completedConversation->status);
    }

    /** @test */
    public function it_throws_exception_when_completing_non_completable_conversation()
    {
        $conversation = Conversation::factory()->archived()->create();

        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('Conversation cannot be completed in its current status.');

        $this->conversationService->completeConversation($conversation);
    }

    /** @test */
    public function it_can_archive_conversation()
    {
        $conversation = Conversation::factory()->active()->create();

        $archivedConversation = $this->conversationService->archiveConversation($conversation);

        $this->assertEquals(ConversationStatus::ARCHIVED, $archivedConversation->status);
    }

    /** @test */
    public function it_can_reactivate_conversation()
    {
        $conversation = Conversation::factory()->completed()->create();

        $reactivatedConversation = $this->conversationService->reactivateConversation($conversation);

        $this->assertEquals(ConversationStatus::ACTIVE, $reactivatedConversation->status);
    }

    /** @test */
    public function it_throws_exception_when_reactivating_non_reactivatable_conversation()
    {
        $conversation = Conversation::factory()->active()->create();

        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('Conversation cannot be reactivated in its current status.');

        $this->conversationService->reactivateConversation($conversation);
    }

    /** @test */
    public function it_can_delete_conversation_and_messages()
    {
        $conversation = Conversation::factory()->create();
        $messages = Message::factory()->count(3)->create([
            'conversation_id' => $conversation->id,
        ]);

        $result = $this->conversationService->deleteConversation($conversation);

        $this->assertTrue($result);
        $this->assertDatabaseMissing('conversations', ['id' => $conversation->id]);
        $this->assertDatabaseMissing('messages', ['conversation_id' => $conversation->id]);
    }

    /** @test */
    public function it_can_get_conversation_statistics()
    {
        $conversation = Conversation::factory()->create();
        
        // Create messages with different roles and tokens
        Message::factory()->count(2)->create([
            'conversation_id' => $conversation->id,
            'role' => 'user',
        ]);
        
        Message::factory()->count(3)->create([
            'conversation_id' => $conversation->id,
            'role' => 'assistant',
            'total_tokens' => 100,
            'cost' => 0.01,
            'response_time_ms' => 1000,
        ]);

        $stats = $this->conversationService->getConversationStats($conversation);

        $this->assertEquals(5, $stats['total_messages']);
        $this->assertEquals(2, $stats['user_messages']);
        $this->assertEquals(3, $stats['assistant_messages']);
        $this->assertEquals(300, $stats['total_tokens']);
        $this->assertEquals(0.03, $stats['total_cost']);
        $this->assertEquals(1000, $stats['average_response_time']);
    }

    /** @test */
    public function it_can_get_active_conversations_count()
    {
        $user = User::factory()->create();

        Conversation::factory()->count(2)->create([
            'user_id' => $user->id,
            'user_type' => User::class,
            'status' => ConversationStatus::ACTIVE,
        ]);

        Conversation::factory()->create([
            'user_id' => $user->id,
            'user_type' => User::class,
            'status' => ConversationStatus::COMPLETED,
        ]);

        $count = $this->conversationService->getActiveConversationsCount($user->id, User::class);

        $this->assertEquals(2, $count);
    }

    /** @test */
    public function it_can_get_recent_conversations()
    {
        $user = User::factory()->create();

        // Create conversations with different last_message_at times
        Conversation::factory()->create([
            'user_id' => $user->id,
            'user_type' => User::class,
            'status' => ConversationStatus::ACTIVE,
            'last_message_at' => now()->subHours(1),
        ]);

        Conversation::factory()->create([
            'user_id' => $user->id,
            'user_type' => User::class,
            'status' => ConversationStatus::ACTIVE,
            'last_message_at' => now()->subHours(2),
        ]);

        $recentConversations = $this->conversationService->getRecentConversations(
            $user->id,
            User::class,
            5
        );

        $this->assertCount(2, $recentConversations);
        // Should be ordered by last_message_at desc
        $this->assertTrue(
            $recentConversations[0]->last_message_at->gt($recentConversations[1]->last_message_at)
        );
    }

    /** @test */
    public function it_can_search_conversations()
    {
        $user = User::factory()->create();

        $conversation1 = Conversation::factory()->create([
            'title' => 'Customer Support Issue',
            'user_id' => $user->id,
            'user_type' => User::class,
        ]);

        $conversation2 = Conversation::factory()->create([
            'title' => 'Technical Question',
            'user_id' => $user->id,
            'user_type' => User::class,
        ]);

        // Create a message with searchable content
        Message::factory()->create([
            'conversation_id' => $conversation2->id,
            'content' => 'This is about customer support',
        ]);

        $results = $this->conversationService->searchConversations(
            'customer support',
            $user->id,
            User::class,
            10
        );

        $this->assertCount(2, $results->items());
    }
}
