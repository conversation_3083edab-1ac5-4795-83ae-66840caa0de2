<?php

namespace Modules\ChatBot\Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use Modules\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Enums\BotType;
use Modules\ChatBot\Enums\BotVisibility;
use Modules\ChatBot\Enums\ToolCallingMode;
use Modules\ChatBot\Models\Bot;
use Modules\ModelAI\Models\ModelAI;
use Modules\User\Models\User;

class BotSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first AI model and user for seeding
        $aiModel = ModelAI::first();
        $user = User::first();

        // Get first organization for organization bot
        $organization = \Modules\Organization\Models\Organization::first();

        if (!$aiModel || !$user) {
            $this->command->warn('Skipping bot seeding: No AI models or users found. Please seed ModelAI and User modules first.');
            return;
        }

        if (!$organization) {
            $this->command->warn('No organization found for organization bot. Will create organization bot with user owner.');
        }

        $bots = [
            [
                'uuid' => Str::uuid(),
                'name' => 'Customer Support Assistant',
                'logo' => null,
                'description' => 'A helpful AI assistant for customer support inquiries, capable of handling common questions and routing complex issues to human agents.',
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'model_ai_id' => $aiModel->id,
                'system_prompt' => 'You are a helpful customer support assistant. Your role is to assist customers with their inquiries in a friendly, professional, and efficient manner. Always be polite, empathetic, and solution-oriented. If you cannot resolve an issue, guide the customer to the appropriate human support channel.',
                'greeting_message' => 'Xin chào! Tôi là trợ lý hỗ trợ khách hàng. Tôi có thể giúp gì cho bạn hôm nay?',
                'starter_messages' => [
                    'Tôi cần hỗ trợ về sản phẩm',
                    'Làm thế nào để liên hệ với bộ phận kỹ thuật?',
                    'Tôi muốn khiếu nại về dịch vụ',
                    'Hướng dẫn sử dụng tính năng'
                ],
                'closing_message' => 'Cảm ơn bạn đã sử dụng dịch vụ hỗ trợ. Chúc bạn một ngày tốt lành!',
                'parameters' => [
                    'temperature' => 0.7,
                    'max_tokens' => 1000,
                    'top_p' => 0.9,
                ],
                'tool_calling_mode' => ToolCallingMode::Auto,
                'visibility' => BotVisibility::PRIVATE,
                'bot_type' => BotType::PERSONAL,
                'is_shareable' => true,
                'status' => BotStatus::Active,
                'metadata' => [
                    'category' => 'customer_support',
                    'tags' => ['support', 'customer_service', 'help'],
                    'version' => '1.0',
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Content Writer Bot',
                'logo' => null,
                'description' => 'An AI assistant specialized in creating high-quality content including blog posts, articles, and marketing copy.',
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'model_ai_id' => $aiModel->id,
                'system_prompt' => 'You are a professional content writer with expertise in creating engaging, well-structured, and SEO-friendly content. Focus on clarity, readability, and value for the target audience. Always maintain a consistent tone and style appropriate for the content type.',
                'greeting_message' => 'Xin chào! Tôi là trợ lý viết nội dung. Hãy cùng tạo ra nội dung tuyệt vời!',
                'starter_messages' => [
                    'Giúp tôi viết bài blog',
                    'Tạo nội dung marketing',
                    'Viết bài về...',
                    'Cải thiện nội dung của tôi',
                    'Tạo tiêu đề hấp dẫn',
                    'Viết mô tả sản phẩm'
                ],
                'closing_message' => 'Rất vui được làm việc với bạn! Nội dung của bạn trông tuyệt vời.',
                'parameters' => [
                    'temperature' => 0.7,
                    'max_tokens' => 1000,
                    'top_p' => 0.95,
                ],
                'tool_calling_mode' => ToolCallingMode::None,
                'visibility' => BotVisibility::PRIVATE,
                'bot_type' => BotType::PERSONAL,
                'is_shareable' => true,
                'status' => BotStatus::Active,
                'metadata' => [
                    'category' => 'content_creation',
                    'tags' => ['writing', 'content', 'marketing', 'seo'],
                    'version' => '1.0',
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Code Review Assistant',
                'logo' => null,
                'description' => 'An AI assistant that helps with code reviews, bug detection, and programming best practices across multiple languages.',
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'model_ai_id' => $aiModel->id,
                'system_prompt' => 'You are an experienced software engineer specializing in code review and quality assurance. Analyze code for bugs, security issues, performance problems, and adherence to best practices. Provide constructive feedback with specific suggestions for improvement.',
                'greeting_message' => 'Xin chào! Tôi ở đây để giúp bạn review code và cải thiện chất lượng.',
                'starter_messages' => [
                    'Review code này giúp tôi',
                    'Kiểm tra lỗi bảo mật',
                    'Tối ưu hàm này',
                    'Tìm lỗi trong code',
                    'Phân tích performance',
                    'Kiểm tra best practices'
                ],
                'closing_message' => 'Code của bạn trông tốt hơn nhiều rồi! Tiếp tục phát huy nhé.',
                'parameters' => [
                    'temperature' => 0.3,
                    'max_tokens' => 1000,
                    'top_p' => 0.8,
                ],
                'tool_calling_mode' => ToolCallingMode::Auto,
                'visibility' => BotVisibility::PRIVATE,
                'bot_type' => BotType::PERSONAL,
                'is_shareable' => false,
                'status' => BotStatus::Active,
                'metadata' => [
                    'category' => 'development',
                    'tags' => ['code_review', 'programming', 'debugging', 'quality_assurance'],
                    'version' => '1.0',
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Data Analysis Bot',
                'logo' => null,
                'description' => 'An AI assistant for data analysis, visualization, and insights generation from various data sources.',
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'model_ai_id' => $aiModel->id,
                'system_prompt' => 'You are a data analyst with expertise in statistical analysis, data visualization, and business intelligence. Help users understand their data through clear explanations, identify patterns and trends, and provide actionable insights.',
                'greeting_message' => 'Xin chào! Tôi là trợ lý phân tích dữ liệu. Hãy cùng khám phá dữ liệu của bạn!',
                'starter_messages' => [
                    'Phân tích bộ dữ liệu này',
                    'Tạo biểu đồ trực quan',
                    'Tìm mẫu trong dữ liệu',
                    'Tạo báo cáo thông tin',
                    'Thống kê mô tả',
                    'Dự đoán xu hướng'
                ],
                'closing_message' => 'Phân tích tuyệt vời! Thông tin từ dữ liệu đã sẵn sàng.',
                'parameters' => [
                    'temperature' => 0.5,
                    'max_tokens' => 1000,
                    'top_p' => 0.9,
                ],
                'tool_calling_mode' => ToolCallingMode::Required,
                'visibility' => BotVisibility::PRIVATE,
                'bot_type' => BotType::PERSONAL,
                'is_shareable' => false,
                'status' => BotStatus::Review,
                'metadata' => [
                    'category' => 'analytics',
                    'tags' => ['data_analysis', 'statistics', 'visualization', 'insights'],
                    'version' => '1.0',
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Language Tutor',
                'logo' => null,
                'description' => 'An AI language learning assistant that helps users practice and improve their language skills through conversation and exercises.',
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'model_ai_id' => $aiModel->id,
                'system_prompt' => 'You are a friendly and patient language tutor. Help users learn and practice languages through conversation, grammar explanations, vocabulary building, and cultural insights. Adapt your teaching style to the user\'s proficiency level.',
                'greeting_message' => 'Xin chào! Tôi là gia sư ngôn ngữ. Sẵn sàng thực hành và học cùng nhau chưa?',
                'starter_messages' => [
                    'Giúp tôi luyện tiếng Anh',
                    'Giải thích quy tắc ngữ pháp',
                    'Hãy trò chuyện',
                    'Dạy tôi từ vựng mới',
                    'Kiểm tra phát âm',
                    'Luyện tập nghe'
                ],
                'closing_message' => 'Buổi luyện tập tuyệt vời! Hãy tiếp tục phát huy!',
                'parameters' => [
                    'temperature' => 0.7,
                    'max_tokens' => 1000,
                    'top_p' => 0.9,
                ],
                'tool_calling_mode' => ToolCallingMode::None,
                'visibility' => BotVisibility::PRIVATE,
                'bot_type' => BotType::PERSONAL,
                'is_shareable' => true,
                'status' => BotStatus::Draft,
                'metadata' => [
                    'category' => 'education',
                    'tags' => ['language_learning', 'education', 'conversation', 'tutoring'],
                    'version' => '1.0',
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Public Marketing Assistant',
                'logo' => null,
                'description' => 'A public marketing assistant bot that helps with marketing strategies, campaign planning, and content marketing. Available for everyone to use.',
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'model_ai_id' => $aiModel->id,
                'system_prompt' => 'You are a marketing expert with deep knowledge in digital marketing, content strategy, and campaign management. Help users create effective marketing strategies, analyze market trends, and develop compelling marketing content.',
                'greeting_message' => 'Xin chào! Tôi là trợ lý marketing. Hãy cùng tạo ra những chiến lược marketing tuyệt vời!',
                'starter_messages' => [
                    'Tạo chiến lược marketing',
                    'Phân tích xu hướng thị trường',
                    'Lập kế hoạch chiến dịch social media',
                    'Viết nội dung marketing',
                    'Phân tích đối thủ cạnh tranh',
                    'Tối ưu hóa ROI'
                ],
                'closing_message' => 'Chiến lược marketing tuyệt vời! Chiến dịch của bạn đã sẵn sàng triển khai.',
                'parameters' => [
                    'temperature' => 0.7,
                    'max_tokens' => 1000,
                    'top_p' => 0.9,
                ],
                'tool_calling_mode' => ToolCallingMode::Auto,
                'visibility' => BotVisibility::PUBLIC,
                'bot_type' => BotType::PERSONAL,
                'is_shareable' => true,
                'status' => BotStatus::Active,
                'metadata' => [
                    'category' => 'marketing',
                    'tags' => ['marketing', 'strategy', 'campaigns', 'public'],
                    'version' => '1.0',
                    'featured' => true,
                ],
            ],
            [
                'uuid' => Str::uuid(),
                'name' => 'Organization HR Bot',
                'logo' => null,
                'description' => 'An organization-level HR assistant for managing employee queries, policies, and HR processes. Designed for internal company use.',
                'owner_id' => $organization ? $organization->id : $user->id,
                'owner_type' => $organization ? get_class($organization) : get_class($user),
                'model_ai_id' => $aiModel->id,
                'system_prompt' => 'You are an HR assistant specialized in human resources management, employee relations, and company policies. Help with HR queries, policy explanations, and employee support while maintaining confidentiality and professionalism.',
                'greeting_message' => 'Xin chào! Tôi là trợ lý HR. Tôi có thể giúp gì cho bạn về vấn đề nhân sự hôm nay?',
                'starter_messages' => [
                    'Giải thích chính sách công ty',
                    'Hỗ trợ đơn xin nghỉ phép',
                    'Hướng dẫn đánh giá hiệu suất',
                    'Thông tin phúc lợi',
                    'Quy trình tuyển dụng',
                    'Hỗ trợ nhân viên mới'
                ],
                'closing_message' => 'Hy vọng tôi đã giúp được bạn với câu hỏi HR. Chúc bạn một ngày tốt lành!',
                'parameters' => [
                    'temperature' => 0.5,
                    'max_tokens' => 1000,
                    'top_p' => 0.8,
                ],
                'tool_calling_mode' => ToolCallingMode::None,
                'visibility' => BotVisibility::PRIVATE,
                'bot_type' => BotType::ORGANIZATION,
                'is_shareable' => true, // Organization bots can now be shared
                'status' => BotStatus::Active,
                'metadata' => [
                    'category' => 'hr',
                    'tags' => ['hr', 'organization', 'policies', 'internal'],
                    'version' => '1.0',
                    'department' => 'human_resources',
                ],
            ],
        ];

        foreach ($bots as $botData) {
            Bot::create($botData);
        }

        $this->command->info('Successfully seeded ' . count($bots) . ' bots.');
    }
}
