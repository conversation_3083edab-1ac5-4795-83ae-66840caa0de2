<?php

namespace Modules\ChatBot\Http\Requests;

use Illuminate\Validation\Rule;
use Mo<PERSON>les\ChatBot\Enums\ConversationStatus;
use Modules\Core\Http\Requests\BaseFormRequest;

class ConversationRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => ['nullable', 'string', 'max:255'],
            'bot' => ['required', 'string', 'exists:bots,uuid'],
            'uuid' => ['nullable', 'string', 'exists:conversations,uuid']
        ];
    }
}
