# ChatBot Module Tests

Comprehensive test suite for the ChatBot module including unit tests and integration tests.

## Test Structure

```
tests/
├── Unit/                           # Unit tests
│   ├── Models/                     # Model tests
│   │   ├── ConversationTest.php    # Conversation model tests
│   │   └── MessageTest.php         # Message model tests
│   └── Services/                   # Service tests
│       ├── ConversationServiceTest.php
│       └── MessageServiceTest.php
├── Feature/                        # Integration tests
│   ├── ChatBotTestCase.php         # Base test case
│   ├── ConversationApiTest.php     # Conversation API tests
│   ├── MessageApiTest.php          # Message API tests
│   ├── AIIntegrationTest.php       # AI integration tests

│   └── ToolCallingIntegrationTest.php # Tool calling tests
├── TestSuite.php                   # Test suite organizer
└── README.md                       # This file
```

## Running Tests

### Using the Test Runner Script

Make the script executable:
```bash
chmod +x run-tests.sh
```

Run all tests:
```bash
./run-tests.sh
```

Run specific test suites:
```bash
./run-tests.sh -s unit           # Unit tests only
./run-tests.sh -s feature        # Integration tests only
./run-tests.sh -s api            # API tests only
./run-tests.sh -s ai             # AI-related tests only
```

Run with coverage:
```bash
./run-tests.sh -c               # Generate coverage report
./run-tests.sh -s unit -c       # Unit tests with coverage
```

Run with filters:
```bash
./run-tests.sh -f ConversationTest    # Tests matching name
./run-tests.sh -f "test_can_create"   # Tests matching method
```

Verbose output:
```bash
./run-tests.sh -v               # Verbose output
./run-tests.sh -s api -v -c     # API tests, verbose, with coverage
```

### Using PHPUnit Directly

From the module directory:
```bash
../../vendor/bin/phpunit --configuration phpunit.xml
```

Run specific test suites:
```bash
../../vendor/bin/phpunit --testsuite "ChatBot Unit Tests"
../../vendor/bin/phpunit --testsuite "ChatBot Feature Tests"
```

Run specific test files:
```bash
../../vendor/bin/phpunit tests/Unit/Models/ConversationTest.php
../../vendor/bin/phpunit tests/Feature/ConversationApiTest.php
```

### Using Laravel Artisan

From the project root:
```bash
php artisan test Modules/ChatBot/tests
php artisan test Modules/ChatBot/tests/Unit
php artisan test Modules/ChatBot/tests/Feature
```

## Test Categories

### Unit Tests

Test individual components in isolation:

- **Model Tests**: Test Eloquent models, relationships, scopes, and methods
- **Service Tests**: Test business logic services with mocked dependencies

### Integration Tests

Test complete workflows and API endpoints:

- **API Tests**: Test REST API endpoints with real HTTP requests
- **AI Integration Tests**: Test AI service integration with mocked API responses

- **Tool Calling Tests**: Test tool execution workflows

## Test Environment

### Database

Tests use SQLite in-memory database for fast execution:
```php
'DB_CONNECTION' => 'testing'
'DB_DATABASE' => ':memory:'
```

### HTTP Mocking

AI API calls are mocked using Laravel HTTP fake:
```php
Http::fake([
    'api.openai.com/*' => Http::response([...], 200),
    'api.anthropic.com/*' => Http::response([...], 200),
]);
```

### Test Data

- Factories create realistic test data
- Base test case sets up common test objects
- Each test is isolated with fresh database

## Writing Tests

### Unit Test Example

```php
/** @test */
public function it_can_create_a_conversation()
{
    $user = User::factory()->create();
    $bot = Bot::factory()->create();

    $conversation = Conversation::factory()->create([
        'bot_id' => $bot->id,
        'user_id' => $user->id,
        'user_type' => User::class,
    ]);

    $this->assertInstanceOf(Conversation::class, $conversation);
    $this->assertEquals($bot->id, $conversation->bot_id);
}
```

### Integration Test Example

```php
/** @test */
public function it_can_create_conversation_via_api()
{
    $this->authenticateUser();

    $data = [
        'title' => 'Test Chat',
        'bot_id' => $this->bot->id,
        'user_id' => $this->user->id,
        'user_type' => User::class,
    ];

    $response = $this->postJson('/api/v1/conversations', $data);

    $this->assertApiResponse($response, 201);
    $this->assertDatabaseHas('conversations', ['title' => 'Test Chat']);
}
```

## Test Helpers

### Base Test Case

`ChatBotTestCase` provides:
- Pre-configured test data (user, bot, conversation)
- AI service mocking
- Common assertion methods
- Authentication helpers

### Custom Assertions

```php
$this->assertApiResponse($response, 200);           // Check API response
$this->assertValidationError($response, 'field');   // Check validation
$this->assertDatabaseHasMessage($attributes);       // Check message exists
$this->assertDatabaseHasConversation($attributes);  // Check conversation exists
```

### Test Data Creation

```php
$conversation = $this->createTestConversation(['title' => 'Test']);
$message = $this->createTestMessage(['content' => 'Hello']);
```

## Coverage Reports

Generate HTML coverage report:
```bash
./run-tests.sh -c
```

View coverage:
```bash
open ../../storage/logs/coverage/index.html
```

Coverage targets:
- **Models**: 95%+ coverage
- **Services**: 90%+ coverage
- **Controllers**: 85%+ coverage
- **Overall**: 85%+ coverage

## Continuous Integration

### GitHub Actions Example

```yaml
name: ChatBot Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.2
        extensions: mbstring, xml, ctype, json, openssl, pdo, sqlite
        
    - name: Install dependencies
      run: composer install
      
    - name: Run ChatBot tests
      run: |
        cd Modules/ChatBot
        chmod +x run-tests.sh
        ./run-tests.sh -c
        
    - name: Upload coverage
      uses: codecov/codecov-action@v1
      with:
        file: ./storage/logs/coverage.xml
```

## Performance Testing

For performance testing of AI integrations:

```bash
# Run with timing
./run-tests.sh -s ai -v

# Profile memory usage
php -d memory_limit=512M ../../vendor/bin/phpunit tests/Feature/AIIntegrationTest.php
```

## Debugging Tests

Enable debug output:
```bash
./run-tests.sh -v                    # Verbose output
./run-tests.sh -s unit --debug       # Debug mode
```

View logs:
```bash
tail -f ../../storage/logs/laravel.log
```

## Best Practices

1. **Test Naming**: Use descriptive test method names starting with `it_`
2. **Isolation**: Each test should be independent and not rely on other tests
3. **Mocking**: Mock external services (AI APIs, tools) for consistent results
4. **Assertions**: Use specific assertions and test both success and failure cases
5. **Data**: Use factories for test data creation
6. **Coverage**: Aim for high coverage but focus on meaningful tests
7. **Performance**: Keep tests fast by using in-memory database and mocking

## Troubleshooting

### Common Issues

1. **Database errors**: Ensure migrations are run in test environment
2. **HTTP mocking**: Clear HTTP fakes between tests
3. **Memory issues**: Use `RefreshDatabase` trait properly
4. **Timing issues**: Mock time-dependent operations

### Debug Commands

```bash
# Check test configuration
../../vendor/bin/phpunit --configuration phpunit.xml --list-tests

# Run single test with debug
../../vendor/bin/phpunit tests/Unit/Models/ConversationTest.php::it_can_create_a_conversation --debug

# Check coverage without running tests
../../vendor/bin/phpunit --coverage-text --coverage-filter app/
```
