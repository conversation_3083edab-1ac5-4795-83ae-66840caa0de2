<?php

namespace Modules\ChatBot\Services;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Enums\BotStatus;
use Modules\Organization\Models\Organization;

class ChatService
{
    /**
     * Cache tag for bots
     */
    private const CACHE_TAG = 'chats';

    /**
     * Get all active bots accessible by the current user.
     * This includes:
     * - Bots owned by the user
     * - Bots shared directly with the user
     * - Bots from organizations where user is a confirmed member
     *
     * Note: Users with pending invitations (not yet accepted) cannot see organization bots.
     * Only after accepting invitation and becoming a member can they access bots.
     */
    public function getActiveChatBots(): Collection
    {
        if (!enabledCache()) {
            return $this->getAccessibleBots();
        }

        return Cache::tags(self::CACHE_TAG)->remember(
            key: 'bots.active.list.user.' . auth()->id(),
            ttl: cacheTTL(),
            callback: fn() => $this->getAccessibleBots()
        );
    }

    /**
     * Get all bots accessible by the current user.
     * Only returns ACTIVE bots - excludes draft, review, paused, banned status.
     */
    private function getAccessibleBots(): Collection
    {
        $user = auth()->user();
        $userId = $user->id;
        $userClass = get_class($user);

        return Bot::query()
            ->with(['aiModel:id,key,name', 'owner',
                'conversations' => function ($query) {
                    $query->select(['id', 'uuid', 'bot_id', 'title', 'last_message_at'])
                        ->forOwner(auth()->id(), get_class(auth()->user()))
                        ->active()
                        ->orderBy('updated_at', 'desc')
                        ->limit(2);
                }
            ])
            ->where(function ($query) use ($userId, $userClass) {
                // 1. Bots owned by the user
                $query->where(function ($q) use ($userId, $userClass) {
                    $q->where('owner_id', $userId)
                        ->where('owner_type', $userClass);
                })
                    // 2. Bots shared directly with the user
                    ->orWhereHas('shares', function ($q) use ($userId) {
                        $q->where('user_id', $userId)
                            ->where('status', 'active');
                    })
                    // 3. Bots from organizations where user is a member
                    // Uses whereExists to properly link bot.owner_id with organization.id
                    ->orWhere(function ($q) use ($userId) {
                        $q->where('owner_type', 'Modules\\Organization\\Models\\Organization')
                            ->whereExists(function ($subQuery) use ($userId) {
                                $subQuery->select(DB::raw(1))
                                    ->from('organization_members')
                                    ->whereColumn('organization_members.organization_id', 'bots.owner_id')
                                    ->where('organization_members.user_id', $userId);
                            });
                    });
            })
            ->where('status', BotStatus::Active) // Only active bots, exclude draft/review/paused/banned
            ->orderByDesc('updated_at')
            ->get()
            ->map(function (Bot $bot) {
                $bot->aiModel?->makeHidden(['id']);
                $this->hideOwnerSensitiveData($bot->owner);

                // Mark personal bots that are shared with current user as 'shared'
                if ($bot->bot_type->value == 'personal' && $bot->owner_id != auth()->id()) {
                    $bot->bot_type = 'shared';
                }

                // Let model's $hidden array handle all sensitive information
                return $bot;
            });
    }

    /**
     * Hide sensitive data from owner based on owner type.
     */
    private function hideOwnerSensitiveData($owner): void
    {
        if (!$owner) {
            return;
        }

        // Check if owner is User or Organization
        if ($owner instanceof \Modules\User\Models\User) {
            // For User owners, hide personal sensitive data
            $owner->makeHidden([
                'id', 'username', 'email', 'email_verified_at', 'phone',
                'phone_verified_at', 'birthday', 'gender', 'address',
                'status', 'last_login_at', 'last_login_ip', 'preferences',
                'is_verified', 'newsletter_subscribed', 'created_at',
                'updated_at'
            ]);
        } elseif ($owner instanceof \Modules\Organization\Models\Organization) {
            // For Organization owners, hide organizational sensitive data
            $owner->makeHidden([
                'id', 'parent_id', 'owner_id', 'geo_division_id', 'country_id',
                'postal_code', 'path', 'depth', 'sort_order', 'settings',
                'created_at', 'updated_at', 'deleted_at'
            ]);
        }
    }

}
