<?php

namespace Modules\ChatBot\Observers;

use Illuminate\Support\Facades\Cache;
use Mo<PERSON>les\ChatBot\Models\Conversation;

class ConversationObserver
{
    /**
     * Cache tags for bot shares
     */
    private const CACHE_TAGS = ['bot-shares', 'bots'];

    /**
     * Handle the Conversation "created" event.
     */
    public function created(Conversation $conversation): void
    {
        $this->clearCache($conversation);
    }

    /**
     * Handle the Conversation "updated" event.
     */
    public function updated(Conversation $conversation): void
    {
        $this->clearCache($conversation);
    }

    /**
     * Handle the Conversation "deleted" event.
     */
    public function deleted(Conversation $conversation): void
    {
        $this->clearCache($conversation);
    }

    /**
     * Handle the Conversation "restored" event.
     */
    public function restored(Conversation $conversation): void
    {
        $this->clearCache($conversation);
    }

    /**
     * Handle the Conversation "force deleted" event.
     */
    public function forceDeleted(Conversation $conversation): void
    {
        $this->clearCache($conversation);
    }

    /**
     * Clear bot share related cache.
     */
    private function clearCache(Conversation $conversation): void
    {
        try {
            Cache::tags(['chats'])->forget("bots.active.list.user.".auth()->id());
        } catch (\Exception $e) {
            // Silently handle cache errors
            \Log::warning('Failed to clear Conversation cache', [
                'bot_share_id' => $conversation->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
