<?php

namespace Modules\ChatBot\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Enums\BotVisibility;
use Modules\ChatBot\Enums\BotType;
use Modules\ChatBot\Enums\ToolCallingMode;
use Modules\ChatBot\Models\Bot;
use Modules\ModelAI\Models\ModelAI;
use Modules\User\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\ChatBot\Models\Bot>
 */
class BotFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Bot::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'uuid' => Str::uuid(),
            'name' => $this->faker->words(3, true) . ' Bot',
            'description' => $this->faker->paragraph(),
            'owner_id' => User::factory(),
            'owner_type' => User::class,
            'model_ai_id' => ModelAI::factory(),
            'system_prompt' => $this->faker->paragraphs(2, true),
            'parameters' => [
                'temperature' => $this->faker->randomFloat(2, 0, 0.7),
                'max_tokens' => $this->faker->numberBetween(500, 1000),
                'top_p' => $this->faker->randomFloat(2, 0, 1),
            ],
            'tool_calling_mode' => $this->faker->randomElement(ToolCallingMode::cases()),
            'status' => $this->faker->randomElement(BotStatus::cases()),
            'visibility' => $this->faker->randomElement(BotVisibility::cases()),
            'bot_type' => $this->faker->randomElement(BotType::cases()),
            'is_shareable' => $this->faker->boolean(70), // 70% chance of being shareable
            'metadata' => [
                'category' => $this->faker->randomElement(['support', 'content', 'development', 'analytics', 'education']),
                'tags' => $this->faker->words(3),
                'version' => '1.0',
            ],
        ];
    }

    /**
     * Indicate that the bot is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BotStatus::Active,
        ]);
    }

    /**
     * Indicate that the bot is in draft status.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BotStatus::Draft,
        ]);
    }

    /**
     * Indicate that the bot is under review.
     */
    public function review(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BotStatus::Review,
        ]);
    }

    /**
     * Indicate that the bot is paused.
     */
    public function paused(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BotStatus::Paused,
        ]);
    }

    /**
     * Indicate that the bot is banned.
     */
    public function banned(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BotStatus::Banned,
        ]);
    }

    /**
     * Indicate that the bot uses auto tool calling mode.
     */
    public function autoToolCalling(): static
    {
        return $this->state(fn (array $attributes) => [
            'tool_calling_mode' => ToolCallingMode::Auto,
        ]);
    }

    /**
     * Indicate that the bot uses no tool calling.
     */
    public function noToolCalling(): static
    {
        return $this->state(fn (array $attributes) => [
            'tool_calling_mode' => ToolCallingMode::None,
        ]);
    }

    /**
     * Indicate that the bot requires tool calling.
     */
    public function requiredToolCalling(): static
    {
        return $this->state(fn (array $attributes) => [
            'tool_calling_mode' => ToolCallingMode::Required,
        ]);
    }

    /**
     * Indicate that the bot is owned by a specific user.
     */
    public function ownedBy(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'owner_id' => $user->id,
            'owner_type' => get_class($user),
        ]);
    }

    /**
     * Indicate that the bot uses a specific AI model.
     */
    public function withAiModel(ModelAI $model): static
    {
        return $this->state(fn (array $attributes) => [
            'ai_model_id' => $model->id,
        ]);
    }

    /**
     * Indicate that the bot is for customer support.
     */
    public function customerSupport(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Customer Support Bot',
            'description' => 'A helpful AI assistant for customer support inquiries.',
            'system_prompt' => 'You are a helpful customer support assistant. Always be polite and professional.',
            'parameters' => [
                'temperature' => 0.7,
                'max_tokens' => 1000,
                'top_p' => 0.9,
            ],
            'tool_calling_mode' => ToolCallingMode::Auto,
            'metadata' => [
                'category' => 'support',
                'tags' => ['customer_service', 'support', 'help'],
                'version' => '1.0',
            ],
        ]);
    }

    /**
     * Indicate that the bot is for content creation.
     */
    public function contentCreator(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Content Creator Bot',
            'description' => 'An AI assistant specialized in creating high-quality content.',
            'system_prompt' => 'You are a professional content writer. Create engaging and well-structured content.',
            'parameters' => [
                'temperature' => 0.7,
                'max_tokens' => 1000,
                'top_p' => 0.95,
            ],
            'tool_calling_mode' => ToolCallingMode::None,
            'metadata' => [
                'category' => 'content',
                'tags' => ['writing', 'content', 'marketing'],
                'version' => '1.0',
            ],
        ]);
    }

    /**
     * Indicate that the bot is for code assistance.
     */
    public function codeAssistant(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Code Assistant Bot',
            'description' => 'An AI assistant that helps with programming and code review.',
            'system_prompt' => 'You are an experienced software engineer. Help with code review and programming best practices.',
            'parameters' => [
                'temperature' => 0.3,
                'max_tokens' => 1000,
                'top_p' => 0.8,
            ],
            'tool_calling_mode' => ToolCallingMode::Auto,
            'metadata' => [
                'category' => 'development',
                'tags' => ['programming', 'code_review', 'development'],
                'version' => '1.0',
            ],
        ]);
    }

    /**
     * Indicate that the bot has minimal parameters.
     */
    public function withMinimalParameters(): static
    {
        return $this->state(fn (array $attributes) => [
            'parameters' => [
                'temperature' => 0.7,
                'max_tokens' => 1000,
            ],
        ]);
    }

    /**
     * Indicate that the bot has no parameters.
     */
    public function withoutParameters(): static
    {
        return $this->state(fn (array $attributes) => [
            'parameters' => null,
        ]);
    }

    /**
     * Indicate that the bot has no metadata.
     */
    public function withoutMetadata(): static
    {
        return $this->state(fn (array $attributes) => [
            'metadata' => null,
        ]);
    }

    /**
     * Indicate that the bot is public.
     */
    public function publicVisibility(): static
    {
        return $this->state(fn (array $attributes) => [
            'visibility' => BotVisibility::PUBLIC,
        ]);
    }

    /**
     * Indicate that the bot is private.
     */
    public function privateVisibility(): static
    {
        return $this->state(fn (array $attributes) => [
            'visibility' => BotVisibility::PRIVATE,
        ]);
    }

    /**
     * Indicate that the bot is personal type.
     */
    public function personal(): static
    {
        return $this->state(fn (array $attributes) => [
            'bot_type' => BotType::PERSONAL,
            'is_shareable' => true,
        ]);
    }

    /**
     * Indicate that the bot is organization type.
     */
    public function organization(): static
    {
        return $this->state(fn (array $attributes) => [
            'bot_type' => BotType::ORGANIZATION,
            'is_shareable' => false,
        ]);
    }

    /**
     * Indicate that the bot is shareable.
     */
    public function shareable(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_shareable' => true,
            'bot_type' => BotType::PERSONAL,
        ]);
    }

    /**
     * Indicate that the bot is not shareable.
     */
    public function notShareable(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_shareable' => false,
        ]);
    }
}
