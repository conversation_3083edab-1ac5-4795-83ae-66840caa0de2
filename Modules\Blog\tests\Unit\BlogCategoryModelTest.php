<?php

namespace Modules\Blog\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Models\BlogCategoryTranslation;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Enums\BlogStatus;
use Modules\User\Models\User;

class BlogCategoryModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Blog module
        $this->artisan('migrate', ['--path' => 'Modules/Blog/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_has_correct_fillable_attributes()
    {
        $category = new BlogCategory();
        $expected = [
            'parent_id',
            'layout',
            'path',
            'status',
            'depth'
        ];

        $this->assertEquals($expected, $category->getFillable());
    }

    #[Test]
    public function it_has_correct_casts()
    {
        $category = new BlogCategory();
        $expected = [
            'id' => 'int',
            'parent_id' => 'integer',
            'depth' => 'integer',
            'status' => BlogStatus::class,
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];

        $this->assertEquals($expected, $category->getCasts());
    }

    #[Test]
    public function it_has_correct_hidden_attributes()
    {
        $category = new BlogCategory();
        $expected = ['deleted_at'];

        $this->assertEquals($expected, $category->getHidden());
    }

    #[Test]
    public function it_can_create_a_category()
    {
        $categoryData = [
            'parent_id' => null,
            'layout' => 'default',
            'path' => '/technology',
            'depth' => 0,
            'status' => BlogStatus::Active,
        ];

        $category = BlogCategory::create($categoryData);

        $this->assertInstanceOf(BlogCategory::class, $category);
        $this->assertDatabaseHas('blog_categories', [
            'id' => $category->id,
            'status' => BlogStatus::Active->value,
            'depth' => 0,
        ]);
        $this->assertEquals(BlogStatus::Active, $category->status);
    }

    #[Test]
    public function it_has_translations_relationship()
    {
        $category = BlogCategory::factory()->create();
        $translation = BlogCategoryTranslation::factory()->create(['category_id' => $category->id]);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $category->translations);
        $this->assertTrue($category->translations->contains($translation));
    }

    #[Test]
    public function it_has_parent_relationship()
    {
        $parent = BlogCategory::factory()->create();
        $child = BlogCategory::factory()->create(['parent_id' => $parent->id]);

        $this->assertInstanceOf(BlogCategory::class, $child->parent);
        $this->assertEquals($parent->id, $child->parent->id);
    }

    #[Test]
    public function it_has_children_relationship()
    {
        $parent = BlogCategory::factory()->create();
        $child1 = BlogCategory::factory()->create(['parent_id' => $parent->id]);
        $child2 = BlogCategory::factory()->create(['parent_id' => $parent->id]);

        $children = $parent->children;
        
        $this->assertCount(2, $children);
        $this->assertTrue($children->contains('id', $child1->id));
        $this->assertTrue($children->contains('id', $child2->id));
    }

    #[Test]
    public function it_has_posts_relationship()
    {
        $user = User::factory()->create();
        $category = BlogCategory::factory()->create();
        $post1 = BlogPost::factory()->create(['author_id' => $user->id]);
        $post2 = BlogPost::factory()->create(['author_id' => $user->id]);

        $category->posts()->attach([$post1->id, $post2->id]);

        $posts = $category->posts;
        
        $this->assertCount(2, $posts);
        $this->assertTrue($posts->contains('id', $post1->id));
        $this->assertTrue($posts->contains('id', $post2->id));
    }

    #[Test]
    public function it_can_scope_active_categories()
    {
        $activeCategory = BlogCategory::factory()->create(['status' => BlogStatus::Active]);
        $inactiveCategory = BlogCategory::factory()->create(['status' => BlogStatus::Inactive]);
        $draftCategory = BlogCategory::factory()->create(['status' => BlogStatus::Draft]);

        $activeCategories = BlogCategory::active()->get();

        $this->assertTrue($activeCategories->contains($activeCategory));
        $this->assertFalse($activeCategories->contains($inactiveCategory));
        $this->assertFalse($activeCategories->contains($draftCategory));
    }

    #[Test]
    public function it_can_scope_root_categories()
    {
        $root1 = BlogCategory::factory()->create(['parent_id' => null, 'status' => BlogStatus::Active]);
        $root2 = BlogCategory::factory()->create(['parent_id' => null, 'status' => BlogStatus::Active]);
        $child = BlogCategory::factory()->create(['parent_id' => $root1->id, 'status' => BlogStatus::Active]);

        // Create translations
        BlogCategoryTranslation::factory()->create(['category_id' => $root1->id, 'locale' => 'en']);
        BlogCategoryTranslation::factory()->create(['category_id' => $root2->id, 'locale' => 'en']);
        BlogCategoryTranslation::factory()->create(['category_id' => $child->id, 'locale' => 'en']);

        $rootCategories = BlogCategory::getRootCategories('en');

        $this->assertCount(2, $rootCategories);
        $this->assertTrue($rootCategories->contains('id', $root1->id));
        $this->assertTrue($rootCategories->contains('id', $root2->id));
        $this->assertFalse($rootCategories->contains('id', $child->id));
    }

    #[Test]
    public function it_can_scope_by_slug()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active]);
        $translation = BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'slug' => 'test-category',
            'locale' => 'en',
            'name' => 'Test Category'
        ]);

        $foundCategory = BlogCategory::getCategoryBySlug('test-category', 'en');

        $this->assertNotNull($foundCategory);
        $this->assertEquals($category->id, $foundCategory->id);
    }

    #[Test]
    public function it_can_check_if_category_is_root()
    {
        $root = BlogCategory::factory()->create(['parent_id' => null]);
        $child = BlogCategory::factory()->create(['parent_id' => $root->id]);

        $this->assertTrue($root->isRoot());
        $this->assertFalse($child->isRoot());
    }

    #[Test]
    public function it_can_check_if_category_has_children()
    {
        $parent = BlogCategory::factory()->create();
        $childless = BlogCategory::factory()->create();
        BlogCategory::factory()->create(['parent_id' => $parent->id]);

        $this->assertTrue($parent->hasChildren());
        $this->assertFalse($childless->hasChildren());
    }

    #[Test]
    public function it_can_get_depth()
    {
        // Create parent categories to establish hierarchy
        $parent = BlogCategory::factory()->create(['parent_id' => null]);
        $child = BlogCategory::factory()->create(['parent_id' => $parent->id]);
        $grandchild = BlogCategory::factory()->create(['parent_id' => $child->id]);

        // Refresh to get updated depth values from boot method
        $parent->refresh();
        $child->refresh();
        $grandchild->refresh();

        $this->assertEquals(0, $parent->depth);
        $this->assertEquals(1, $child->depth);
        $this->assertEquals(2, $grandchild->depth);
    }

    #[Test]
    public function it_returns_zero_depth_when_null()
    {
        $category = BlogCategory::factory()->create(['depth' => null]);
        
        $this->assertEquals(0, $category->getDepth());
    }

    #[Test]
    public function it_can_get_name_for_specific_locale()
    {
        $category = BlogCategory::factory()->create();
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'en',
            'name' => 'Technology',
        ]);
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'vi',
            'name' => 'Công nghệ',
        ]);

        // Test accessing translation data directly
        $category->load(['translations' => fn($q) => $q->where('locale', 'en')]);
        $this->assertEquals('Technology', $category->translations->first()->name);
    }

    #[Test]
    public function it_can_get_slug_for_specific_locale()
    {
        $category = BlogCategory::factory()->create();
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'en',
            'slug' => 'technology',
        ]);
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'vi',
            'slug' => 'cong-nghe',
        ]);

        // Test accessing translation data directly
        $category->load(['translations' => fn($q) => $q->where('locale', 'en')]);
        $this->assertEquals('technology', $category->translations->first()->slug);
    }

    #[Test]
    public function it_has_correct_table_structure()
    {
        $this->assertTrue(Schema::hasTable('blog_categories'));

        $expectedColumns = [
            'id', 'parent_id', 'layout', 'path', 'depth', 'status',
            'created_at', 'updated_at', 'deleted_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertTrue(Schema::hasColumn('blog_categories', $column));
        }
    }

    #[Test]
    public function it_has_factory()
    {
        $category = BlogCategory::factory()->make();

        $this->assertInstanceOf(BlogCategory::class, $category);
        $this->assertNotEmpty($category->status);
    }

    #[Test]
    public function it_uses_soft_deletes()
    {
        $category = BlogCategory::factory()->create();
        $categoryId = $category->id;

        $category->delete();

        $this->assertSoftDeleted('blog_categories', ['id' => $categoryId]);
        $this->assertNotNull($category->fresh()->deleted_at);
    }

    #[Test]
    public function it_uses_translatable_trait()
    {
        $category = new BlogCategory();

        $this->assertContains('Astrotomic\Translatable\Translatable', class_uses_recursive($category));
    }

    #[Test]
    public function it_has_correct_translatable_attributes()
    {
        $category = new BlogCategory();
        $expected = [
            'name',
            'slug',
            'image',
            'description',
            'meta_title',
            'meta_description'
        ];

        $this->assertEquals($expected, $category->translatedAttributes);
    }
}
