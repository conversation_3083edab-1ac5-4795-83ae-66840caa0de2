<?php

namespace Modules\Core\Traits;
use Illuminate\Http\JsonResponse;
use Illuminate\Pagination\LengthAwarePaginator;

trait ResponseTrait
{
    /**
     * Generate a JSON response with a custom structure.
     *
     * @param mixed $data
     * @return JsonResponse
     */
    protected function rawResponse(mixed $data): JsonResponse
    {
        return response()->json($data);
    }

    /**
     * Generate a successful JSON response.
     *
     * @param mixed $data
     * @param string|null $message
     * @param int $code
     * @return JsonResponse
     */
    protected function successResponse(mixed $data, string $message = null, int $code = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message ?? __('Success'),
            'data' => $data,
        ], $code);
    }

    /**
     * Generate an error JSON response.
     *
     * @param mixed $data
     * @param string|null $message
     * @param int $code
     * @return JsonResponse
     */
    protected function errorResponse(mixed $data, string $message = null, int $code = 400): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message ?? __('Error'),
            'data' => $data
        ], $code);
    }

    /**
     * Generate a forbidden JSON response.
     *
     * @param mixed|null $data
     * @param string|null $message
     * @return JsonResponse
     */
    protected function notFoundResponse(mixed $data = null, string $message = null): JsonResponse
    {
        return $this->errorResponse($data, $message, 404);
    }

    /**
     * Generate a paginated JSON response.
     *
     * @param LengthAwarePaginator $paginator
     * @param string|null $message
     * @return JsonResponse
     */
    protected function paginatedResponse(LengthAwarePaginator $paginator,string $message = null): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message ?? __('Success'),
            'data' => $paginator->items(),
            'total' => $paginator->total(),
            'limit' => $paginator->perPage(),
        ]);
    }

    protected function jsonResponse(mixed $data): JsonResponse
    {
        return response()->json($data);
    }

    /**
     * Sanitize error message to prevent sensitive information exposure.
     */
    protected function sanitizeErrorMessage(\Exception $exception): string
    {
        // In production, return generic error messages
        if (app()->environment('production')) {
            return match(true) {
                $exception instanceof \Illuminate\Database\Eloquent\ModelNotFoundException => 'Resource not found',
                $exception instanceof \Illuminate\Validation\ValidationException => 'Validation failed',
                $exception instanceof \Illuminate\Auth\AuthenticationException => 'Authentication required',
                $exception instanceof \Illuminate\Auth\Access\AuthorizationException => 'Access denied',
                $exception instanceof \Symfony\Component\HttpKernel\Exception\HttpException => 'Request failed',
                default => 'An error occurred. Please try again later.'
            };
        }

        // In development, return actual error message for debugging
        return $exception->getMessage();
    }

    /**
     * Generate a safe error response that doesn't expose sensitive information.
     */
    protected function safeErrorResponse(\Exception $exception, int $code = 500): JsonResponse
    {
        // Log the actual error for debugging
        \Log::error('Controller Error', [
            'message' => $exception->getMessage(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // Always return sanitized message regardless of environment
        $sanitizedMessage = $this->sanitizeErrorMessage($exception);

        return $this->errorResponse(
            null,
            $sanitizedMessage,
            $code
        );
    }

}
