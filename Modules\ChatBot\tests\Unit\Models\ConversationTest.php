<?php

namespace Modules\ChatBot\Tests\Unit\Models;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\ChatBot\Enums\MessageRole;
use App\Models\User;

class ConversationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate', ['--database' => 'testing']);
    }

    /** @test */
    public function it_can_create_a_conversation()
    {
        $user = User::factory()->create();
        $bot = Bot::factory()->create();

        $conversation = Conversation::factory()->create([
            'bot_id' => $bot->id,
            'user_id' => $user->id,
            'user_type' => User::class,
        ]);

        $this->assertInstanceOf(Conversation::class, $conversation);
        $this->assertEquals($bot->id, $conversation->bot_id);
        $this->assertEquals($user->id, $conversation->user_id);
        $this->assertEquals(User::class, $conversation->user_type);
    }

    /** @test */
    public function it_belongs_to_a_bot()
    {
        $bot = Bot::factory()->create();
        $conversation = Conversation::factory()->create(['bot_id' => $bot->id]);

        $this->assertInstanceOf(Bot::class, $conversation->bot);
        $this->assertEquals($bot->id, $conversation->bot->id);
    }

    /** @test */
    public function it_belongs_to_a_user_polymorphically()
    {
        $user = User::factory()->create();
        $conversation = Conversation::factory()->create([
            'user_id' => $user->id,
            'user_type' => User::class,
        ]);

        $this->assertInstanceOf(User::class, $conversation->user);
        $this->assertEquals($user->id, $conversation->user->id);
    }

    /** @test */
    public function it_has_many_messages()
    {
        $conversation = Conversation::factory()->create();
        $messages = Message::factory()->count(3)->create([
            'conversation_id' => $conversation->id,
        ]);

        $this->assertCount(3, $conversation->messages);
        $this->assertInstanceOf(Message::class, $conversation->messages->first());
    }

    /** @test */
    public function it_can_scope_active_conversations()
    {
        Conversation::factory()->create(['status' => ConversationStatus::ACTIVE]);
        Conversation::factory()->create(['status' => ConversationStatus::COMPLETED]);
        Conversation::factory()->create(['status' => ConversationStatus::ARCHIVED]);

        $activeConversations = Conversation::active()->get();

        $this->assertCount(1, $activeConversations);
        $this->assertEquals(ConversationStatus::ACTIVE, $activeConversations->first()->status);
    }

    /** @test */
    public function it_can_scope_conversations_for_bot()
    {
        $bot1 = Bot::factory()->create();
        $bot2 = Bot::factory()->create();

        Conversation::factory()->count(2)->create(['bot_id' => $bot1->id]);
        Conversation::factory()->create(['bot_id' => $bot2->id]);

        $bot1Conversations = Conversation::forBot($bot1->id)->get();

        $this->assertCount(2, $bot1Conversations);
        $this->assertTrue($bot1Conversations->every(fn($conv) => $conv->bot_id === $bot1->id));
    }

    /** @test */
    public function it_can_scope_conversations_for_user()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        Conversation::factory()->count(2)->create([
            'user_id' => $user1->id,
            'user_type' => User::class,
        ]);
        Conversation::factory()->create([
            'user_id' => $user2->id,
            'user_type' => User::class,
        ]);

        $user1Conversations = Conversation::forUser($user1->id, User::class)->get();

        $this->assertCount(2, $user1Conversations);
        $this->assertTrue($user1Conversations->every(fn($conv) => $conv->user_id === $user1->id));
    }

    /** @test */
    public function it_can_update_message_stats()
    {
        $conversation = Conversation::factory()->create();
        
        // Create messages
        Message::factory()->count(3)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::USER,
        ]);
        Message::factory()->count(2)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::ASSISTANT,
        ]);

        $conversation->updateMessageStats();
        $conversation->refresh();

        $this->assertEquals(5, $conversation->message_count);
        $this->assertNotNull($conversation->last_message_at);
    }

    /** @test */
    public function it_can_mark_as_completed()
    {
        $conversation = Conversation::factory()->active()->create();

        $result = $conversation->markAsCompleted();

        $this->assertTrue($result);
        $this->assertEquals(ConversationStatus::COMPLETED, $conversation->fresh()->status);
    }

    /** @test */
    public function it_can_archive_conversation()
    {
        $conversation = Conversation::factory()->active()->create();

        $result = $conversation->archive();

        $this->assertTrue($result);
        $this->assertEquals(ConversationStatus::ARCHIVED, $conversation->fresh()->status);
    }

    /** @test */
    public function it_can_reactivate_conversation()
    {
        $conversation = Conversation::factory()->completed()->create();

        $result = $conversation->reactivate();

        $this->assertTrue($result);
        $this->assertEquals(ConversationStatus::ACTIVE, $conversation->fresh()->status);
    }

    /** @test */
    public function it_casts_status_to_enum()
    {
        $conversation = Conversation::factory()->create([
            'status' => ConversationStatus::ACTIVE,
        ]);

        $this->assertInstanceOf(ConversationStatus::class, $conversation->status);
        $this->assertEquals(ConversationStatus::ACTIVE, $conversation->status);
    }

    /** @test */
    public function it_casts_timestamps_correctly()
    {
        $conversation = Conversation::factory()->create();

        $this->assertInstanceOf(\Carbon\Carbon::class, $conversation->created_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $conversation->updated_at);
    }

    /** @test */
    public function it_has_fillable_attributes()
    {
        $fillable = [
            'title',
            'bot_id',
            'user_id',
            'user_type',
            'status',
            'message_count',
            'last_message_at',
        ];

        $conversation = new Conversation();

        $this->assertEquals($fillable, $conversation->getFillable());
    }
}
