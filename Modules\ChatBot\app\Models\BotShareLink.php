<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Carbon\Carbon;

class BotShareLink extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'token',
        'bot_id',
        'shareable_id',
        'shareable_type',
        'expires_at',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Generate UUID token on creation
        static::creating(function ($shareLink) {
            if (empty($shareLink->token)) {
                $shareLink->token = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'token';
    }

    /**
     * Get the bot that is shared.
     */
    public function bot(): BelongsTo
    {
        return $this->belongsTo(Bot::class);
    }

    /**
     * Get the shareable entity (polymorphic relationship).
     */
    public function shareable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope a query to only include active (non-expired) links.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where(function ($q) {
            $q->whereNull('expires_at')
              ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope a query to only include expired links.
     */
    public function scopeExpired(Builder $query): Builder
    {
        return $query->where('expires_at', '<=', now());
    }



    /**
     * Check if the share link is active (not expired).
     */
    public function isActive(): bool
    {
        return is_null($this->expires_at) || $this->expires_at->isFuture();
    }

    /**
     * Check if the share link is expired.
     */
    public function isExpired(): bool
    {
        return !is_null($this->expires_at) && $this->expires_at->isPast();
    }



    /**
     * Get the full share URL.
     */
    public function getShareUrl(): string
    {
        return url("/shared/bot/{$this->token}");
    }

    /**
     * Create a new share link for a bot and shareable entity.
     */
    public static function createForBot(int $botId, int $shareableId, string $shareableType, ?\DateTimeInterface $expiresAt = null): self
    {
        return static::create([
            'bot_id' => $botId,
            'shareable_id' => $shareableId,
            'shareable_type' => $shareableType,
            'expires_at' => $expiresAt,
        ]);
    }

    /**
     * Find an active share link by token.
     */
    public static function findActiveByToken(string $token): ?self
    {
        return static::where('token', $token)->active()->first();
    }

    /**
     * Clean up expired share links.
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->delete();
    }

    /**
     * Get sharing statistics.
     */
    public function getStats(): array
    {
        return [
            'token' => $this->token,
            'is_active' => $this->isActive(),
            'is_expired' => $this->isExpired(),
            'expires_at' => $this->expires_at?->toDateTimeString(),
            'share_url' => $this->getShareUrl(),
            'created_at' => $this->created_at->toDateTimeString(),
        ];
    }
}
