<?php

namespace Modules\ChatBot\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * AI Facade
 *
 * Provides convenient access to AI functionality through a static interface.
 * Only exposes public/user-level methods. Administrative methods should be accessed
 * through direct service injection.
 *
 *
 * @see \Modules\ChatBot\Services\ChatService
 */
class ChatFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'chat.service';
    }
}
