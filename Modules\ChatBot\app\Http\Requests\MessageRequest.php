<?php

namespace Modules\ChatBot\Http\Requests;

use Illuminate\Validation\Rule;
use <PERSON><PERSON><PERSON>\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;
use Modules\Core\Http\Requests\BaseFormRequest;

class MessageRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'conversation_uuid' => ['string', 'exists:conversations,uuid'],
            'content' => ['required_without:attachments', 'nullable', 'string', 'max:50000'],
            'content_type' => ['nullable', Rule::enum(ContentType::class)],
            'attachments' => ['nullable', 'array'],
            'attachments.*' => ['array'],
            'attachments.*.type' => ['required_with:attachments.*', 'string', 'in:image,file,audio,video'],
            'attachments.*.url' => ['required_with:attachments.*', 'string', 'url'],
            'attachments.*.name' => ['required_with:attachments.*', 'string', 'max:255'],
            'attachments.*.size' => ['nullable', 'integer', 'min:0'],
            'attachments.*.mime_type' => ['nullable', 'string', 'max:100'],
            'metadata' => ['nullable', 'array'],
            'quality_score' => ['nullable', 'integer', 'min:1', 'max:5'],
            'is_helpful' => ['nullable', 'boolean'],

            // AI generation options
            'temperature' => ['nullable', 'numeric', 'min:0', 'max:2'],
            'max_tokens' => ['nullable', 'integer', 'min:1', 'max:4000'],

            // RAG Query options
            'enable_rag' => ['nullable', 'boolean'],
            'file_ids' => ['nullable', 'array', 'max:20'],
            'file_ids.*' => ['integer', 'exists:knowledge_bases,id'],
            'top_k' => ['nullable', 'integer', 'min:1', 'max:20'],
        ];
    }
}
