<?php

namespace Modules\ChatBot\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Http;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ModelAI\Models\ModelAI;
use App\Models\User;

abstract class ChatBotTestCase extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $user;
    protected Bot $bot;
    protected ModelAI $aiModel;
    protected Conversation $conversation;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->artisan('migrate', ['--database' => 'testing']);
        
        $this->setupTestData();
        $this->mockAIServices();
    }

    protected function setupTestData(): void
    {
        // Create test user
        $this->user = User::factory()->create();

        // Create AI model
        $this->aiModel = ModelAI::factory()->create([
            'key' => 'gpt-4-turbo',
            'name' => 'GPT-4 Turbo',
            'provider' => 'openai',
            'api_endpoint' => 'https://api.openai.com/v1/chat/completions',
            'status' => 'active',
            'streaming' => false,
            'function_calling' => true,
        ]);

        // Create test bot
        $this->bot = Bot::factory()->create([
            'name' => 'Test Support Bot',
            'model_ai_id' => $this->aiModel->id,
            'status' => 'active',
            'system_prompt' => 'You are a helpful customer support assistant.',
        ]);

        // Create test conversation
        $this->conversation = Conversation::factory()->create([
            'title' => 'Test Conversation',
            'bot_id' => $this->bot->id,
            'user_id' => $this->user->id,
            'user_type' => User::class,
            'status' => 'active',
        ]);
    }

    protected function mockAIServices(): void
    {
        // Mock OpenAI API responses
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'role' => 'assistant',
                            'content' => 'This is a mocked AI response for testing.',
                        ],
                        'finish_reason' => 'stop',
                    ],
                ],
                'usage' => [
                    'prompt_tokens' => 50,
                    'completion_tokens' => 25,
                    'total_tokens' => 75,
                ],
                'model' => 'gpt-4-turbo',
            ], 200),


        ]);

        // Mock Anthropic API
        Http::fake([
            'api.anthropic.com/*' => Http::response([
                'content' => [
                    [
                        'type' => 'text',
                        'text' => 'This is a mocked Anthropic response.',
                    ],
                ],
                'usage' => [
                    'input_tokens' => 40,
                    'output_tokens' => 20,
                ],
                'stop_reason' => 'end_turn',
            ], 200),
        ]);
    }

    protected function authenticateUser(): void
    {
        $this->actingAs($this->user);
    }

    protected function getApiHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
        ];
    }

    protected function createTestMessage(array $attributes = []): Message
    {
        return Message::factory()->create(array_merge([
            'conversation_id' => $this->conversation->id,
        ], $attributes));
    }

    protected function createTestConversation(array $attributes = []): Conversation
    {
        return Conversation::factory()->create(array_merge([
            'bot_id' => $this->bot->id,
            'user_id' => $this->user->id,
            'user_type' => User::class,
        ], $attributes));
    }

    protected function assertApiResponse($response, int $status = 200, bool $success = true): void
    {
        $response->assertStatus($status);
        
        if ($success) {
            $response->assertJson(['success' => true]);
        } else {
            $response->assertJson(['success' => false]);
        }
    }

    protected function assertValidationError($response, string $field): void
    {
        $response->assertStatus(422);
        $response->assertJsonValidationErrors($field);
    }

    protected function assertDatabaseHasMessage(array $attributes): void
    {
        $this->assertDatabaseHas('messages', $attributes);
    }

    protected function assertDatabaseHasConversation(array $attributes): void
    {
        $this->assertDatabaseHas('conversations', $attributes);
    }

    protected function mockFailedAIResponse(): void
    {
        Http::fake([
            'api.openai.com/*' => Http::response([
                'error' => [
                    'message' => 'API rate limit exceeded',
                    'type' => 'rate_limit_error',
                ],
            ], 429),
        ]);
    }

    protected function mockAIResponseWithTools(): void
    {
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'role' => 'assistant',
                            'content' => null,
                            'tool_calls' => [
                                [
                                    'id' => 'call_123',
                                    'type' => 'function',
                                    'function' => [
                                        'name' => 'web_search',
                                        'arguments' => json_encode(['query' => 'test query']),
                                    ],
                                ],
                            ],
                        ],
                        'finish_reason' => 'tool_calls',
                    ],
                ],
                'usage' => [
                    'prompt_tokens' => 60,
                    'completion_tokens' => 30,
                    'total_tokens' => 90,
                ],
            ], 200),
        ]);
    }

    protected function tearDown(): void
    {
        Http::clearResolvedInstances();
        parent::tearDown();
    }
}
