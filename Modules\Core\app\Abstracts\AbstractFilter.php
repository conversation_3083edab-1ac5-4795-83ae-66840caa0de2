<?php

namespace Modules\Core\Abstracts;


use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

abstract class AbstractFilter
{
    protected Request $request;
    protected array $rawParams = [];

    public function __construct(Request $request)
    {
        $this->request = $request;
        $this->parseRawQueryParams();
    }

    /**
     * Parse raw query string để giữ nguyên dot notation
     */
    protected function parseRawQueryParams(): void
    {
        $queryString = $_SERVER['QUERY_STRING'] ?? '';
        if ($queryString) {
            parse_str($queryString, $this->rawParams);
        }
    }

    public function apply(Builder $query): Builder
    {
        foreach ($this->filters() as $field => $config) {
            if (!$this->hasParam($field)) {
                continue;
            }

            $value = $this->getParam($field);
            $type = is_array($config) ? ($config['type'] ?? 'exact') : $config;
            $column = is_array($config) ? ($config['column'] ?? $field) : $field;
            $relation = is_array($config) ? ($config['relation'] ?? null) : null;

            if ($type === 'custom') {
                $this->applyCustomFilter($query, $field, $value);
            } elseif ($relation) {
                $query->whereHas($relation, function ($q) use ($column, $value, $type) {
                    $this->buildCondition($q, $column, $value, $type);
                });
            } else {
                $this->buildCondition($query, $column, $value, $type);
            }
        }

        $this->applySorting($query);
        return $query;
    }

    protected function buildCondition(Builder $query, string $column, mixed $value, string $type): void
    {

        match ($type) {
            'like' => $query->where($column, 'like', '%' . $this->escapeLike($value) . '%'),
            'starts' => $query->where($column, 'like', $this->escapeLike($value) . '%'),
            'ends' => $query->where($column, 'like', '%' . $this->escapeLike($value)),
            'not_like' => $query->where($column, 'not like', '%' . $this->escapeLike($value) . '%'),
            'in' => $query->whereIn($column, is_array($value) ? $value : explode(',', $value)),
            'not_in' => $query->whereNotIn($column, is_array($value) ? $value : explode(',', $value)),
            'boolean' => $query->where($column, filter_var($value, FILTER_VALIDATE_BOOLEAN)),
            'null' => $query->whereNull($column),
            'not_null' => $query->whereNotNull($column),
            'date' => $query->whereDate($column, $value),
            'from' => $query->whereDate($column, '>=', $value),
            'to' => $query->whereDate($column, '<=', $value),
            'between' => $this->applyBetween($query, $column, $value),
            'trashed' => ($value === 'yes') ? $query->onlyTrashed() : null,
            default => $query->where($column, $value),
        };
    }

    protected function applyBetween(Builder $query, string $column, mixed $value): void
    {
        $values = is_array($value) ? $value : explode(',', $value);
        if (count($values) === 2) {
            $query->whereBetween($column, $values);
        }
    }

    protected function escapeLike(string $value): string
    {
        return str_replace(['%', '_'], ['\\%', '\\_'], $value);
    }

    protected function applySorting(Builder $query): void
    {
        $sortBy = $this->getParam('sort_by');
        $sortDir = strtolower($this->getParam('sort_dir', 'asc')) === 'desc' ? 'desc' : 'asc';

        if (!$sortBy) {
            return;
        }

        // Hỗ trợ sort theo quan hệ: relation.column
        if (str_contains($sortBy, '.')) {
            $this->applySortWithRelation($query, $sortBy, $sortDir);
        } else {
            $query->orderBy($sortBy, $sortDir);
        }
    }

    protected function applySortWithRelation(Builder $query, string $sortBy, string $sortDir): void
    {
        [$relation, $column] = explode('.', $sortBy, 2);

        // Sử dụng join để sort được theo relation
        $model = $query->getModel();
        $relationInstance = $model->{$relation}();

        if (method_exists($relationInstance, 'getRelated')) {
            $relatedTable = $relationInstance->getRelated()->getTable();
            $foreignKey = $relationInstance->getForeignKeyName();
            $localKey = $relationInstance->getLocalKeyName();
            $currentTable = $model->getTable();

            $query->leftJoin($relatedTable, "{$currentTable}.{$localKey}", '=', "{$relatedTable}.{$foreignKey}")
                ->orderBy("{$relatedTable}.{$column}", $sortDir)
                ->select("{$currentTable}.*");
        } else {
            // Fallback cho các loại relation khác
            $query->orderBy(\DB::raw("(SELECT {$column} FROM {$relation} WHERE {$relation}.id = {$model->getTable()}.{$relation}_id LIMIT 1)"), $sortDir);
        }
    }

    /**
     * Lấy parameter - ưu tiên raw params (giữ dot notation)
     */
    protected function getParam(string $key, mixed $default = null): mixed
    {
        // Ưu tiên raw params (giữ nguyên dot notation)
        if (array_key_exists($key, $this->rawParams)) {
            return $this->rawParams[$key];
        }

        // Fallback: thử với underscore version từ Laravel request
        $underscoreKey = str_replace('.', '_', $key);
        return $this->request->get($underscoreKey, $default);
    }

    /**
     * Kiểm tra parameter có tồn tại không
     */
    protected function hasParam(string $key): bool
    {
        // Kiểm tra trong raw params trước
        if (array_key_exists($key, $this->rawParams)) {
            return true;
        }

        // Fallback: kiểm tra với underscore version
        $underscoreKey = str_replace('.', '_', $key);
        return $this->request->has($underscoreKey);
    }

    /**
     * Apply custom filter logic.
     * Override this method in child classes to implement custom filtering.
     */
    protected function applyCustomFilter(Builder $query, string $field, mixed $value): void
    {
        // Default implementation does nothing
        // Child classes should override this method
    }

    abstract protected function filters(): array;
}
