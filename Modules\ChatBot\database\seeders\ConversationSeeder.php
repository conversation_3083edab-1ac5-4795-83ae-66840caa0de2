<?php

namespace Modules\ChatBot\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\ChatBot\Enums\MessageRole;
use Modules\User\Models\User;

class ConversationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing bots and users
        $bots = Bot::active()->take(3)->get();
        $users = User::take(5)->get();

        if ($bots->isEmpty() || $users->isEmpty()) {
            $this->command->warn('No bots or users found. Please run BotSeeder and ensure users exist first.');
            return;
        }

        $this->command->info('Creating sample conversations...');

        foreach ($users as $user) {
            // Create 2-4 conversations per user
            $conversationCount = rand(2, 4);
            
            for ($i = 0; $i < $conversationCount; $i++) {
                $bot = $bots->random();
                
                $conversation = Conversation::factory()
                    ->forUser($user)
                    ->forBot($bot)
                    ->withRealisticTitle()
                    ->create([
                        'status' => $this->getRandomStatus(),
                    ]);

                // Create messages for each conversation
                $this->createMessagesForConversation($conversation);
                
                // Update conversation stats
                $conversation->updateMessageStats();
            }
        }

        $this->command->info('Sample conversations created successfully!');
    }

    /**
     * Create messages for a conversation.
     */
    private function createMessagesForConversation(Conversation $conversation): void
    {
        $messageCount = rand(3, 15);
        $messages = [];

        // Always start with a system message
        $messages[] = Message::factory()
            ->forConversation($conversation)
            ->create([
                'role' => MessageRole::SYSTEM,
                'content' => $conversation->bot->system_prompt,
                'created_at' => $conversation->created_at,
            ]);

        $currentTime = $conversation->created_at;

        // Create alternating user and assistant messages
        for ($i = 1; $i < $messageCount; $i++) {
            $currentTime = $currentTime->addMinutes(rand(1, 30));
            
            if ($i % 2 === 1) {
                // User message
                $message = Message::factory()
                    ->fromUser()
                    ->completed()
                    ->forConversation($conversation)
                    ->create([
                        'created_at' => $currentTime,
                        'updated_at' => $currentTime,
                    ]);
            } else {
                // Assistant message
                $message = Message::factory()
                    ->fromAssistant()
                    ->completed()
                    ->forConversation($conversation)
                    ->create([
                        'model_used' => $conversation->bot->aiModel->key,
                        'created_at' => $currentTime->addSeconds(rand(5, 30)),
                        'updated_at' => $currentTime->addSeconds(rand(5, 30)),
                        'started_at' => $currentTime,
                        'completed_at' => $currentTime->addSeconds(rand(5, 30)),
                    ]);

                // Occasionally add tool calls
                if (rand(1, 10) === 1) {
                    $message->update([
                        'tool_calls' => [
                            [
                                'id' => 'call_' . uniqid(),
                                'type' => 'function',
                                'function' => [
                                    'name' => 'web_search',
                                    'arguments' => json_encode(['query' => 'latest information']),
                                ],
                            ],
                        ],
                    ]);

                    // Add tool response
                    Message::factory()
                        ->forConversation($conversation)
                        ->create([
                            'role' => MessageRole::TOOL,
                            'content' => json_encode([
                                'status' => 'success',
                                'results' => ['Found relevant information'],
                            ]),
                            'tool_call_id' => 'call_' . uniqid(),
                            'created_at' => $currentTime->addSeconds(rand(35, 60)),
                        ]);
                }
            }

            $messages[] = $message;
        }

        // Occasionally add failed messages
        if (rand(1, 5) === 1) {
            Message::factory()
                ->fromAssistant()
                ->failed()
                ->forConversation($conversation)
                ->create([
                    'model_used' => $conversation->bot->aiModel->key,
                    'created_at' => $currentTime->addMinutes(rand(5, 15)),
                ]);
        }
    }

    /**
     * Get random conversation status with realistic distribution.
     */
    private function getRandomStatus(): ConversationStatus
    {
        $rand = rand(1, 100);
        
        return match (true) {
            $rand <= 60 => ConversationStatus::ACTIVE,
            $rand <= 85 => ConversationStatus::COMPLETED,
            default => ConversationStatus::ARCHIVED,
        };
    }

    /**
     * Create specific scenario conversations.
     */
    public function createScenarioConversations(): void
    {
        $bot = Bot::active()->first();
        $user = User::first();

        if (!$bot || !$user) {
            return;
        }

        // Customer support scenario
        $supportConversation = Conversation::factory()
            ->forUser($user)
            ->forBot($bot)
            ->active()
            ->create([
                'title' => 'Hỗ trợ khách hàng - Lỗi đăng nhập',
            ]);

        $this->createSupportScenario($supportConversation);

        // Technical consultation scenario
        $techConversation = Conversation::factory()
            ->forUser($user)
            ->forBot($bot)
            ->completed()
            ->create([
                'title' => 'Tư vấn kỹ thuật - Tích hợp API',
            ]);

        $this->createTechScenario($techConversation);
    }

    /**
     * Create customer support scenario.
     */
    private function createSupportScenario(Conversation $conversation): void
    {
        $messages = [
            ['role' => MessageRole::SYSTEM, 'content' => $conversation->bot->system_prompt],
            ['role' => MessageRole::USER, 'content' => 'Xin chào, tôi không thể đăng nhập vào tài khoản của mình.'],
            ['role' => MessageRole::ASSISTANT, 'content' => 'Xin chào! Tôi rất tiếc khi biết bạn gặp khó khăn với việc đăng nhập. Bạn có thể cho tôi biết thông báo lỗi cụ thể mà bạn nhận được không?'],
            ['role' => MessageRole::USER, 'content' => 'Hệ thống báo "Tên đăng nhập hoặc mật khẩu không đúng" nhưng tôi chắc chắn đã nhập đúng.'],
            ['role' => MessageRole::ASSISTANT, 'content' => 'Tôi hiểu vấn đề của bạn. Hãy thử các bước sau: 1) Xóa cache và cookies của trình duyệt, 2) Thử đăng nhập ở chế độ ẩn danh, 3) Kiểm tra xem Caps Lock có đang bật không. Nếu vẫn không được, tôi sẽ hỗ trợ reset mật khẩu cho bạn.'],
        ];

        $this->createMessagesFromArray($conversation, $messages);
    }

    /**
     * Create technical consultation scenario.
     */
    private function createTechScenario(Conversation $conversation): void
    {
        $messages = [
            ['role' => MessageRole::SYSTEM, 'content' => $conversation->bot->system_prompt],
            ['role' => MessageRole::USER, 'content' => 'Tôi cần tư vấn về cách tích hợp API của bạn vào ứng dụng Laravel.'],
            ['role' => MessageRole::ASSISTANT, 'content' => 'Tuyệt vời! Tôi sẽ hướng dẫn bạn tích hợp API vào Laravel. Trước tiên, bạn đã có API key chưa?'],
            ['role' => MessageRole::USER, 'content' => 'Rồi, tôi đã có API key. Bước tiếp theo là gì?'],
            ['role' => MessageRole::ASSISTANT, 'content' => 'Tốt! Bạn cần cài đặt Guzzle HTTP client và tạo một service class để xử lý API calls. Tôi sẽ gửi cho bạn code mẫu.'],
        ];

        $this->createMessagesFromArray($conversation, $messages);
    }

    /**
     * Create messages from array.
     */
    private function createMessagesFromArray(Conversation $conversation, array $messages): void
    {
        $currentTime = $conversation->created_at;

        foreach ($messages as $index => $messageData) {
            $currentTime = $currentTime->addMinutes(rand(1, 5));

            Message::factory()
                ->forConversation($conversation)
                ->completed()
                ->create([
                    'role' => $messageData['role'],
                    'content' => $messageData['content'],
                    'model_used' => $messageData['role'] === MessageRole::ASSISTANT ? $conversation->bot->aiModel->key : null,
                    'created_at' => $currentTime,
                    'updated_at' => $currentTime,
                ]);
        }

        $conversation->updateMessageStats();
    }
}
