<?php

namespace Modules\ChatBot\Facades;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Facade;
use Modules\ChatBot\Models\Bot;

/**
 * Bot Facade
 *
 * Provides convenient access to public bot functionality through a static interface.
 * Only exposes public/user-level methods. Administrative methods should be accessed
 * through direct service injection.
 *
 * @method static Collection getActiveBots() Get all active bots for public use
 * @method static Collection getPublicBots() Get all publicly visible bots (active status)
 * @method static Bot|null getByUuid(string $uuid) Get bot by UUID (only public bots)
 * @method static Collection getBotsForDropdown() Get bots for dropdown/select options
 * @method static Collection searchBots(string $query, int $limit = 10) Search bots by name or description
 *
 * @see \Modules\ChatBot\Services\BotService
 */
class BotFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'bot.service';
    }
}
