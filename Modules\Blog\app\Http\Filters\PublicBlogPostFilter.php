<?php

namespace Modules\Blog\Http\Filters;

use Modules\Core\Abstracts\AbstractFilter;

class PublicBlogPostFilter extends AbstractFilter
{
    /**
     * Define the filters available for this model.
     */
    protected function filters(): array
    {
        return [
            // Basic field filters
            'author_id' => 'exact',

            // Date range filters (following project standard pattern)
            'published_from' => ['type' => 'from', 'column' => 'published_at'],
            'published_to' => ['type' => 'to', 'column' => 'published_at'],

            // Author-based filters
            'author_fullName' => [
                'type' => 'like',
                'relation' => 'author',
                'column' => 'full_name'
            ],
            // Translation-based filters
            'title' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'title'
            ],
            'slug' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'slug'
            ],
            'summary' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'summary'
            ],
            'content' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'content'
            ],
            'meta_description' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'meta_description'
            ],
        ];
    }
}
