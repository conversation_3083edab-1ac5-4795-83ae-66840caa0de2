<?php

namespace Modules\Blog\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Blog\Models\BlogPostTranslation;
use Modules\Blog\Models\BlogPost;

class BlogPostTranslationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = BlogPostTranslation::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'post_id' => BlogPost::factory(),
            'locale' => 'en',
            'title' => $this->faker->sentence(6),
            'slug' => $this->faker->unique()->slug(3),
            'content' => $this->faker->paragraphs(5, true),
            'summary' => $this->faker->paragraph(3),
            'image' => $this->faker->optional(0.6)->imageUrl(800, 600, 'business'),
            'meta_title' => $this->faker->sentence(4),
            'meta_description' => $this->faker->paragraph(2),
        ];
    }

    /**
     * Indicate that the translation is in Vietnamese.
     */
    public function vietnamese(): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => 'vi',
            'title' => $this->faker->sentence(6),
            'slug' => $this->faker->unique()->slug(3),
        ]);
    }

    /**
     * Indicate that the translation is in English.
     */
    public function english(): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => 'en',
        ]);
    }

    /**
     * Set a specific post.
     */
    public function forPost(BlogPost $post): static
    {
        return $this->state(fn (array $attributes) => [
            'post_id' => $post->id,
        ]);
    }

    /**
     * Set a specific locale.
     */
    public function locale(string $locale): static
    {
        return $this->state(fn (array $attributes) => [
            'locale' => $locale,
        ]);
    }

    /**
     * Set a specific slug.
     */
    public function slug(string $slug): static
    {
        return $this->state(fn (array $attributes) => [
            'slug' => $slug,
        ]);
    }

    /**
     * Set a specific title.
     */
    public function title(string $title): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => $title,
        ]);
    }
}
