<?php

namespace Modules\ChatBot\Observers;

use Illuminate\Support\Facades\Cache;
use Mo<PERSON>les\ChatBot\Models\BotShareLink;

class BotShareLinkObserver
{
    /**
     * Cache tags for bot share links
     */
    private const CACHE_TAGS = ['bot-share-links', 'bots'];

    /**
     * Handle the BotShareLink "created" event.
     */
    public function created(BotShareLink $botShareLink): void
    {
        $this->clearCache($botShareLink);
    }

    /**
     * Handle the BotShareLink "updated" event.
     */
    public function updated(BotShareLink $botShareLink): void
    {
        $this->clearCache($botShareLink);
    }

    /**
     * Handle the BotShareLink "deleted" event.
     */
    public function deleted(BotShareLink $botShareLink): void
    {
        $this->clearCache($botShareLink);
    }

    /**
     * Handle the BotShareLink "restored" event.
     */
    public function restored(BotShareLink $botShareLink): void
    {
        $this->clearCache($botShareLink);
    }

    /**
     * Handle the BotShareLink "force deleted" event.
     */
    public function forceDeleted(BotShareLink $botShareLink): void
    {
        $this->clearCache($botShareLink);
    }

    /**
     * Clear bot share link related cache.
     */
    private function clearCache(BotShareLink $botShareLink): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                // Clear tagged cache
                Cache::tags(self::CACHE_TAGS)->flush();
                
                // Clear specific cache keys related to bot share links
                $cacheKeys = [
                    'bot_share_links.all',
                    'bot_share_links.active',
                    'bot_share_links.expired',
                    "bot_share_links.bot.{$botShareLink->bot_id}",
                    "bot_share_links.token.{$botShareLink->token}",
                    "bot_share_links.shareable.{$botShareLink->shareable_type}.{$botShareLink->shareable_id}",
                    "bot.{$botShareLink->bot_id}.share_links",
                    "bot.{$botShareLink->bot_id}.active_share_links",
                    "bot.{$botShareLink->bot_id}.sharing_stats",
                ];

                foreach ($cacheKeys as $key) {
                    Cache::forget($key);
                }

                // Clear paginated cache for different parameters
                for ($page = 1; $page <= 10; $page++) {
                    for ($limit = 10; $limit <= 50; $limit += 10) {
                        Cache::forget("bot_share_links.paginated.{$page}.{$limit}");
                        Cache::forget("bot_share_links.bot.{$botShareLink->bot_id}.paginated.{$page}.{$limit}");
                        Cache::forget("bot_share_links.active.paginated.{$page}.{$limit}");
                    }
                }

                // Clear bot-specific cache that might be affected
                if ($botShareLink->bot_id) {
                    $botCacheKeys = [
                        "bot.{$botShareLink->bot_id}.stats",
                        "bot.{$botShareLink->bot_id}.public_access",
                    ];

                    foreach ($botCacheKeys as $key) {
                        Cache::forget($key);
                    }
                }

                // Clear public access cache if this affects public sharing
                Cache::forget('public_bot_access');
                Cache::forget('shared_bot_links');
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
            \Log::warning('Failed to clear BotShareLink cache', [
                'bot_share_link_id' => $botShareLink->id,
                'token' => $botShareLink->token,
                'error' => $e->getMessage()
            ]);
        }
    }
}
