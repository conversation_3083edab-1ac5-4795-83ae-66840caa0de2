<?php

namespace Modules\ChatBot\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Role\Models\Permission;
use Modules\Role\Models\Role;

class ChatBotPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->seedChatBotPermissions();
        $this->assignPermissionsToSuperAdmin();
    }

    /**
     * Seed chatbot module permissions.
     */
    private function seedChatBotPermissions(): void
    {
        $permissions = [
            // Bot Management Permissions
            [
                'name' => 'bot.view',
                'display_name' => 'View Bots',
                'description' => 'Permission to view bots and their details',
                'module_name' => 'chatbot',
                'sort_order' => 1,
                'guard_name' => 'api',
            ],
            [
                'name' => 'bot.create',
                'display_name' => 'Create Bots',
                'description' => 'Permission to create new bots',
                'module_name' => 'chatbot',
                'sort_order' => 2,
                'guard_name' => 'api',
            ],
            [
                'name' => 'bot.edit',
                'display_name' => 'Edit Bots',
                'description' => 'Permission to update bot settings and configurations',
                'module_name' => 'chatbot',
                'sort_order' => 3,
                'guard_name' => 'api',
            ],
            [
                'name' => 'bot.delete',
                'display_name' => 'Soft Delete Bots',
                'description' => 'Permission to soft delete bots',
                'module_name' => 'chatbot',
                'sort_order' => 4,
                'guard_name' => 'api',
            ],
            [
                'name' => 'bot.destroy',
                'display_name' => 'Force Delete Bots',
                'description' => 'Permission to permanently delete bots',
                'module_name' => 'chatbot',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],

            // Bot Sharing Permissions
            [
                'name' => 'bot.share',
                'display_name' => 'Share Bots',
                'description' => 'Permission to share bots with other users',
                'module_name' => 'chatbot',
                'sort_order' => 5,
                'guard_name' => 'api',
            ],
            [
                'name' => 'bot.share-link',
                'display_name' => 'Create Share Links',
                'description' => 'Permission to create public share links for bots',
                'module_name' => 'chatbot',
                'sort_order' => 6,
                'guard_name' => 'api',
            ],

            // Conversation Permissions
            [
                'name' => 'conversation.view',
                'display_name' => 'View Conversations',
                'description' => 'Permission to view conversations and chat history',
                'module_name' => 'chatbot',
                'sort_order' => 7,
                'guard_name' => 'api',
            ],
            [
                'name' => 'conversation.create',
                'display_name' => 'Create Conversations',
                'description' => 'Permission to start new conversations',
                'module_name' => 'chatbot',
                'sort_order' => 8,
                'guard_name' => 'api',
            ],
            [
                'name' => 'conversation.edit',
                'display_name' => 'Edit Conversations',
                'description' => 'Permission to update conversation settings',
                'module_name' => 'chatbot',
                'sort_order' => 9,
                'guard_name' => 'api',
            ],
            [
                'name' => 'conversation.delete',
                'display_name' => 'Soft Delete Conversations',
                'description' => 'Permission to soft delete conversations',
                'module_name' => 'chatbot',
                'sort_order' => 10,
                'guard_name' => 'api',
            ],
            [
                'name' => 'conversation.destroy',
                'display_name' => 'Force Delete Conversations',
                'description' => 'Permission to permanently delete conversations',
                'module_name' => 'chatbot',
                'sort_order' => 11,
                'guard_name' => 'api',
            ],

            // Message Permissions
            [
                'name' => 'messages.view',
                'display_name' => 'View Messages',
                'description' => 'Permission to view messages in conversations',
                'module_name' => 'chatbot',
                'sort_order' => 13,
                'guard_name' => 'api',
            ],
            [
                'name' => 'messages.create',
                'display_name' => 'Send Messages',
                'description' => 'Permission to send messages and interact with bots',
                'module_name' => 'chatbot',
                'sort_order' => 14,
                'guard_name' => 'api',
            ],
            [
                'name' => 'messages.edit',
                'display_name' => 'Edit Messages',
                'description' => 'Permission to edit and retry messages',
                'module_name' => 'chatbot',
                'sort_order' => 15,
                'guard_name' => 'api',
            ],
            [
                'name' => 'messages.delete',
                'display_name' => 'Soft Delete Messages',
                'description' => 'Permission to soft delete messages',
                'module_name' => 'chatbot',
                'sort_order' => 16,
                'guard_name' => 'api',
            ],
            [
                'name' => 'messages.destroy',
                'display_name' => 'Force Delete Messages',
                'description' => 'Permission to permanently delete messages',
                'module_name' => 'chatbot',
                'sort_order' => 17,
                'guard_name' => 'api',
            ],

            // Knowledge Base Permissions
            [
                'name' => 'knowledge-base.view',
                'display_name' => 'View Knowledge Bases',
                'description' => 'Permission to view knowledge bases and their details',
                'module_name' => 'chatbot',
                'sort_order' => 19,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.create',
                'display_name' => 'Create Knowledge Bases',
                'description' => 'Permission to create new knowledge bases from text or files',
                'module_name' => 'chatbot',
                'sort_order' => 20,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.edit',
                'display_name' => 'Edit Knowledge Bases',
                'description' => 'Permission to update knowledge base content and settings',
                'module_name' => 'chatbot',
                'sort_order' => 21,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.delete',
                'display_name' => 'Soft Delete Knowledge Bases',
                'description' => 'Permission to soft delete knowledge bases',
                'module_name' => 'chatbot',
                'sort_order' => 22,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.destroy',
                'display_name' => 'Force Delete Knowledge Bases',
                'description' => 'Permission to permanently delete knowledge bases',
                'module_name' => 'chatbot',
                'sort_order' => 23,
                'guard_name' => 'api',
            ],
            [
                'name' => 'knowledge-base.attach',
                'display_name' => 'Attach Knowledge Bases',
                'description' => 'Permission to attach knowledge bases to bots and conversations',
                'module_name' => 'chatbot',
                'sort_order' => 25,
                'guard_name' => 'api',
            ],

            // Analytics and Reports
            [
                'name' => 'chatbot.analytics',
                'display_name' => 'ChatBot Analytics',
                'description' => 'Permission to view chatbot analytics and reports',
                'module_name' => 'chatbot',
                'sort_order' => 26,
                'guard_name' => 'api',
            ],
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => 'api'],
                array_merge($permission, ['guard_name' => 'api'])
            );
        }
    }

    /**
     * Assign chatbot permissions to super-admin role.
     */
    private function assignPermissionsToSuperAdmin(): void
    {
        $superAdmin = Role::where('name', 'super-admin')->where('guard_name', 'api')->first();

        if ($superAdmin) {
            $chatbotPermissions = Permission::where('module_name', 'chatbot')->where('guard_name', 'api')->get();

            foreach ($chatbotPermissions as $permission) {
                if (!$superAdmin->hasPermissionTo($permission->name)) {
                    $superAdmin->givePermissionTo($permission->name);
                }
            }
        }
    }
}
