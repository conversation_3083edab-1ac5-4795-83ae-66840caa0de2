<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Http\Controllers\Controller;
use Intervention\Image\Image;
use Modules\ChatBot\Http\Filters\KnowledgeBaseFilter;
use Modules\ChatBot\Http\Requests\KnowledgeBaseRequest;
use Modules\ChatBot\Http\Requests\KnowledgeBaseFileUploadRequest;
use Modules\ChatBot\Http\Requests\KnowledgeBaseBulkUploadRequest;
use Modules\ChatBot\Http\Requests\BulkKnowledgeBaseRequest;
use Modules\ChatBot\Http\Requests\AttachToBotRequest;
use Modules\ChatBot\Jobs\RAGFileProcessingJob;
use Modules\ChatBot\Models\KnowledgeBase;
use Modules\ChatBot\Models\Bot;
use Modules\Core\Traits\ResponseTrait;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class KnowledgeBaseController extends Controller
{
    use ResponseTrait;

    public function __construct()
    {
        $this->middleware('role_or_permission:super-admin|knowledge-base.view')->only(['index', 'show', 'getFiles', 'dropdown']);
        $this->middleware('role_or_permission:super-admin|knowledge-base.create')->only(['store', 'storeFromText', 'storeFromFile', 'storeFileUpload', 'removeFileUpload', 'bulkUpload']);
        $this->middleware('role_or_permission:super-admin|knowledge-base.edit')->only(['update', 'retrain']);
        $this->middleware('role_or_permission:super-admin|knowledge-base.delete')->only(['delete', 'restore', 'bulkDelete', 'bulkRestore']);
        $this->middleware('role_or_permission:super-admin|knowledge-base.destroy')->only(['destroy', 'bulkDestroy']);
    }

    /**
     * Display a listing of knowledge bases.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $knowledgeBases = KnowledgeBase::forOwner(auth()->id(), get_class(auth()->user()))
                ->filter(new KnowledgeBaseFilter($request))
                ->paginate($request->input('limit', 10));
            return $this->paginatedResponse($knowledgeBases, __('Knowledge bases retrieved successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get knowledge bases for dropdown.
     */
    public function dropdown(): JsonResponse
    {
        try {
            $knowledgeBases = KnowledgeBase::forOwner(auth()->id(), get_class(auth()->user()))
                ->where('status', 'ready')
                ->select(['id', 'uuid', 'name', 'type'])
                ->orderBy('name')
                ->get();

            return $this->successResponse($knowledgeBases, __('Knowledge bases dropdown retrieved successfully.'));
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Display a listing of knowledge base files only.
     *
     * This endpoint returns only knowledge bases with type='file', excluding text-based knowledge bases.
     * Supports filtering by name, status, and date range.
     *
     * @param Request $request
     * @return JsonResponse
     *
     * @queryParam limit integer optional Number of items per page (default: 10)
     * @queryParam name string optional Filter by knowledge base name (partial match)
     * @queryParam status string optional Filter by status (pending, processing, ready, error)
     * @queryParam created_from string optional Filter by creation date (Y-m-d format)
     */
    public function getFiles(Request $request): JsonResponse
    {
        try {
            $knowledgeBaseFiles = KnowledgeBase::forOwner(auth()->id(), get_class(auth()->user()))
                ->filter(new KnowledgeBaseFilter($request))
                ->orderBy('updated_at', 'desc')
                ->paginate($request->input('limit', 10), ['uuid', 'name', 'storage_path', 'created_at', 'updated_at']);

            return $this->paginatedResponse($knowledgeBaseFiles, 'Knowledge base files retrieved successfully.');
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Display the specified knowledge base.
     */
    public function show(string $uuid): JsonResponse
    {
        try {
            $knowledgeBase = KnowledgeBase::where('uuid', $uuid)
                ->forOwner(auth()->id(), get_class(auth()->user()))
                ->firstOrFail();

            return $this->successResponse(
                $knowledgeBase,
                'Knowledge base retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created knowledge base from text.
     */
    public function storeFromText(KnowledgeBaseRequest $request): JsonResponse
    {
        try {
            $knowledgeBase = KnowledgeBase::create([
                'owner_id' => auth()->id(),
                'owner_type' => get_class(auth()->user()),
                'name' => $request->input('name'),
                'type' => 'text',
                'content' => $request->input('content'),
                'status' => 'pending'
            ]);

            $knowledgeBase->generateTextFilePath($request->input('content'));

            RAGFileProcessingJob::dispatch(
                [$knowledgeBase->id],
                auth()->id(),
                get_class(auth()->user()),
                'create'
            );

            return $this->successResponse(
                $knowledgeBase->fresh(),
                'Knowledge base created successfully from text',
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created knowledge base.
     */
    public function store(KnowledgeBaseRequest $request): JsonResponse
    {
        try {
            $type = $request->input('type', 'text');

            if ($type === 'text') {
                return $this->storeFromText($request);
            } elseif ($type === 'file') {
                return $this->storeFromFile($request);
            }

            return $this->errorResponse('Invalid knowledge base type', 400);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Store a newly created knowledge base from file upload.
     */
    public function storeFileUpload(KnowledgeBaseFileUploadRequest $request): JsonResponse
    {

        try {
            $user = auth()->user();

            $file = $request->file('file');
            $extension = strtolower($file->getClientOriginalExtension());

            // Generate file path
            $originalName = $file->getClientOriginalName();
            $uuidFileName = Str::uuid() . '.' . $extension;
            $relativePath = 'knowledge-bases/temp/' . $user->uuid . '/' . $uuidFileName;

            Storage::putFileAs(
                'knowledge-bases/temp/' . $user->uuid,
                $file,
                $uuidFileName
            );

            return $this->successResponse(
                ['name' => $originalName, 'storage_path' => $relativePath],
                'Knowledge base created successfully from file',
                201
            );

        } catch (\Throwable $e) {
            report($e);
            return $this->errorResponse('File upload failed: ' . $e->getMessage(), 500);
        }
    }

    public function removeFileUpload(Request $request)
    {
        $request->validate([
            'name' => 'required|string',
            'storage_path' => 'required|string',
        ]);

        $file = KnowledgeBase::where([
            'name' => $request->input('name'),
            'storage_path' => $request->input('storage_path')
        ])->first();

        if (!$file) {
            Storage::disk('local')->delete($request->input('storage_path'));
            return $this->successResponse(true, 'File removed successfully');
        }

        return $this->errorResponse(false, __('Bad request'));
    }

    /**
     * Store a newly created knowledge base from file.
     */
    public function storeFromFile(KnowledgeBaseRequest $request): JsonResponse
    {

        try {
            $user = auth()->user();

            // Check if file exists
            if (!Storage::exists($request->input('storage_path'))) {
                return $this->errorResponse('File not found', 404);
            }

            // Move file from temp to permanent location
            $tempPath = $request->input('storage_path');
            $fileName = basename($tempPath);
            $permanentPath = 'knowledge-bases/' . $user->uuid . '/' . $fileName;

            Storage::move($tempPath, $permanentPath);

            // Get file metadata
            $fileSize = Storage::size($permanentPath);
            $mimeType = Storage::mimeType($permanentPath);

            $knowledgeBase = KnowledgeBase::create([
                'owner_id' => $user->id,
                'owner_type' => get_class($user),
                'name' => $request->input('name'),
                'type' => 'file',
                'storage_path' => $permanentPath,
                'status' => 'pending',
                'metadata' => [
                    'file_size' => $fileSize,
                    'mime_type' => $mimeType,
                    'original_name' => $request->input('name'),
                    'uploaded_at' => now()->toISOString(),
                ]
            ]);

            // Dispatch processing job
            RAGFileProcessingJob::dispatch(
                [$knowledgeBase->id],
                $user->id,
                get_class(auth()->user()),
                'create'
            );

            return $this->successResponse(
                $knowledgeBase->fresh(),
                'Knowledge base created successfully from file',
                201
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }


    /**
     * Update the specified knowledge base.
     */
    public function update(KnowledgeBaseRequest $request, string $uuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $knowledgeBase = KnowledgeBase::where('uuid', $uuid)
                ->forOwner($user->id, get_class($user))
                ->firstOrFail();

            $knowledgeBase->update($request->only(['name']));

            return $this->successResponse(
                $knowledgeBase->fresh(),
                __('Knowledge base name successfully')
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->safeErrorResponse($e);
        }
    }

    /**
     * Soft delete the specified knowledge base.
     */
    public function delete(string $uuid): JsonResponse
    {
        try {
            $knowledgeBase = KnowledgeBase::where('uuid', $uuid)
                ->forOwner(auth()->id(), get_class(auth()->user()))
                ->firstOrFail();

            $knowledgeBase->delete();

            return $this->successResponse(
                null,
                'Knowledge base deleted successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Permanently delete the specified knowledge base.
     */
    public function destroy(string $uuid): JsonResponse
    {
        try {
            $knowledgeBase = KnowledgeBase::withTrashed()
                ->where('uuid', $uuid)
                ->forOwner(auth()->id(), get_class(auth()->user()))
                ->firstOrFail();

            $knowledgeBase->deleteWithFile();

            return $this->successResponse(
                null,
                'Knowledge base permanently deleted successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Restore the specified knowledge base.
     */
    public function restore(string $uuid): JsonResponse
    {
        try {
            $knowledgeBase = KnowledgeBase::withTrashed()
                ->where('uuid', $uuid)
                ->forOwner(auth()->id(), get_class(auth()->user()))
                ->firstOrFail();

            $knowledgeBase->restore();

            return $this->successResponse(
                $knowledgeBase->fresh(),
                'Knowledge base restored successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Knowledge base not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Retrain selected knowledge bases.
     */
    public function retrain(Request $request): JsonResponse
    {
        $request->validate([
            'ids' => 'required|array|max:20',
            'ids.*' => 'string|exists:knowledge_bases,uuid',
        ]);

        try {
            $knowledgeBaseIds = $request->input('ids');

            // Verify ownership and check if files can be retrained
            $knowledgeBases = KnowledgeBase::whereIn('uuid', $knowledgeBaseIds)
                ->where('owner_id', auth()->id())
                ->where('owner_type', get_class(auth()->user()))
                ->get();

            if ($knowledgeBases->count() !== count($knowledgeBaseIds)) {
                return $this->errorResponse('Some knowledge bases not found or access denied', 404);
            }

            // Check if all can be retrained
            $cannotRetrain = $knowledgeBases->filter(function ($kb) {
                return !$kb->canBeRetrained();
            });

            if ($cannotRetrain->isNotEmpty()) {
                return $this->errorResponse(
                    'Some knowledge bases cannot be retrained. Only ready or error status files can be retrained.',
                    400
                );
            }

            // Update status to pending
            /*$knowledgeBases->each(function ($kb) {
                $kb->update([
                    'status' => 'pending',
                    'metadata' => array_merge($kb->metadata ?? [], [
                        'retrain_requested_at' => now()->toISOString(),
                    ])
                ]);
            });*/

            $ids = $knowledgeBases->pluck('id')->toArray();

            // Dispatch RAG processing job
            RAGFileProcessingJob::dispatch(
                $ids,
                auth()->id(),
                get_class(auth()->user()),
                'retrain'
            );

            return $this->successResponse([
                'message' => 'Knowledge bases queued for retraining',
                'count' => count($knowledgeBaseIds),
                'knowledge_ids' => $knowledgeBaseIds,
            ]);

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Bulk upload multiple files.
     */
    public function bulkUpload(KnowledgeBaseBulkUploadRequest $request): JsonResponse
    {

        try {
            $user = auth()->user();
            $successful = [];
            $failed = [];

            foreach ($request->file('files') as $file) {
                try {
                    $extension = strtolower($file->getClientOriginalExtension());
                    $originalName = $file->getClientOriginalName();
                    $uuidFileName = Str::uuid() . '.' . $extension;
                    $relativePath = 'knowledge-bases/temp/' . $user->uuid . '/' . $uuidFileName;

                    Storage::putFileAs(
                        'knowledge-bases/temp/' . $user->uuid,
                        $file,
                        $uuidFileName
                    );

                    $successful[] = [
                        'name' => $originalName,
                        'storage_path' => $relativePath
                    ];

                } catch (\Exception $e) {
                    $failed[] = [
                        'name' => $file->getClientOriginalName(),
                        'error' => $e->getMessage()
                    ];
                }
            }

            return $this->successResponse([
                'successful' => $successful,
                'failed' => $failed,
                'total_uploaded' => count($successful),
                'total_failed' => count($failed)
            ], 'Bulk upload completed', 201);

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Bulk soft delete knowledge bases.
     */
    public function bulkDelete(BulkKnowledgeBaseRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $ids = $request->validated('ids');

            // Get knowledge bases that belong to the user
            $knowledgeBases = KnowledgeBase::whereIn('uuid', $ids)
                ->forOwner($user->id, get_class($user))
                ->get();

            if ($knowledgeBases->isEmpty()) {
                return $this->errorResponse('No knowledge bases found or you do not have permission to delete them', 404);
            }

            $deletedCount = 0;
            foreach ($knowledgeBases as $knowledgeBase) {
                if ($knowledgeBase->delete()) {
                    $deletedCount++;
                }
            }

            return $this->successResponse(
                ['deleted_count' => $deletedCount],
                "Successfully deleted {$deletedCount} knowledge base(s)"
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Bulk permanently delete knowledge bases.
     */
    public function bulkDestroy(BulkKnowledgeBaseRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $ids = $request->validated('ids');

            // Get knowledge bases that belong to the user (including trashed)
            $knowledgeBases = KnowledgeBase::withTrashed()
                ->whereIn('uuid', $ids)
                ->forOwner($user->id, get_class($user))
                ->get();

            if ($knowledgeBases->isEmpty()) {
                return $this->errorResponse('No knowledge bases found or you do not have permission to destroy them', 404);
            }

            $destroyedCount = 0;
            foreach ($knowledgeBases as $knowledgeBase) {
                if ($knowledgeBase->deleteWithFile()) {
                    $destroyedCount++;
                }
            }

            return $this->successResponse(
                ['destroyed_count' => $destroyedCount],
                "Successfully destroyed {$destroyedCount} knowledge base(s)"
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }

    /**
     * Bulk restore knowledge bases.
     */
    public function bulkRestore(BulkKnowledgeBaseRequest $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $ids = $request->validated('ids');

            // Get trashed knowledge bases that belong to the user
            $knowledgeBases = KnowledgeBase::withTrashed()
                ->whereIn('uuid', $ids)
                ->forOwner($user->id, get_class($user))
                ->onlyTrashed()
                ->get();

            if ($knowledgeBases->isEmpty()) {
                return $this->errorResponse('No trashed knowledge bases found or you do not have permission to restore them', 404);
            }

            $restoredCount = 0;
            foreach ($knowledgeBases as $knowledgeBase) {
                if ($knowledgeBase->restore()) {
                    $restoredCount++;
                }
            }

            return $this->successResponse(
                ['restored_count' => $restoredCount],
                "Successfully restored {$restoredCount} knowledge base(s)"
            );

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 500);
        }
    }
}
