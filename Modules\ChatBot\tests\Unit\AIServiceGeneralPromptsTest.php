<?php

namespace Modules\ChatBot\Tests\Unit;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Config;
use Modules\ChatBot\Services\AIService;
use Modules\ModelAI\Models\ModelProvider;
use Tests\TestCase;

class AIServiceGeneralPromptsTest extends TestCase
{
    use RefreshDatabase;

    private AIService $aiService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->aiService = app(AIService::class);
    }

    /** @test */
    public function it_can_generate_system_prompt()
    {
        // Mock Gemini API response
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => 'Bạn là một trợ lý AI chuyên về giáo dục toán học. Tôi có thể giúp bạn giải quyết các bài toán, gi<PERSON>i thích khái niệm và hướng dẫn phương pháp học tập hiệu quả.'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        // Set API key
        Config::set('services.google.api_key', 'test-api-key');

        $result = $this->aiService->generalPrompts('Giáo viên dạy Toán', 'system');

        $this->assertNotNull($result);
        $this->assertIsString($result);
        $this->assertStringContainsString('toán', strtolower($result));
    }

    /** @test */
    public function it_can_generate_greeting_message()
    {
        // Mock Gemini API response
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => 'Xin chào! Tôi là MathBot, sẵn sàng giúp bạn học toán!'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        Config::set('services.google.api_key', 'test-api-key');

        $result = $this->aiService->generalPrompts('MathBot', 'getting_message');

        $this->assertNotNull($result);
        $this->assertIsString($result);
        $this->assertStringContainsString('MathBot', $result);
    }

    /** @test */
    public function it_can_generate_starting_questions()
    {
        // Mock Gemini API response
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => '["Bạn có thể giải phương trình bậc hai không?", "Làm thế nào để tính đạo hàm?", "Giải thích về tích phân là gì?", "Hướng dẫn giải hệ phương trình tuyến tính?"]'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        Config::set('services.google.api_key', 'test-api-key');

        $systemPrompt = 'Bạn là trợ lý AI chuyên về toán học';
        $result = $this->aiService->generalPrompts($systemPrompt, 'starting_message');

        $this->assertNotNull($result);
        $this->assertIsString($result);
        
        // Try to decode as JSON
        $questions = json_decode($result, true);
        $this->assertIsArray($questions);
        $this->assertGreaterThan(0, count($questions));
    }

    /** @test */
    public function it_throws_exception_for_invalid_prompt_type()
    {
        $this->expectException(\Modules\Core\Exceptions\BusinessException::class);
        $this->expectExceptionMessage('Unsupported prompt type: invalid_type');

        $this->aiService->generalPrompts('Test Role', 'invalid_type');
    }

    /** @test */
    public function it_throws_exception_when_api_key_missing()
    {
        Config::set('services.google.api_key', null);

        $this->expectException(\Modules\Core\Exceptions\BusinessException::class);
        $this->expectExceptionMessage('Vui lòng cấu hình API Key cho Gemini trong file .env');

        $this->aiService->generalPrompts('Test Role', 'system');
    }

    /** @test */
    public function it_handles_api_error_response()
    {
        // Mock API error response
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'error' => [
                    'code' => 400,
                    'message' => 'Invalid request'
                ]
            ], 400)
        ]);

        Config::set('services.google.api_key', 'test-api-key');

        $this->expectException(\Modules\Core\Exceptions\BusinessException::class);
        $this->expectExceptionMessage('Lỗi khi gọi Gemini API');

        $this->aiService->generalPrompts('Test Role', 'system');
    }

    /** @test */
    public function it_handles_invalid_response_structure()
    {
        // Mock invalid response structure
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'invalid' => 'structure'
            ], 200)
        ]);

        Config::set('services.google.api_key', 'test-api-key');

        $this->expectException(\Modules\Core\Exceptions\BusinessException::class);
        $this->expectExceptionMessage('Gemini API trả về cấu trúc dữ liệu không hợp lệ');

        $this->aiService->generalPrompts('Test Role', 'system');
    }

    /** @test */
    public function it_handles_network_error()
    {
        // Mock network error
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response('', 500)
        ]);

        Config::set('services.google.api_key', 'test-api-key');

        $this->expectException(\Modules\Core\Exceptions\BusinessException::class);

        $this->aiService->generalPrompts('Test Role', 'system');
    }

    /** @test */
    public function it_generates_correct_prompt_templates()
    {
        $botRole = 'Giáo viên dạy Toán';
        $botName = 'MathBot';
        $systemPrompt = 'Test system prompt';

        // Test system prompt template
        $systemTemplate = $this->aiService->getPromptTemplate($botRole, 'system');
        $this->assertStringContainsString($botRole, $systemTemplate);
        $this->assertStringContainsString('system prompt', $systemTemplate);

        // Test greeting message template
        $greetingTemplate = $this->aiService->getPromptTemplate($botName, 'getting_message');
        $this->assertStringContainsString($botName, $greetingTemplate);
        $this->assertStringContainsString('câu chào', $greetingTemplate);

        // Test starting questions template
        $questionsTemplate = $this->aiService->getPromptTemplate($systemPrompt, 'starting_message');
        $this->assertStringContainsString($systemPrompt, $questionsTemplate);
        $this->assertStringContainsString('JSON', $questionsTemplate);
    }

    /** @test */
    public function it_returns_null_for_invalid_template_type()
    {
        $result = $this->aiService->getPromptTemplate('Test', 'invalid_type');
        $this->assertNull($result);
    }

    /** @test */
    public function it_uses_correct_gemini_model()
    {
        Http::fake([
            'generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                ['text' => 'Test response']
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        Config::set('services.google.api_key', 'test-api-key');

        $this->aiService->generalPrompts('Test Role', 'system');

        // Verify the correct model endpoint was called
        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'gemini-1.5-flash:generateContent');
        });
    }

    /** @test */
    public function it_sends_correct_payload_structure()
    {
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                ['text' => 'Test response']
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);

        Config::set('services.google.api_key', 'test-api-key');

        $this->aiService->generalPrompts('Test Role', 'system');

        // Verify the payload structure
        Http::assertSent(function ($request) {
            $data = $request->data();
            return isset($data['contents']) &&
                   isset($data['contents'][0]['role']) &&
                   $data['contents'][0]['role'] === 'user' &&
                   isset($data['contents'][0]['parts'][0]['text']);
        });
    }
}
