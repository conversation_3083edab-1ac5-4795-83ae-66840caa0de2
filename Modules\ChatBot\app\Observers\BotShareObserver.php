<?php

namespace Modules\ChatBot\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\ChatBot\Models\BotShare;

class BotShareObserver
{
    /**
     * Cache tags for bot shares
     */
    private const CACHE_TAGS = ['bot-shares', 'bots'];

    /**
     * Handle the BotShare "created" event.
     */
    public function created(BotShare $botShare): void
    {
        $this->clearCache($botShare);
    }

    /**
     * Handle the BotShare "updated" event.
     */
    public function updated(BotShare $botShare): void
    {
        $this->clearCache($botShare);
    }

    /**
     * Handle the BotShare "deleted" event.
     */
    public function deleted(BotShare $botShare): void
    {
        $this->clearCache($botShare);
    }

    /**
     * Handle the BotShare "restored" event.
     */
    public function restored(BotShare $botShare): void
    {
        $this->clearCache($botShare);
    }

    /**
     * Handle the BotShare "force deleted" event.
     */
    public function forceDeleted(BotShare $botShare): void
    {
        $this->clearCache($botShare);
    }

    /**
     * Clear bot share related cache.
     */
    private function clearCache(BotShare $botShare): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                // Clear tagged cache
                Cache::tags(self::CACHE_TAGS)->flush();
                
                // Clear specific cache keys related to bot sharing
                $cacheKeys = [
                    'bot_shares.all',
                    'bot_shares.active',
                    "bot_shares.bot.{$botShare->bot_id}",
                    "bot_shares.user.{$botShare->user_id}",
                    "bot_shares.shareable.{$botShare->shareable_type}.{$botShare->shareable_id}",
                    "bot.{$botShare->bot_id}.shares",
                    "bot.{$botShare->bot_id}.shared_users",
                    "bot.{$botShare->bot_id}.sharing_stats",
                    "user.{$botShare->user_id}.shared_bots",
                    "user.{$botShare->user_id}.accessible_bots",
                ];

                foreach ($cacheKeys as $key) {
                    Cache::forget($key);
                }

                // Clear paginated cache for different parameters
                for ($page = 1; $page <= 10; $page++) {
                    for ($limit = 10; $limit <= 50; $limit += 10) {
                        Cache::forget("bot_shares.paginated.{$page}.{$limit}");
                        Cache::forget("bot_shares.bot.{$botShare->bot_id}.paginated.{$page}.{$limit}");
                        Cache::forget("bot_shares.user.{$botShare->user_id}.paginated.{$page}.{$limit}");
                    }
                }

                // Clear bot-specific cache that might be affected
                if ($botShare->bot_id) {
                    $botCacheKeys = [
                        "bot.{$botShare->bot_id}.accessible_by_user",
                        "bot.{$botShare->bot_id}.permissions",
                        "bot.{$botShare->bot_id}.stats",
                    ];

                    foreach ($botCacheKeys as $key) {
                        Cache::forget($key);
                    }
                }
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
            \Log::warning('Failed to clear BotShare cache', [
                'bot_share_id' => $botShare->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
