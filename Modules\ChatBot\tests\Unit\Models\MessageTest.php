<?php

namespace Modules\ChatBot\Tests\Unit\Models;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;

class MessageTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate', ['--database' => 'testing']);
    }

    /** @test */
    public function it_can_create_a_message()
    {
        $conversation = Conversation::factory()->create();

        $message = Message::factory()->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::USER,
            'content' => 'Test message content',
        ]);

        $this->assertInstanceOf(Message::class, $message);
        $this->assertEquals($conversation->id, $message->conversation_id);
        $this->assertEquals(MessageRole::USER, $message->role);
        $this->assertEquals('Test message content', $message->content);
    }

    /** @test */
    public function it_belongs_to_a_conversation()
    {
        $conversation = Conversation::factory()->create();
        $message = Message::factory()->create(['conversation_id' => $conversation->id]);

        $this->assertInstanceOf(Conversation::class, $message->conversation);
        $this->assertEquals($conversation->id, $message->conversation->id);
    }

    /** @test */
    public function it_can_scope_messages_by_role()
    {
        $conversation = Conversation::factory()->create();
        
        Message::factory()->count(2)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::USER,
        ]);
        Message::factory()->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::ASSISTANT,
        ]);

        $userMessages = Message::fromUser()->get();
        $assistantMessages = Message::fromAssistant()->get();

        $this->assertCount(2, $userMessages);
        $this->assertCount(1, $assistantMessages);
        $this->assertTrue($userMessages->every(fn($msg) => $msg->role === MessageRole::USER));
        $this->assertTrue($assistantMessages->every(fn($msg) => $msg->role === MessageRole::ASSISTANT));
    }

    /** @test */
    public function it_can_scope_messages_by_status()
    {
        Message::factory()->create(['status' => MessageStatus::COMPLETED]);
        Message::factory()->create(['status' => MessageStatus::FAILED]);
        Message::factory()->create(['status' => MessageStatus::PENDING]);

        $completedMessages = Message::completed()->get();
        $failedMessages = Message::failed()->get();
        $pendingMessages = Message::pending()->get();

        $this->assertCount(1, $completedMessages);
        $this->assertCount(1, $failedMessages);
        $this->assertCount(1, $pendingMessages);
    }

    /** @test */
    public function it_can_scope_messages_for_conversation()
    {
        $conversation1 = Conversation::factory()->create();
        $conversation2 = Conversation::factory()->create();

        Message::factory()->count(3)->create(['conversation_id' => $conversation1->id]);
        Message::factory()->count(2)->create(['conversation_id' => $conversation2->id]);

        $conv1Messages = Message::forConversation($conversation1->id)->get();

        $this->assertCount(3, $conv1Messages);
        $this->assertTrue($conv1Messages->every(fn($msg) => $msg->conversation_id === $conversation1->id));
    }

    /** @test */
    public function it_can_check_if_message_has_tool_calls()
    {
        $messageWithTools = Message::factory()->create([
            'tool_calls' => [
                ['id' => 'call_123', 'function' => ['name' => 'test_tool']],
            ],
        ]);

        $messageWithoutTools = Message::factory()->create([
            'tool_calls' => null,
        ]);

        $this->assertTrue($messageWithTools->hasToolCalls());
        $this->assertFalse($messageWithoutTools->hasToolCalls());
    }

    /** @test */
    public function it_can_check_if_message_has_attachments()
    {
        $messageWithAttachments = Message::factory()->create([
            'attachments' => [
                ['type' => 'image', 'url' => 'https://example.com/image.jpg'],
            ],
        ]);

        $messageWithoutAttachments = Message::factory()->create([
            'attachments' => null,
        ]);

        $this->assertTrue($messageWithAttachments->hasAttachments());
        $this->assertFalse($messageWithoutAttachments->hasAttachments());
    }



    /** @test */
    public function it_can_check_completion_status()
    {
        $completedMessage = Message::factory()->create(['status' => MessageStatus::COMPLETED]);
        $pendingMessage = Message::factory()->create(['status' => MessageStatus::PENDING]);

        $this->assertTrue($completedMessage->isCompleted());
        $this->assertFalse($pendingMessage->isCompleted());
    }

    /** @test */
    public function it_can_check_failure_status()
    {
        $failedMessage = Message::factory()->create(['status' => MessageStatus::FAILED]);
        $completedMessage = Message::factory()->create(['status' => MessageStatus::COMPLETED]);

        $this->assertTrue($failedMessage->hasFailed());
        $this->assertFalse($completedMessage->hasFailed());
    }

    /** @test */
    public function it_can_mark_as_completed()
    {
        $message = Message::factory()->create(['status' => MessageStatus::PENDING]);

        $result = $message->markAsCompleted();

        $this->assertTrue($result);
        $this->assertEquals(MessageStatus::COMPLETED, $message->fresh()->status);
        $this->assertNotNull($message->fresh()->completed_at);
    }

    /** @test */
    public function it_can_mark_as_failed()
    {
        $message = Message::factory()->create(['status' => MessageStatus::PENDING]);

        $result = $message->markAsFailed('Test error', 'TEST_ERROR');

        $this->assertTrue($result);
        $this->assertEquals(MessageStatus::FAILED, $message->fresh()->status);
        $this->assertEquals('Test error', $message->fresh()->error_message);
        $this->assertEquals('TEST_ERROR', $message->fresh()->error_code);
        $this->assertNotNull($message->fresh()->completed_at);
    }

    /** @test */
    public function it_can_calculate_response_time()
    {
        $startTime = now();
        $endTime = $startTime->addSeconds(5);

        $message = Message::factory()->create([
            'started_at' => $startTime,
            'completed_at' => $endTime,
        ]);

        $responseTime = $message->calculateResponseTime();

        $this->assertEquals(5000, $responseTime); // 5 seconds = 5000 milliseconds
    }

    /** @test */
    public function it_returns_null_response_time_when_timestamps_missing()
    {
        $message = Message::factory()->create([
            'started_at' => null,
            'completed_at' => null,
        ]);

        $responseTime = $message->calculateResponseTime();

        $this->assertNull($responseTime);
    }

    /** @test */
    public function it_can_get_total_cost()
    {
        $message = Message::factory()->create(['cost' => 0.05]);

        $this->assertEquals(0.05, $message->getTotalCost());
    }

    /** @test */
    public function it_returns_zero_cost_when_null()
    {
        $message = Message::factory()->create(['cost' => null]);

        $this->assertEquals(0.0, $message->getTotalCost());
    }

    /** @test */
    public function it_can_calculate_token_efficiency()
    {
        $message = Message::factory()->create([
            'total_tokens' => 1000,
            'response_time_ms' => 2000, // 2 seconds
        ]);

        $efficiency = $message->getTokenEfficiency();

        $this->assertEquals(500, $efficiency); // 1000 tokens / 2 seconds = 500 tokens/second
    }

    /** @test */
    public function it_returns_null_efficiency_when_data_missing()
    {
        $message = Message::factory()->create([
            'total_tokens' => null,
            'response_time_ms' => null,
        ]);

        $efficiency = $message->getTokenEfficiency();

        $this->assertNull($efficiency);
    }

    /** @test */
    public function it_casts_enums_correctly()
    {
        $message = Message::factory()->create([
            'role' => MessageRole::USER,
            'status' => MessageStatus::COMPLETED,
            'content_type' => ContentType::TEXT,
        ]);

        $this->assertInstanceOf(MessageRole::class, $message->role);
        $this->assertInstanceOf(MessageStatus::class, $message->status);
        $this->assertInstanceOf(ContentType::class, $message->content_type);
    }

    /** @test */
    public function it_casts_json_fields_correctly()
    {
        $toolCalls = [['id' => 'call_123', 'function' => ['name' => 'test']]];
        $attachments = [['type' => 'image', 'url' => 'test.jpg']];
        $metadata = ['key' => 'value'];

        $message = Message::factory()->create([
            'tool_calls' => $toolCalls,
            'attachments' => $attachments,
            'metadata' => $metadata,
        ]);

        $this->assertEquals($toolCalls, $message->tool_calls);
        $this->assertEquals($attachments, $message->attachments);
        $this->assertEquals($metadata, $message->metadata);
    }
}
