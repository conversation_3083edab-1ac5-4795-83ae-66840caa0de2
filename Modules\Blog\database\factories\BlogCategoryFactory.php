<?php

namespace Modules\Blog\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Enums\BlogStatus;
use Modules\Page\Models\Page;

class BlogCategoryFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = BlogCategory::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'parent_id' => null,
            'layout' => null,
            'path' => null,
            'status' => $this->faker->randomElement(BlogStatus::cases()),
        ];
    }

    /**
     * Indicate that the category is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BlogStatus::Published,
        ]);
    }

    /**
     * Indicate that the category is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BlogStatus::Draft,
        ]);
    }

    /**
     * Indicate that the category is archived.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BlogStatus::Archived,
        ]);
    }

    /**
     * Indicate that the category has a parent.
     */
    public function withParent(BlogCategory $parent = null): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => $parent?->id ?? BlogCategory::factory(),
        ]);
    }

    /**
     * Indicate that the category has a layout.
     */
    public function withLayout(): static
    {
        return $this->state(fn (array $attributes) => [
            'layout' => $this->faker->randomElement(['default', 'grid', 'list', 'card']),
        ]);
    }

    /**
     * Create a root category (no parent).
     */
    public function root(): static
    {
        return $this->state(fn (array $attributes) => [
            'parent_id' => null,
            'path' => null,
        ]);
    }

    /**
     * Create with default translation.
     */
    public function withTranslation(string $locale = 'en'): static
    {
        return $this->afterCreating(function (BlogCategory $category) use ($locale) {
            // Create default translations
            $category->translations()->create([
                'locale' => $locale,
                'name' => $this->faker->words(2, true),
                'slug' => $this->faker->unique()->slug(2),
                'image' => $this->faker->optional(0.4)->imageUrl(400, 300, 'business'),
                'description' => $this->faker->optional(0.7)->paragraph(3),
                'meta_title' => $this->faker->sentence(3),
                'meta_description' => $this->faker->paragraph(2),
            ]);
        });
    }
}
