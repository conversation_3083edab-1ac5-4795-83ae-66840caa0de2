<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Query extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'question',
        'owner_id',
        'owner_type',
        'bot_id',
        'conversation_id',
        'message_id',
        'file_ids',
        'top_k',
        'collection',
        'status',
        'error_message',
        'results',
        'metadata',
        'processing_started_at',
        'completed_at',
    ];

    protected $casts = [
        'file_ids' => 'array',
        'results' => 'array',
        'metadata' => 'array',
        'processing_started_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    protected $dates = [
        'processing_started_at',
        'completed_at',
        'created_at',
        'updated_at',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the owner of the query (polymorphic).
     */
    public function owner(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the bot associated with the query.
     */
    public function bot(): BelongsTo
    {
        return $this->belongsTo(Bot::class);
    }

    /**
     * Get the conversation associated with the query.
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class);
    }

    /**
     * Get the message associated with the query.
     */
    public function message(): BelongsTo
    {
        return $this->belongsTo(Message::class);
    }

    /**
     * Scope queries for a specific owner.
     */
    public function scopeForOwner($query, $ownerId, $ownerType)
    {
        return $query->where('owner_id', $ownerId)
                    ->where('owner_type', $ownerType);
    }

    /**
     * Scope queries by status.
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope pending queries.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope processing queries.
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', 'processing');
    }

    /**
     * Scope completed queries.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope failed queries.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope RAG completed queries.
     */
    public function scopeRagCompleted($query)
    {
        return $query->where('status', 'rag_completed');
    }

    /**
     * Scope LLM processing queries.
     */
    public function scopeLlmProcessing($query)
    {
        return $query->where('status', 'llm_processing');
    }

    /**
     * Create a new query from user input.
     */
    public static function createFromUserInput(
        string $question,
        int $ownerId,
        string $ownerType,
        ?int $botId = null,
        ?int $conversationId = null,
        ?int $messageId = null,
        ?array $fileIds = null,
        int $topK = 5,
        string $collection = 'documents',
        ?int $assistantMessageId = null
    ): self {
        return self::create([
            'question' => $question,
            'owner_id' => $ownerId,
            'owner_type' => $ownerType,
            'bot_id' => $botId,
            'conversation_id' => $conversationId,
            'message_id' => $messageId,
            'file_ids' => $fileIds,
            'top_k' => $topK,
            'collection' => $collection,
            'status' => 'pending',
            'metadata' => $assistantMessageId ? ['assistant_message_id' => $assistantMessageId] : null,
        ]);
    }

    /**
     * Mark query as processing.
     */
    public function markAsProcessing(): bool
    {
        return $this->update([
            'status' => 'processing',
            'processing_started_at' => now(),
        ]);
    }

    /**
     * Mark query as completed with results.
     */
    public function markAsCompleted(array $results, ?array $metadata = null): bool
    {
        return $this->update([
            'status' => 'completed',
            'results' => $results,
            'metadata' => array_merge($this->metadata ?? [], $metadata ?? []),
            'completed_at' => now(),
        ]);
    }

    /**
     * Mark query as failed with error.
     */
    public function markAsFailed(string $errorMessage, ?array $metadata = null): bool
    {
        return $this->update([
            'status' => 'failed',
            'error_message' => $errorMessage,
            'metadata' => array_merge($this->metadata ?? [], $metadata ?? []),
            'completed_at' => now(),
        ]);
    }

    /**
     * Get the processing duration in seconds.
     */
    public function getProcessingDurationAttribute(): ?int
    {
        if (!$this->processing_started_at || !$this->completed_at) {
            return null;
        }

        return $this->completed_at->diffInSeconds($this->processing_started_at);
    }

    /**
     * Check if query is still processing.
     */
    public function isProcessing(): bool
    {
        return in_array($this->status, ['processing', 'rag_completed', 'llm_processing']);
    }

    /**
     * Check if query is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if query has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if RAG processing is completed.
     */
    public function isRagCompleted(): bool
    {
        return $this->status === 'rag_completed';
    }

    /**
     * Check if LLM processing is in progress.
     */
    public function isLlmProcessing(): bool
    {
        return $this->status === 'llm_processing';
    }

    /**
     * Get formatted results for display.
     */
    public function getFormattedResults(): array
    {
        if (!$this->results) {
            return [];
        }

        return collect($this->results)->map(function ($result) {
            return [
                'file_id' => $result['fileId'] ?? null,
                'url' => $result['url'] ?? null,
                'score' => $result['score'] ?? 0,
                'snippet' => $result['snippet'] ?? '',
                'formatted_score' => number_format(($result['score'] ?? 0) * 100, 1) . '%',
            ];
        })->toArray();
    }
}
