<?php

namespace Modules\Blog\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Modules\Blog\Enums\BlogStatus;
use Modules\Core\Abstracts\AbstractFilter;

class BlogCategoryFilter extends AbstractFilter
{
    /**
     * Define the filters available for this model.
     */
    protected function filters(): array
    {
        return [
            // Basic field filters
            'status' => 'exact',
            'is_trashed' => 'exact',

            // Date range filters (following project standard pattern)
            'created_from' => ['type' => 'from', 'column' => 'created_at'],
            'created_to' => ['type' => 'to', 'column' => 'created_at'],
            'updated_from' => ['type' => 'from', 'column' => 'updated_at'],
            'updated_to' => ['type' => 'to', 'column' => 'updated_at'],

            // Translation-based filters
            'name' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'name'
            ],
            'slug' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'slug'
            ],
            'locale' => [
                'type' => 'exact',
                'relation' => 'translations',
                'column' => 'locale'
            ],
            'description' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'meta_title'
            ],
            'meta_title' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'meta_title'
            ],
            'meta_description' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'meta_description'
            ],
            'meta_keywords' => [
                'type' => 'like',
                'relation' => 'translations',
                'column' => 'meta_keywords'
            ],

            // Special filters
            'search' => 'search'
        ];
    }

    /**
     * Handle search filter (searches across multiple fields).
     */
    protected function applySearchFilter($query, $value): void
    {
        $escapedValue = $this->escapeLike($value);

        $query->where(function ($q) use ($escapedValue) {
            // Search in translations
            $q->whereHas('translations', function ($translationQuery) use ($escapedValue) {
                $translationQuery
                    ->where('name', 'LIKE', "%{$escapedValue}%")
                    ->orWhere('slug', 'LIKE', "%{$escapedValue}%")
                    ->orWhere('description', 'LIKE', "%{$escapedValue}%")
                    ->orWhere('meta_title', 'LIKE', "%{$escapedValue}%")
                    ->orWhere('meta_description', 'LIKE', "%{$escapedValue}%")
                    ->orWhere('meta_keywords', 'LIKE', "%{$escapedValue}%");
            });
        });
    }

    /**
     * Override the buildCondition method to handle custom filters.
     */
    protected function buildCondition($query, $column, $value, $type): void
    {
        switch ($type) {
            case 'search':
                $this->applySearchFilter($query, $value);
                break;
            case 'boolean':
                $method = 'apply' . ucfirst(str_replace('_', '', $column)) . 'Filter';
                if (method_exists($this, $method)) {
                    $this->$method($query, $value);
                }
                break;
            default:
                parent::buildCondition($query, $column, $value, $type);
        }
    }
}
