<?php

namespace Modules\Blog\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use PHPUnit\Framework\Attributes\Test;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Models\BlogPostTranslation;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Models\BlogCategoryTranslation;
use Modules\Blog\Enums\BlogStatus;
use Modules\User\Models\User;

class BlogControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Blog module
        $this->artisan('migrate', ['--path' => 'Modules/Blog/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_can_show_published_post_by_slug()
    {
        $user = User::factory()->create(['first_name' => 'John', 'last_name' => 'Doe']);
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'test-blog-post',
            'title' => 'Test Blog Post Title',
            'content' => '<div>Test blog content</div>',
            'summary' => 'Test blog summary',
            'meta_title' => 'Test Meta Title',
            'meta_description' => 'Test meta description',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/test-blog-post');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'status',
                'author_id',
                'published_at',
                'featured',
                'layout',
                'created_at',
                'updated_at',
                'author' => [
                    'id',
                    'first_name',
                    'last_name',
                ]
            ]
        ]);

        $this->assertEquals($post->id, $response->json('data.id'));
        $this->assertEquals(BlogStatus::Published->value, $response->json('data.status'));
        $this->assertEquals($user->id, $response->json('data.author.id'));
        $this->assertEquals('John', $response->json('data.author.first_name'));
        $this->assertEquals('Doe', $response->json('data.author.last_name'));
    }

    #[Test]
    public function it_returns_404_for_non_existent_post_slug()
    {
        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/non-existent-slug');

        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'message' => 'Post not found',
        ]);
    }

    #[Test]
    public function it_returns_404_for_draft_post()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Draft,
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'draft-post',
            'title' => 'Draft Post',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/draft-post');

        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'message' => 'Post not found',
        ]);
    }

    #[Test]
    public function it_returns_404_for_future_published_post()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->addDay(),
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'future-post',
            'title' => 'Future Post',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/future-post');

        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'message' => 'Post not found',
        ]);
    }

    #[Test]
    public function it_returns_404_for_archived_post()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Archived,
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'archived-post',
            'title' => 'Archived Post',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/archived-post');

        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'message' => 'Post not found',
        ]);
    }

    #[Test]
    public function it_respects_current_locale_for_posts()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'english-post',
            'title' => 'English Title',
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'vi',
            'slug' => 'vietnamese-post',
            'title' => 'Vietnamese Title',
        ]);

        // Test with English locale
        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/english-post');
        $response->assertStatus(200);
        $this->assertEquals($post->id, $response->json('data.id'));

        // Test Vietnamese slug with English locale (should return 404)
        $response = $this->getJson('/api/v1/blog/posts/vietnamese-post');
        $response->assertStatus(404);

        // Test with Vietnamese locale
        App::setLocale('vi');
        $response = $this->getJson('/api/v1/blog/posts/vietnamese-post');
        $response->assertStatus(200);
        $this->assertEquals($post->id, $response->json('data.id'));

        // Test English slug with Vietnamese locale (should return 404)
        $response = $this->getJson('/api/v1/blog/posts/english-post');
        $response->assertStatus(404);
    }

    #[Test]
    public function it_loads_author_relationship_for_posts()
    {
        $user = User::factory()->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
        ]);
        
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'test-post',
            'title' => 'Test Post',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/test-post');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'author' => [
                    'id',
                    'first_name',
                    'last_name',
                ]
            ]
        ]);

        $this->assertEquals($user->id, $response->json('data.author.id'));
        $this->assertEquals('Jane', $response->json('data.author.first_name'));
        $this->assertEquals('Smith', $response->json('data.author.last_name'));
    }

    #[Test]
    public function it_returns_404_for_soft_deleted_post()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'deleted-post',
            'title' => 'Deleted Post',
        ]);

        // Soft delete the post
        $post->delete();

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/deleted-post');

        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'message' => 'Post not found',
        ]);
    }

    #[Test]
    public function it_hides_translations_in_post_response()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'test-post',
            'title' => 'Test Post',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/test-post');

        $response->assertStatus(200);
        $responseData = $response->json('data');
        $this->assertArrayNotHasKey('translations', $responseData);
    }

    #[Test]
    public function it_includes_featured_flag_in_post_response()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
            'featured' => true,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'featured-post',
            'title' => 'Featured Post',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/featured-post');

        $response->assertStatus(200);
        $this->assertTrue($response->json('data.featured'));
    }

    #[Test]
    public function it_returns_consistent_post_response_structure()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'consistent-post',
            'title' => 'Consistent Post',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/posts/consistent-post');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'id',
                'status',
                'author_id',
                'published_at',
                'featured',
                'layout',
                'created_at',
                'updated_at',
                'author',
            ]
        ]);

        $this->assertTrue($response->json('success'));
        $this->assertIsString($response->json('message'));
        $this->assertIsArray($response->json('data'));
    }

    #[Test]
    public function it_can_list_blog_posts()
    {
        $user = User::factory()->create();
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active]);

        // Create published posts
        $post1 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);
        $post2 = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDays(2),
            'author_id' => $user->id,
        ]);

        // Create draft post (should not appear)
        $draftPost = BlogPost::factory()->create([
            'status' => BlogStatus::Draft,
            'author_id' => $user->id,
        ]);

        // Attach published posts to category
        $post1->categories()->attach($category->id);
        $post2->categories()->attach($category->id);
        $draftPost->categories()->attach($category->id);

        // Create translations
        BlogPostTranslation::factory()->create(['post_id' => $post1->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $post2->id, 'locale' => 'en']);
        BlogPostTranslation::factory()->create(['post_id' => $draftPost->id, 'locale' => 'en']);

        App::setLocale('en');
        $response = $this->getJson("/api/v1/blog/posts?category_id={$category->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'title',
                    'slug',
                    'summary',
                    'image',
                    'published_at',
                    'author'
                ]
            ],
            'total'
        ]);

        $this->assertCount(2, $response->json('data')); // Only published posts
    }

    #[Test]
    public function it_can_list_blog_categories()
    {
        // Create active categories
        $category1 = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);
        $category2 = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);

        // Create inactive category (should not appear)
        $inactiveCategory = BlogCategory::factory()->create(['status' => BlogStatus::Inactive, 'parent_id' => null]);

        // Create translations
        BlogCategoryTranslation::factory()->create(['category_id' => $category1->id, 'locale' => 'en']);
        BlogCategoryTranslation::factory()->create(['category_id' => $category2->id, 'locale' => 'en']);
        BlogCategoryTranslation::factory()->create(['category_id' => $inactiveCategory->id, 'locale' => 'en']);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/categories');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                '*' => [
                    'id',
                    'status',
                    'parent_id',
                    'depth',
                    'children'
                ]
            ]
        ]);

        $this->assertCount(2, $response->json('data')); // Only active categories
    }

    #[Test]
    public function it_can_show_category_by_slug()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active]);
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'en',
            'slug' => 'technology',
            'name' => 'Technology',
        ]);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/categories/technology');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'category' => [
                    'id',
                    'status',
                    'parent_id',
                    'depth',
                    'layout',
                    'path',
                    'created_at',
                    'updated_at',
                ],
                'posts',
                'posts_count'
            ]
        ]);

        $this->assertEquals($category->id, $response->json('data.category.id'));
    }

    #[Test]
    public function it_returns_404_for_non_existent_category_slug()
    {
        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/categories/non-existent-category');

        $response->assertStatus(404);
        $response->assertJson([
            'success' => false,
            'message' => 'Blog category not found.',
        ]);
    }

    #[Test]
    public function it_can_get_posts_by_category()
    {
        $user = User::factory()->create();
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active]);

        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
            'author_id' => $user->id,
        ]);

        // Attach post to category
        $post->categories()->attach($category->id);

        // Create translations
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'en',
            'slug' => 'technology'
        ]);
        BlogPostTranslation::factory()->create(['post_id' => $post->id, 'locale' => 'en']);

        App::setLocale('en');
        $response = $this->getJson('/api/v1/blog/categories/technology');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'message',
            'data' => [
                'category' => [
                    'id',
                    'status',
                    'parent_id',
                    'depth',
                    'layout',
                    'path',
                    'created_at',
                    'updated_at',
                ],
                'posts' => [
                    '*' => [
                        'id',
                        'title',
                        'slug',
                        'summary',
                        'image',
                        'published_at',
                        'author'
                    ]
                ],
                'posts_count'
            ]
        ]);

        $this->assertCount(1, $response->json('data.posts'));
        $this->assertEquals($post->id, $response->json('data.posts.0.id'));
    }
}
