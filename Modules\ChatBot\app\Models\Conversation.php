<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Modules\ChatBot\Database\Factories\ConversationFactory;
use Modules\ChatBot\Enums\ConversationStatus;
use Illuminate\Support\Str;

class Conversation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'title',
        'bot_id',
        'owner_id',
        'owner_type',
        'status',
        'message_count',
        'last_message_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'id',
        'bot_id',
        'owner_id',
        'owner_type',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'status' => ConversationStatus::class,
        'message_count' => 'integer',
        'last_message_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Generate UUID on creation
        static::creating(function ($conversation) {
            if (empty($conversation->uuid)) {
                $conversation->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return ConversationFactory::new();
    }

    /**
     * Get the bot that owns this conversation.
     */
    public function bot(): BelongsTo
    {
        return $this->belongsTo(Bot::class, 'bot_id', 'id');
    }

    /**
     * Get the owner that owns this conversation (polymorphic).
     */
    public function owner(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get all messages for this conversation.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class, 'conversation_id', 'id')->orderBy('created_at');
    }

    /**
     * Get the latest message for this conversation.
     */
    public function latestMessage(): HasMany
    {
        return $this->hasMany(Message::class)->latest();
    }

    /**
     * Get knowledge bases associated with this conversation.
     */
    public function knowledgeBases(): BelongsToMany
    {
        return $this->belongsToMany(KnowledgeBase::class, 'conversation_knowledge_bases');
    }

    /**
     * Get ready knowledge bases for this conversation.
     */
    public function readyKnowledgeBases(): BelongsToMany
    {
        return $this->knowledgeBases()->where('knowledge_bases.status', 'ready');
    }

    /**
     * Scope a query to only include active conversations.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', ConversationStatus::ACTIVE);
    }

    /**
     * Scope a query to only include completed conversations.
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', ConversationStatus::COMPLETED);
    }

    /**
     * Scope a query to only include archived conversations.
     */
    public function scopeArchived(Builder $query): Builder
    {
        return $query->where('status', ConversationStatus::ARCHIVED);
    }

    /**
     * Scope a query to filter by bot.
     */
    public function scopeForBot(Builder $query, int $botId): Builder
    {
        return $query->where('bot_id', $botId);
    }

    /**
     * Scope a query to filter by user (legacy method for backward compatibility).
     */
    public function scopeForUser(Builder $query, int $userId, string $userType): Builder
    {
        return $query->where('owner_id', $userId)->where('owner_type', $userType);
    }

    /**
     * Scope a query to filter by owner.
     */
    public function scopeForOwner(Builder $query, int $ownerId, string $ownerType): Builder
    {
        return $query->where('owner_id', $ownerId)->where('owner_type', $ownerType);
    }

    /**
     * Update message count and last message timestamp.
     */
    public function updateMessageStats(): void
    {
        $this->update([
            'message_count' => $this->messages()->count(),
            'last_message_at' => $this->messages()->latest()->first()?->created_at,
        ]);
    }


}
