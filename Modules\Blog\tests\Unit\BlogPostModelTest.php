<?php

namespace Modules\Blog\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Models\BlogPostTranslation;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Enums\BlogStatus;
use Modules\User\Models\User;

class BlogPostModelTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Blog module
        $this->artisan('migrate', ['--path' => 'Modules/Blog/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_has_correct_fillable_attributes()
    {
        $post = new BlogPost();
        $expected = [
            'featured',
            'layout',
            'author_id',
            'status',
            'published_at',
            'views'
        ];

        $this->assertEquals($expected, $post->getFillable());
    }

    #[Test]
    public function it_has_correct_casts()
    {
        $post = new BlogPost();
        $expected = [
            'id' => 'int',
            'featured' => 'boolean',
            'author_id' => 'integer',
            'status' => BlogStatus::class,
            'published_at' => 'datetime',
            'created_at' => 'datetime',
            'updated_at' => 'datetime',
            'deleted_at' => 'datetime',
        ];

        $this->assertEquals($expected, $post->getCasts());
    }

    #[Test]
    public function it_has_correct_hidden_attributes()
    {
        $post = new BlogPost();
        $expected = ['deleted_at'];

        $this->assertEquals($expected, $post->getHidden());
    }

    #[Test]
    public function it_can_create_a_post()
    {
        $user = User::factory()->create();
        
        $postData = [
            'featured' => true,
            'layout' => 'default',
            'author_id' => $user->id,
            'status' => BlogStatus::Published,
            'published_at' => now(),
        ];

        $post = BlogPost::create($postData);

        $this->assertInstanceOf(BlogPost::class, $post);
        $this->assertDatabaseHas('blog_posts', [
            'id' => $post->id,
            'status' => BlogStatus::Published->value,
            'author_id' => $user->id,
            'featured' => true,
        ]);
        $this->assertEquals(BlogStatus::Published, $post->status);
        $this->assertTrue($post->featured);
    }

    #[Test]
    public function it_has_translations_relationship()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create(['author_id' => $user->id]);
        $translation = BlogPostTranslation::factory()->create(['post_id' => $post->id]);

        $this->assertInstanceOf(\Illuminate\Database\Eloquent\Collection::class, $post->translations);
        $this->assertTrue($post->translations->contains($translation));
    }

    #[Test]
    public function it_has_author_relationship()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create(['author_id' => $user->id]);

        $this->assertInstanceOf(User::class, $post->author);
        $this->assertEquals($user->id, $post->author->id);
    }

    #[Test]
    public function it_has_categories_relationship()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create(['author_id' => $user->id]);
        $category1 = BlogCategory::factory()->create();
        $category2 = BlogCategory::factory()->create();

        $post->categories()->attach([$category1->id, $category2->id]);

        $categories = $post->categories;
        
        $this->assertCount(2, $categories);
        $this->assertTrue($categories->contains('id', $category1->id));
        $this->assertTrue($categories->contains('id', $category2->id));
    }

    #[Test]
    public function it_can_scope_published_posts()
    {
        $user = User::factory()->create();
        
        $publishedPost = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
        ]);
        
        $draftPost = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Draft,
        ]);

        $futurePublishedPost = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Published,
            'published_at' => now()->addDay(),
        ]);

        $publishedPosts = BlogPost::published()->get();

        $this->assertTrue($publishedPosts->contains($publishedPost));
        $this->assertFalse($publishedPosts->contains($draftPost));
        $this->assertFalse($publishedPosts->contains($futurePublishedPost));
    }

    #[Test]
    public function it_can_scope_featured_posts()
    {
        $user = User::factory()->create();
        
        $featuredPost = BlogPost::factory()->create(['author_id' => $user->id, 'featured' => true]);
        $regularPost = BlogPost::factory()->create(['author_id' => $user->id, 'featured' => false]);

        $featuredPosts = BlogPost::featured()->get();

        $this->assertTrue($featuredPosts->contains($featuredPost));
        $this->assertFalse($featuredPosts->contains($regularPost));
    }

    #[Test]
    public function it_can_scope_by_status()
    {
        $user = User::factory()->create();
        
        $publishedPost = BlogPost::factory()->create(['author_id' => $user->id, 'status' => BlogStatus::Published]);
        $draftPost = BlogPost::factory()->create(['author_id' => $user->id, 'status' => BlogStatus::Draft]);

        $publishedPosts = BlogPost::status(BlogStatus::Published)->get();
        $draftPosts = BlogPost::status(BlogStatus::Draft)->get();

        $this->assertTrue($publishedPosts->contains($publishedPost));
        $this->assertFalse($publishedPosts->contains($draftPost));
        $this->assertTrue($draftPosts->contains($draftPost));
        $this->assertFalse($draftPosts->contains($publishedPost));
    }

    #[Test]
    public function it_can_get_post_by_slug()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay()
        ]);
        $translation = BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'slug' => 'test-post',
            'locale' => 'en',
        ]);

        $foundPost = BlogPost::getPostBySlug('test-post', 'en');

        $this->assertNotNull($foundPost);
        $this->assertEquals($post->id, $foundPost->id);
    }

    #[Test]
    public function it_can_check_if_post_is_published()
    {
        $user = User::factory()->create();
        
        $publishedPost = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Published,
            'published_at' => now()->subDay(),
        ]);

        $draftPost = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Draft,
        ]);

        $futurePublishedPost = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Published,
            'published_at' => now()->addDay(),
        ]);

        $this->assertTrue($publishedPost->isPublished());
        $this->assertFalse($draftPost->isPublished());
        $this->assertFalse($futurePublishedPost->isPublished());
    }

    #[Test]
    public function it_can_check_if_post_is_featured()
    {
        $user = User::factory()->create();

        $featuredPost = BlogPost::factory()->create(['author_id' => $user->id, 'featured' => true]);
        $regularPost = BlogPost::factory()->create(['author_id' => $user->id, 'featured' => false]);

        $this->assertTrue($featuredPost->featured);
        $this->assertFalse($regularPost->featured);
    }

    #[Test]
    public function it_can_get_title_for_specific_locale()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create(['author_id' => $user->id]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'title' => 'English Title',
        ]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'vi',
            'title' => 'Vietnamese Title',
        ]);

        // Test accessing translation data directly
        $post->load(['translations' => fn($q) => $q->where('locale', 'en')]);
        $this->assertEquals('English Title', $post->translations->first()->title);
    }

    #[Test]
    public function it_can_get_slug_for_specific_locale()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create(['author_id' => $user->id]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'english-slug',
        ]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'vi',
            'slug' => 'vietnamese-slug',
        ]);

        // Test accessing translation data directly
        $post->load(['translations' => fn($q) => $q->where('locale', 'en')]);
        $this->assertEquals('english-slug', $post->translations->first()->slug);
    }

    #[Test]
    public function it_can_publish_a_post()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Draft,
            'published_at' => null,
        ]);

        // Manually publish the post
        $post->update([
            'status' => BlogStatus::Published,
            'published_at' => now()
        ]);

        $this->assertEquals(BlogStatus::Published, $post->status);
        $this->assertNotNull($post->published_at);
    }

    #[Test]
    public function it_preserves_published_at_when_publishing_if_already_set()
    {
        $user = User::factory()->create();
        $publishedAt = now()->subDay();
        $post = BlogPost::factory()->create([
            'author_id' => $user->id,
            'status' => BlogStatus::Draft,
            'published_at' => $publishedAt,
        ]);

        // Manually publish the post
        $post->update(['status' => BlogStatus::Published]);

        $this->assertEquals(BlogStatus::Published, $post->status);
        $this->assertEquals($publishedAt->format('Y-m-d H:i:s'), $post->published_at->format('Y-m-d H:i:s'));
    }

    #[Test]
    public function it_has_correct_table_structure()
    {
        $this->assertTrue(Schema::hasTable('blog_posts'));

        $expectedColumns = [
            'id', 'featured', 'layout', 'author_id', 'status', 'published_at',
            'created_at', 'updated_at', 'deleted_at'
        ];

        foreach ($expectedColumns as $column) {
            $this->assertTrue(Schema::hasColumn('blog_posts', $column));
        }
    }

    #[Test]
    public function it_has_factory()
    {
        $post = BlogPost::factory()->make();

        $this->assertInstanceOf(BlogPost::class, $post);
        $this->assertNotEmpty($post->status);
    }

    #[Test]
    public function it_uses_soft_deletes()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create(['author_id' => $user->id]);
        $postId = $post->id;

        $post->delete();

        $this->assertSoftDeleted('blog_posts', ['id' => $postId]);
        $this->assertNotNull($post->fresh()->deleted_at);
    }

    #[Test]
    public function it_uses_translatable_trait()
    {
        $post = new BlogPost();

        $this->assertContains('Astrotomic\Translatable\Translatable', class_uses_recursive($post));
    }

    #[Test]
    public function it_has_correct_translatable_attributes()
    {
        $post = new BlogPost();
        $expected = [
            'title',
            'slug',
            'summary',
            'content',
            'image',
            'meta_title',
            'meta_description'
        ];

        $this->assertEquals($expected, $post->translatedAttributes);
    }
}
