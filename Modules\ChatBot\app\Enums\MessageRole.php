<?php

namespace Modules\ChatBot\Enums;

enum MessageRole: string
{
    case USER = 'user';
    case ASSISTANT = 'assistant';
    case SYSTEM = 'system';
    case TOOL = 'tool';

    /**
     * Get all enum values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get the label for the role.
     */
    public function label(): string
    {
        return match ($this) {
            self::USER => 'Người dùng',
            self::ASSISTANT => 'Trợ lý AI',
            self::SYSTEM => 'Hệ thống',
            self::TOOL => 'Công cụ',
        };
    }

    /**
     * Get the color for the role.
     */
    public function color(): string
    {
        return match ($this) {
            self::USER => 'primary',
            self::ASSISTANT => 'success',
            self::SYSTEM => 'warning',
            self::TOOL => 'info',
        };
    }

    /**
     * Get the icon for the role.
     */
    public function icon(): string
    {
        return match ($this) {
            self::USER => 'fas fa-user',
            self::ASSISTANT => 'fas fa-robot',
            self::SYSTEM => 'fas fa-cog',
            self::TOOL => 'fas fa-tools',
        };
    }

    /**
     * Check if the role is from a human.
     */
    public function isHuman(): bool
    {
        return $this === self::USER;
    }

    /**
     * Check if the role is from AI.
     */
    public function isAI(): bool
    {
        return $this === self::ASSISTANT;
    }

    /**
     * Check if the role is system-generated.
     */
    public function isSystem(): bool
    {
        return in_array($this, [self::SYSTEM, self::TOOL]);
    }

    /**
     * Get roles that can be displayed to users.
     */
    public static function displayableRoles(): array
    {
        return [self::USER, self::ASSISTANT];
    }

    /**
     * Get roles that are internal/system.
     */
    public static function internalRoles(): array
    {
        return [self::SYSTEM, self::TOOL];
    }
}
