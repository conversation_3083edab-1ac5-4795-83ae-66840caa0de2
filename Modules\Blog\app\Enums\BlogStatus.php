<?php

namespace Modules\Blog\Enums;

enum BlogStatus: string
{
    case Draft = 'draft';
    case Published = 'published';
    case Archived = 'archived';
    case Pending = 'pending';
    case Rejected = 'rejected';
    case Disabled = 'disabled';
    case Active = 'active';
    case Inactive = 'inactive';

    /**
     * Get the human-readable label for the status.
     */
    public function label(): string
    {
        return match($this) {
            self::Draft => 'Draft',
            self::Published => 'Published',
            self::Archived => 'Archived',
            self::Pending => 'Pending',
            self::Rejected => 'Rejected',
            self::Disabled => 'Disabled',
            self::Active => 'Active',
            self::Inactive => 'Inactive'
        };
    }

    /**
     * Get the color class for the status.
     */
    public function color(): string
    {
        return match($this) {
            self::Inactive, self::Draft => 'gray',
            self::Active, self::Published => 'green',
            self::Archived => 'blue',
            self::Pending => 'yellow',
            self::Rejected, self::Disabled => 'red',
        };
    }

    /**
     * Get all status options as array.
     */
    public static function options(): array
    {
        return array_map(fn($case) => [
            'value' => $case->value,
            'label' => $case->label(),
            'color' => $case->color(),
        ], self::cases());
    }
}
