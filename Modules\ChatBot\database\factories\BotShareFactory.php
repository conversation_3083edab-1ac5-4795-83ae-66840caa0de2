<?php

namespace Modules\ChatBot\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\BotShare;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\ChatBot\Models\BotShare>
 */
class BotShareFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = BotShare::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $bot = Bot::factory()->personal()->shareable()->create();
        $user = User::factory()->create();

        return [
            'bot_id' => $bot->id,
            'user_id' => $user->id,
            'shareable_id' => $user->id,
            'shareable_type' => get_class($user),
            'status' => $this->faker->randomElement(['active', 'inactive']),
        ];
    }

    /**
     * Indicate that the share is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
            'expires_at' => $this->faker->optional(0.2)->dateTimeBetween('now', '+1 year'),
        ]);
    }

    /**
     * Indicate that the share is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the share is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('-30 days', '-1 day'),
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the share never expires.
     */
    public function permanent(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => null,
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the share has read permission.
     */
    public function readPermission(): static
    {
        return $this->state(fn (array $attributes) => [
            'permission_level' => 'read',
        ]);
    }

    /**
     * Indicate that the share has write permission.
     */
    public function writePermission(): static
    {
        return $this->state(fn (array $attributes) => [
            'permission_level' => 'write',
        ]);
    }

    /**
     * Indicate that the share has admin permission.
     */
    public function adminPermission(): static
    {
        return $this->state(fn (array $attributes) => [
            'permission_level' => 'admin',
        ]);
    }

    /**
     * Indicate that the share is for a specific bot.
     */
    public function forBot(Bot $bot): static
    {
        return $this->state(fn (array $attributes) => [
            'bot_id' => $bot->id,
        ]);
    }

    /**
     * Indicate that the share is shared by a specific user.
     */
    public function sharedBy(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'shared_by_user_id' => $user->id,
        ]);
    }

    /**
     * Indicate that the share is shared with a specific user.
     */
    public function sharedWith(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'shared_with_user_id' => $user->id,
        ]);
    }

    /**
     * Indicate that the share expires in a specific timeframe.
     */
    public function expiresIn(string $timeframe): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('now', $timeframe),
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the share was created recently.
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'shared_at' => $this->faker->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Indicate that the share was created long ago.
     */
    public function old(): static
    {
        return $this->state(fn (array $attributes) => [
            'shared_at' => $this->faker->dateTimeBetween('-1 year', '-30 days'),
        ]);
    }

    /**
     * Create a share between two specific users for a specific bot.
     */
    public function between(User $sharedBy, User $sharedWith, Bot $bot): static
    {
        return $this->state(fn (array $attributes) => [
            'bot_id' => $bot->id,
            'user_id' => $sharedWith->id,
            'shareable_id' => $sharedBy->id,
            'shareable_type' => get_class($sharedBy),
            'status' => 'active',
        ]);
    }

    /**
     * Create multiple shares for the same bot.
     */
    public function forSameBot(): static
    {
        $bot = Bot::factory()->personal()->shareable()->create();

        return $this->state(fn (array $attributes) => [
            'bot_id' => $bot->id,
        ]);
    }

    /**
     * Create shares with different permission levels.
     */
    public function withRandomPermission(): static
    {
        return $this->state(fn (array $attributes) => [
            'permission_level' => $this->faker->randomElement(['read', 'write', 'admin']),
        ]);
    }

    /**
     * Create a share that will expire soon.
     */
    public function expiringSoon(): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $this->faker->dateTimeBetween('now', '+7 days'),
            'is_active' => true,
        ]);
    }

    /**
     * Create a share with specific expiration date.
     */
    public function expiresAt(\DateTimeInterface $date): static
    {
        return $this->state(fn (array $attributes) => [
            'expires_at' => $date,
            'is_active' => true,
        ]);
    }
}
