<?php

namespace Modules\ChatBot\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\Query;
use Throwable;

class QueryProcessingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300; // 5 minutes timeout
    public int $tries = 3;

    private string $queueName;

    /**
     * Create a new job instance.
     * Sử dụng PHP 8 Constructor Property Promotion để code gọn hơn.
     */
    public function __construct(protected Query $query)
    {
        $this->queueName = config('chatbot.rag.queue_name', 'rag-processing');
        $this->onQueue($this->queueName);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->query->markAsProcessing();

            $payload = $this->buildQueryPayload();
            $this->pushQueryToRedisQueue($payload);

        } catch (Throwable $e) {
            $this->query->markAsFailed($e->getMessage(), [
                'job_failed_at' => now()->toISOString(),
                'attempt' => $this->attempts(),
            ]);

            throw $e; // Ném lại exception để queue worker xử lý retry
        }
    }

    /**
     * Build the query payload for Python service.
     */
    private function buildQueryPayload(): array
    {
        $taskId = Str::uuid()->toString();

        $payload = [
            'taskId' => $taskId,
            'type' => 'query',
            'queryId' => $this->query->uuid,
            'question' => $this->query->question,
            'ownerId' => $this->query->owner_id,
            'collection' => $this->query->collection,
            'topK' => $this->query->top_k,
            'webhookUrl' => route('api.api.webhook.query'),
        ];

        if (!empty($this->query->file_ids)) {
            $payload['fileIds'] = $this->query->file_ids;
        }

        $this->query->update([
            'metadata' => array_merge($this->query->metadata ?? [], [
                'task_id' => $taskId,
                'payload_sent_at' => now()->toISOString(),
            ])
        ]);

        return $payload;
    }

    /**
     * Push query payload to Redis queue for Python service.
     */
    private function pushQueryToRedisQueue(array $payload): void
    {
        Log::info('Pushing query to Redis queue', [
            'queue' => $this->queueName,
            'query_uuid' => $this->query->uuid,
            'payload' => $payload,
        ]);

        Redis::lpush($this->queueName, json_encode($payload));

        Log::info('Query payload pushed to Redis queue successfully', [
            'queue' => $this->queueName,
            'task_id' => $payload['taskId'],
            'query_id' => $this->query->uuid,
        ]);
    }

    /**
     * Handle a job failure.
     */
    public function failed(Throwable $exception): void
    {
        Log::error('QueryProcessingJob permanently failed', [
            'query_id' => $this->query->id,
            'query_uuid' => $this->query->uuid,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts(),
        ]);

        $this->query->markAsFailed($exception->getMessage(), [
            'permanently_failed_at' => now()->toISOString(),
            'total_attempts' => $this->attempts(),
        ]);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'query',
            'query:' . $this->query->uuid,
            'owner:' . $this->query->owner_id,
            'bot:' . ($this->query->bot_id ?? 'none'),
        ];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return [30, 60, 120]; // 30s, 1min, 2min
    }
}
