<?php

namespace Modules\ChatBot\Enums;

enum BotVisibility: string
{
    case PUBLIC = 'public';
    case PRIVATE = 'private';

    /**
     * Get the label for the visibility.
     */
    public function label(): string
    {
        return match ($this) {
            self::PUBLIC => 'Công khai',
            self::PRIVATE => 'Riêng tư',
        };
    }

    /**
     * Get the description for the visibility.
     */
    public function description(): string
    {
        return match ($this) {
            self::PUBLIC => '<PERSON><PERSON> có thể được tìm thấy và sử dụng bởi người dùng khác',
            self::PRIVATE => 'Chỉ chủ sở hữu và người được chia sẻ mới có thể sử dụng',
        };
    }

    /**
     * Get the color for the visibility.
     */
    public function color(): string
    {
        return match ($this) {
            self::PUBLIC => 'green',
            self::PRIVATE => 'gray',
        };
    }

    /**
     * Check if the visibility allows public access.
     */
    public function isPublic(): bool
    {
        return $this === self::PUBLIC;
    }

    /**
     * Check if the visibility is private.
     */
    public function isPrivate(): bool
    {
        return $this === self::PRIVATE;
    }

    /**
     * Get all visibility options.
     */
    public static function options(): array
    {
        return [
            self::PUBLIC->value => self::PUBLIC->label(),
            self::PRIVATE->value => self::PRIVATE->label(),
        ];
    }
}
