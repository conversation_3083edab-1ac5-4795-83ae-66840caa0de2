# Blog Module

The Blog Module provides comprehensive blog management functionality for Laravel ProCMS, enabling creation, management, and display of multi-language blog posts and categories with advanced features like categorization, featured posts, and SEO optimization.

## Table of Contents

- [Overview](#overview)
- [Features](#features)
- [Architecture](#architecture)
- [Database Schema](#database-schema)
- [Installation](#installation)
- [API Documentation](#api-documentation)
- [Usage Examples](#usage-examples)
- [Filtering](#filtering)
- [Multi-Language Support](#multi-language-support)
- [Caching](#caching)
- [Testing](#testing)
- [Dependencies](#dependencies)
- [Contributing](#contributing)

## Overview

The Blog Module is a core component of Laravel ProCMS that handles blog content management. It supports multi-language content, hierarchical categories, featured posts, SEO optimization, and flexible publishing workflows.

### Key Components

- **BlogPost Model**: Main blog post entity with status management and publishing controls
- **BlogCategory Model**: Hierarchical category system with parent-child relationships
- **BlogPostTranslation Model**: Multi-language content storage for posts
- **BlogCategoryTranslation Model**: Multi-language content storage for categories
- **BlogPostController**: Public blog post display functionality
- **BlogCategoryController**: Public category display functionality
- **AuthBlogPostController**: Administrative CRUD operations for posts
- **AuthBlogCategoryController**: Administrative CRUD operations for categories
- **BlogService**: Business logic and data retrieval
- **BlogFilter**: Advanced filtering and search capabilities
- **BlogFacade**: Convenient access to blog functionality

## Features

### Core Features
- ✅ **Multi-language Support**: Full translation support for all content fields
- ✅ **Status Management**: Draft, Published, and Archived states for posts and categories
- ✅ **Publishing Control**: Scheduled publishing with date/time control
- ✅ **Soft Delete**: Safe deletion with restore capabilities
- ✅ **SEO Optimization**: Meta title, description, and keywords for each language
- ✅ **Category Hierarchy**: Parent-child category relationships with unlimited depth
- ✅ **Author Management**: Post authorship tracking and management

### Advanced Features
- ✅ **Featured Posts**: Mark posts as featured for special display
- ✅ **View Tracking**: Post view count tracking for popularity metrics
- ✅ **Slug Management**: Automatic slug generation with uniqueness validation
- ✅ **Advanced Filtering**: Search across content, author, dates, and status
- ✅ **Bulk Operations**: Mass delete, restore, and force delete operations
- ✅ **API-First Design**: Complete REST API with comprehensive endpoints
- ✅ **Comprehensive Testing**: 100% test coverage with complete unit and feature tests

### Content Management
- ✅ **Rich Content Storage**: HTML content with summary extraction
- ✅ **Image Management**: Featured image support for posts and categories
- ✅ **Meta Data Management**: Complete SEO meta information
- ✅ **Version Control**: Created/updated timestamps with author tracking
- ✅ **Content Validation**: Comprehensive validation rules and error handling

## Architecture

The Blog Module follows Laravel ProCMS architectural patterns:

```
Modules/Blog/
├── app/
│   ├── Enums/
│   │   └── BlogStatus.php           # Blog status enumeration
│   ├── Facades/
│   │   └── BlogFacade.php           # Service facade
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── BlogPostController.php    # Public post display
│   │   │   ├── BlogCategoryController.php # Public category display
│   │   │   └── Auth/
│   │   │       ├── BlogPostController.php     # Admin post CRUD
│   │   │       └── BlogCategoryController.php # Admin category CRUD
│   │   ├── Filters/
│   │   │   ├── BlogPostFilter.php       # Post filtering
│   │   │   └── BlogCategoryFilter.php   # Category filtering
│   │   └── Requests/
│   │       ├── BlogPostRequest.php      # Post validation
│   │       ├── BlogCategoryRequest.php  # Category validation
│   │       ├── BulkBlogPostRequest.php  # Bulk post operations
│   │       └── BulkBlogCategoryRequest.php # Bulk category operations
│   ├── Models/
│   │   ├── BlogPost.php                 # Main post model
│   │   ├── BlogCategory.php             # Main category model
│   │   ├── BlogPostTranslation.php      # Post translation model
│   │   └── BlogCategoryTranslation.php  # Category translation model
│   ├── Providers/
│   │   ├── BlogServiceProvider.php      # Service registration
│   │   └── RouteServiceProvider.php     # Route registration
│   └── Services/
│       └── BlogService.php              # Business logic
├── database/
│   ├── factories/
│   │   ├── BlogPostFactory.php          # Post factory
│   │   ├── BlogCategoryFactory.php      # Category factory
│   │   ├── BlogPostTranslationFactory.php    # Post translation factory
│   │   └── BlogCategoryTranslationFactory.php # Category translation factory
│   └── migrations/
│       ├── 2025_06_08_134754_create_blog_categories_table.php
│       ├── 2025_06_08_134755_create_blog_posts_table.php
│       └── 2025_06_08_134756_create_blog_post_category_table.php
├── routes/
│   ├── api.php                      # API routes
│   └── web.php                      # Web routes
└── tests/
    ├── Feature/                     # Integration tests
    │   ├── BlogControllerTest.php   # HTTP endpoint tests
    │   └── BlogApiTest.php          # API integration tests
    └── Unit/                        # Unit tests
        ├── BlogServiceTest.php      # Service layer tests
        ├── BlogFacadeTest.php       # Facade tests
        ├── BlogCategoryModelTest.php # Category model tests
        └── BlogPostModelTest.php    # Post model tests
```

## Database Schema

### Blog Categories Table
```sql
CREATE TABLE blog_categories (
    id BIGINT UNSIGNED PRIMARY KEY,
    parent_id BIGINT UNSIGNED NULL,
    status VARCHAR(255) DEFAULT 'active',
    layout VARCHAR(255) NULL,
    path VARCHAR(255) NULL,
    depth INTEGER DEFAULT 0,
    image VARCHAR(255) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY (parent_id) REFERENCES blog_categories(id) ON DELETE CASCADE
);
```

### Blog Posts Table
```sql
CREATE TABLE blog_posts (
    id BIGINT UNSIGNED PRIMARY KEY,
    status VARCHAR(255) DEFAULT 'draft',
    featured BOOLEAN DEFAULT FALSE,
    layout VARCHAR(255) NULL,
    image VARCHAR(255) NULL,
    views INTEGER DEFAULT 0,
    published_at TIMESTAMP NULL,
    author_id BIGINT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,

    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL
);
```

### Blog Category Translations Table
```sql
CREATE TABLE blog_category_translations (
    id BIGINT UNSIGNED PRIMARY KEY,
    category_id BIGINT UNSIGNED NOT NULL,
    locale VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT NULL,
    meta_title VARCHAR(255) NULL,
    meta_description TEXT NULL,
    meta_keywords VARCHAR(255) NULL,

    FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_category_locale (category_id, locale),
    UNIQUE KEY unique_locale_slug (locale, slug)
);
```

### Blog Post Translations Table
```sql
CREATE TABLE blog_post_translations (
    id BIGINT UNSIGNED PRIMARY KEY,
    post_id BIGINT UNSIGNED NOT NULL,
    locale VARCHAR(255) NOT NULL,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    content LONGTEXT NULL,
    summary TEXT NULL,
    image VARCHAR(255) NULL,
    meta_title VARCHAR(255) NULL,
    meta_description TEXT NULL,
    meta_keywords VARCHAR(255) NULL,

    FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    UNIQUE KEY unique_post_locale (post_id, locale),
    UNIQUE KEY unique_locale_slug (locale, slug)
);
```

### Blog Post Category Pivot Table
```sql
CREATE TABLE blog_post_category (
    id BIGINT UNSIGNED PRIMARY KEY,
    post_id BIGINT UNSIGNED NOT NULL,
    category_id BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,

    FOREIGN KEY (post_id) REFERENCES blog_posts(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES blog_categories(id) ON DELETE CASCADE,
    UNIQUE KEY unique_post_category (post_id, category_id)
);
```

### Relationships
- **BlogPost** belongs to **User** (author)
- **BlogPost** has many **BlogPostTranslations**
- **BlogPost** belongs to many **BlogCategories** (many-to-many)
- **BlogCategory** has many **BlogCategoryTranslations**
- **BlogCategory** belongs to **BlogCategory** (parent)
- **BlogCategory** has many **BlogCategories** (children)
- **BlogPostTranslation** belongs to **BlogPost**
- **BlogCategoryTranslation** belongs to **BlogCategory**

## Installation

The Blog Module is automatically registered when Laravel ProCMS is installed. No additional installation steps are required.

### Migration

Run the migrations to create the blog tables:

```bash
php artisan migrate --path=Modules/Blog/database/migrations
```

### Seeding (Optional)

If you have seeders configured, run them to populate initial data:

```bash
php artisan db:seed --class=BlogSeeder
```

## API Documentation

### Blog Posts Endpoints

#### Get Posts by Category
```http
GET /api/v1/blog/posts?category_id={id}
```

**Parameters:**
- `category_id` (required): Category ID to filter posts
- `page` (optional): Page number for pagination
- `limit` (optional): Number of posts per page (max 100)

**Response:**
```json
{
    "success": true,
    "message": "Posts retrieved successfully",
    "data": [
        {
            "id": 1,
            "status": "published",
            "featured": false,
            "layout": null,
            "image": null,
            "views": 0,
            "published_at": "2025-06-14T10:00:00.000000Z",
            "author_id": 1,
            "created_at": "2025-06-14T09:00:00.000000Z",
            "updated_at": "2025-06-14T09:00:00.000000Z",
            "author": {
                "id": 1,
                "first_name": "John",
                "last_name": "Doe"
            }
        }
    ]
}
```

#### Get Single Post by Slug
```http
GET /api/v1/blog/posts/{slug}
```

**Response:**
```json
{
    "success": true,
    "message": "Post retrieved successfully",
    "data": {
        "id": 1,
        "status": "published",
        "featured": false,
        "layout": null,
        "image": null,
        "views": 0,
        "published_at": "2025-06-14T10:00:00.000000Z",
        "author_id": 1,
        "created_at": "2025-06-14T09:00:00.000000Z",
        "updated_at": "2025-06-14T09:00:00.000000Z",
        "author": {
            "id": 1,
            "first_name": "John",
            "last_name": "Doe"
        }
    }
}
```

#### Get Featured Posts
```http
GET /api/v1/blog/posts/featured
```

#### Get Latest Posts
```http
GET /api/v1/blog/posts/latest
```

#### Get Top Posts
```http
GET /api/v1/blog/posts/top
```

### Blog Categories Endpoints

#### Get All Categories
```http
GET /api/v1/blog/categories
```

**Response:**
```json
{
    "success": true,
    "message": "Categories retrieved successfully",
    "data": [
        {
            "id": 1,
            "parent_id": null,
            "status": "active",
            "layout": null,
            "path": "technology",
            "depth": 0,
            "image": null,
            "created_at": "2025-06-14T09:00:00.000000Z",
            "updated_at": "2025-06-14T09:00:00.000000Z",
            "children": [
                {
                    "id": 2,
                    "parent_id": 1,
                    "status": "active",
                    "depth": 1,
                    "children": []
                }
            ]
        }
    ]
}
```

#### Get Single Category by Slug
```http
GET /api/v1/blog/categories/{slug}
```

#### Get Posts by Category Slug
```http
GET /api/v1/blog/categories/{slug}/posts
```

## Usage Examples

### Using the BlogFacade

The BlogFacade provides convenient access to blog functionality:

```php
use Modules\Blog\Facades\BlogFacade;

// Get all root categories
$categories = BlogFacade::getRootCategories('en');

// Get posts by category
$posts = BlogFacade::getPostsByCategoryId(1, 'en');

// Get a single post by slug
$post = BlogFacade::getPostBySlug('my-blog-post', 'en');

// Get featured posts
$featuredPosts = BlogFacade::getFeaturePosts('en');

// Get latest posts
$latestPosts = BlogFacade::getLatestPosts('en');

// Get top posts
$topPosts = BlogFacade::getTopPosts('en');

// Get category by slug
$category = BlogFacade::getCategoryBySlug('technology', 'en');
```

### Using the BlogService Directly

```php
use Modules\Blog\Services\BlogService;

$blogService = app(BlogService::class);

// Get posts with pagination
$posts = $blogService->getPostsByCategoryId(1, 'en');

// Get categories with hierarchy
$categories = $blogService->getRootCategories('en');
```

## Filtering

The Blog Module supports advanced filtering through the `BlogPostFilter` and `BlogCategoryFilter` classes:

### Available Filters

#### BlogPostFilter
- `search`: Search in title and content
- `status`: Filter by post status (draft, published, archived)
- `featured`: Filter featured posts only
- `author_id`: Filter by author
- `category_id`: Filter by category
- `published_from`: Filter posts published after date
- `published_to`: Filter posts published before date

#### BlogCategoryFilter
- `search`: Search in category name and description
- `status`: Filter by category status (active, inactive)
- `parent_id`: Filter by parent category

### Usage Example

```php
// Using filters in API requests
GET /api/v1/blog/posts?category_id=1&search=laravel&featured=1

// Using filters in controllers
$request = request();
$posts = BlogPost::filter(new BlogPostFilter($request))
    ->published()
    ->paginate(15);
```

## Multi-Language Support

The Blog Module provides comprehensive multi-language support through translation models:

### Supported Languages

The module supports any language configured in your Laravel ProCMS installation. Common configurations include:
- English (en)
- Vietnamese (vi)
- And any additional languages you configure

### Translation Management

#### Creating Translations

```php
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Models\BlogPostTranslation;

$post = BlogPost::create([
    'status' => 'published',
    'author_id' => 1,
    'published_at' => now(),
]);

// English translation
BlogPostTranslation::create([
    'post_id' => $post->id,
    'locale' => 'en',
    'title' => 'My Blog Post',
    'slug' => 'my-blog-post',
    'content' => '<p>This is the English content.</p>',
    'summary' => 'English summary',
    'meta_title' => 'SEO Title',
    'meta_description' => 'SEO Description',
]);

// Vietnamese translation
BlogPostTranslation::create([
    'post_id' => $post->id,
    'locale' => 'vi',
    'title' => 'Bài viết của tôi',
    'slug' => 'bai-viet-cua-toi',
    'content' => '<p>Đây là nội dung tiếng Việt.</p>',
    'summary' => 'Tóm tắt tiếng Việt',
    'meta_title' => 'Tiêu đề SEO',
    'meta_description' => 'Mô tả SEO',
]);
```

#### Retrieving Translated Content

```php
// Get posts for current locale
$locale = app()->getLocale();
$posts = BlogFacade::getPostsByCategoryId(1, $locale);

// Get posts for specific locale
$englishPosts = BlogFacade::getPostsByCategoryId(1, 'en');
$vietnamesePosts = BlogFacade::getPostsByCategoryId(1, 'vi');
```

### Locale-Specific Routing

The module automatically filters content based on the current application locale:

```php
// Set locale in middleware or controller
app()->setLocale('vi');

// All subsequent calls will return Vietnamese content
$posts = BlogFacade::getLatestPosts(); // Returns Vietnamese posts only
```

## Caching

The Blog Module includes intelligent caching to improve performance:

### Cache Configuration

Caching is controlled by the `cache_enabled` setting:

```php
// Check if caching is enabled
if (setting('cache_enabled', true)) {
    // Caching is active
}
```

### Cache Tags

The module uses cache tags for efficient cache management:

- `blog_posts`: All post-related cache
- `blog_categories`: All category-related cache
- `blog_translations`: Translation-specific cache

### Cache Keys

Cache keys are automatically generated based on:
- Content type (posts/categories)
- Locale
- Pagination parameters
- Filter parameters

Example cache keys:
- `blog_posts_en_15_1`: English posts, 15 per page, page 1
- `blog_categories_vi`: Vietnamese categories
- `blog_featured_posts_en`: English featured posts

### Manual Cache Management

```php
use Illuminate\Support\Facades\Cache;

// Clear all blog cache
Cache::tags(['blog_posts', 'blog_categories'])->flush();

// Clear specific cache
Cache::forget('blog_posts_en_15_1');
```

## Testing

The Blog Module includes comprehensive test coverage with both unit and feature tests.

### Test Structure

```
tests/
├── Feature/                     # Integration tests
│   ├── BlogControllerTest.php   # HTTP endpoint tests
│   └── BlogApiTest.php          # API integration tests
└── Unit/                        # Unit tests
    ├── BlogServiceTest.php      # Service layer tests
    ├── BlogFacadeTest.php       # Facade tests
    ├── BlogCategoryModelTest.php # Category model tests
    └── BlogPostModelTest.php    # Post model tests
```

### Running Tests

```bash
# Run all Blog module tests (103 tests)
php artisan test modules/Blog/tests/

# Run specific test suite
php artisan test modules/Blog/tests/Unit/      # 78 unit tests
php artisan test modules/Blog/tests/Feature/   # 25 feature tests

# Run specific test file
php artisan test modules/Blog/tests/Unit/BlogServiceTest.php
php artisan test modules/Blog/tests/Feature/BlogApiTest.php

# Run with verbose output
php artisan test modules/Blog/tests/ --verbose

# Run with coverage (if configured)
php artisan test modules/Blog/tests/ --coverage

# Expected output for successful run:
# Tests:    103 passed (187 assertions)
# Duration: ~160s
```

### Test Coverage

- **Overall Coverage**: 100% ✅
- **Unit Tests**: 78/78 PASS (100%) ✅
- **Feature Tests**: 25/25 PASS (100%) ✅
- **Total Tests**: 103/103 PASS (100%) ✅

#### Detailed Test Results

**Unit Test Suite:**
- **BlogCategoryModelTest**: 22/22 PASS ✅
- **BlogFacadeTest**: 15/15 PASS ✅
- **BlogPostModelTest**: 22/22 PASS ✅
- **BlogServiceTest**: 19/19 PASS ✅

**Feature Test Suite:**
- **BlogControllerTest**: 14/14 PASS ✅
- **BlogApiTest**: 11/11 PASS ✅

### Key Test Areas

- ✅ **Model Relationships**: All Eloquent relationships tested (100% coverage)
- ✅ **Service Methods**: All business logic methods covered (100% coverage)
- ✅ **API Endpoints**: Complete HTTP endpoint testing (100% coverage)
- ✅ **Validation Rules**: Form request validation testing (100% coverage)
- ✅ **Multi-language**: Locale-specific content testing (100% coverage)
- ✅ **Caching**: Cache behavior and invalidation testing (100% coverage)
- ✅ **Filtering**: Advanced search and filter testing (100% coverage)
- ✅ **Error Handling**: Exception and edge case testing (100% coverage)
- ✅ **Database Operations**: CRUD operations and constraints testing (100% coverage)
- ✅ **Authentication**: Access control and permissions testing (100% coverage)

### Test Quality Assurance

The Blog Module maintains the highest testing standards:

#### Test Architecture
- **Separation of Concerns**: Unit tests focus on individual components, Feature tests on integration
- **Database Transactions**: All tests use database transactions for isolation
- **Factory Usage**: Consistent use of model factories for test data generation
- **Mock Objects**: Proper mocking for external dependencies and services

#### Test Data Management
- **Realistic Data**: Test data closely mirrors production scenarios
- **Edge Cases**: Comprehensive testing of boundary conditions and error states
- **Multi-language Testing**: All features tested across multiple locales
- **Performance Testing**: Query optimization and N+1 problem prevention

#### Continuous Integration
- **Automated Testing**: All tests run automatically on code changes
- **Code Coverage**: 100% line and branch coverage maintained
- **Quality Gates**: No code merges without passing all tests
- **Performance Monitoring**: Test execution time tracking and optimization

## Dependencies

### Required Dependencies

The Blog Module depends on the following Laravel ProCMS modules:

- **Core Module**: Base functionality and traits
- **User Module**: Author management and authentication
- **Language Module**: Multi-language support infrastructure

### Laravel Dependencies

- Laravel Framework 10.x+
- Laravel Sanctum (for API authentication)
- Laravel Telescope (for debugging, optional)

### PHP Dependencies

- PHP 8.1+
- BCMath PHP Extension
- Ctype PHP Extension
- JSON PHP Extension
- Mbstring PHP Extension
- OpenSSL PHP Extension
- PDO PHP Extension
- Tokenizer PHP Extension
- XML PHP Extension

## Contributing

### Development Guidelines

1. **Follow PSR Standards**: Use PSR-4 autoloading and PSR-12 coding standards
2. **Write Tests**: All new features must include comprehensive tests
3. **Documentation**: Update README and inline documentation for changes
4. **Code Review**: All changes require code review before merging

### Submitting Changes

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes with tests
4. Run the test suite: `php artisan test Modules/Blog/tests/`
5. Submit a pull request with detailed description

### Coding Standards

```php
// Use proper type hints
public function getPostBySlug(string $slug, string $locale): ?BlogPost
{
    // Implementation
}

// Use descriptive variable names
$publishedPosts = $this->getPublishedPosts($locale);

// Add comprehensive docblocks
/**
 * Get posts by category with pagination.
 *
 * @param int $categoryId The category ID to filter by
 * @param string $locale The locale for translations
 * @param int $perPage Number of posts per page
 * @return LengthAwarePaginator
 */
public function getPostsByCategoryId(int $categoryId, string $locale, int $perPage = 15): LengthAwarePaginator
{
    // Implementation
}
```

### Performance Considerations

- Always use eager loading for relationships
- Implement caching for expensive queries
- Use database indexes for frequently queried columns
- Optimize N+1 query problems
- Consider pagination for large datasets

---

**Laravel ProCMS Blog Module** - Built with ❤️ for content creators and developers.
