<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('bot_shares', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bot_id')->constrained('bots')->onDelete('cascade');
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->morphs('shareable');
            $table->string('status')->default('active');
            $table->timestamps();

            $table->unique(['bot_id', 'user_id']);
        });

        Schema::create('bot_share_links', function (Blueprint $table) {
            $table->id();
            $table->uuid('token')->unique();
            $table->foreignId('bot_id')->constrained('bots')->onDelete('cascade');
            $table->morphs('shareable');
            $table->timestamp('expires_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */

    public function down(): void
    {
        Schema::dropIfExists('bot_share_links');
        Schema::dropIfExists('bot_shares');
    }
};
