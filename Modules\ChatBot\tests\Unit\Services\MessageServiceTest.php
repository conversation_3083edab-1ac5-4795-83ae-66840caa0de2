<?php

namespace Modules\ChatBot\Tests\Unit\Services;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Services\MessageService;
use Modules\ChatBot\Services\ConversationService;
use Modules\ChatBot\Services\AIService;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\Core\Exceptions\BusinessException;

class MessageServiceTest extends TestCase
{
    use RefreshDatabase;

    private MessageService $messageService;
    private $mockAIService;
    private $mockConversationService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('migrate', ['--database' => 'testing']);
        
        $this->mockAIService = Mockery::mock(AIService::class);
        $this->mockConversationService = Mockery::mock(ConversationService::class);
        
        $this->messageService = new MessageService(
            $this->mockConversationService,
            $this->mockAIService
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_create_user_message()
    {
        $conversation = Conversation::factory()->active()->create();

        $data = [
            'conversation_id' => $conversation->id,
            'content' => 'Hello, I need help',
            'content_type' => ContentType::TEXT,
        ];

        $message = $this->messageService->createUserMessage($data);

        $this->assertInstanceOf(Message::class, $message);
        $this->assertEquals($conversation->id, $message->conversation_id);
        $this->assertEquals(MessageRole::USER, $message->role);
        $this->assertEquals('Hello, I need help', $message->content);
        $this->assertEquals(MessageStatus::COMPLETED, $message->status);
    }

    /** @test */
    public function it_throws_exception_when_adding_message_to_non_editable_conversation()
    {
        $conversation = Conversation::factory()->completed()->create();

        $data = [
            'conversation_id' => $conversation->id,
            'content' => 'Hello, I need help',
        ];

        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('Cannot add messages to this conversation.');

        $this->messageService->createUserMessage($data);
    }

    /** @test */
    public function it_can_create_assistant_message()
    {
        $conversation = Conversation::factory()->create();

        $data = [
            'conversation_id' => $conversation->id,
            'content' => 'How can I help you?',
            'model_used' => 'gpt-4-turbo',
            'status' => MessageStatus::COMPLETED,
        ];

        $message = $this->messageService->createAssistantMessage($data);

        $this->assertInstanceOf(Message::class, $message);
        $this->assertEquals($conversation->id, $message->conversation_id);
        $this->assertEquals(MessageRole::ASSISTANT, $message->role);
        $this->assertEquals('How can I help you?', $message->content);
        $this->assertEquals('gpt-4-turbo', $message->model_used);
    }

    /** @test */
    public function it_can_generate_ai_response()
    {
        $bot = Bot::factory()->create(['status' => 'active']);
        $conversation = Conversation::factory()->create(['bot_id' => $bot->id]);

        // Mock AI service response
        $this->mockAIService
            ->shouldReceive('generateResponse')
            ->once()
            ->andReturn([
                'content' => 'AI generated response',
                'usage' => [
                    'prompt_tokens' => 50,
                    'completion_tokens' => 25,
                    'total_tokens' => 75,
                ],
                'cost' => 0.001,
                'response_time_ms' => 1500,
            ]);

        $message = $this->messageService->generateAIResponse($conversation);

        $this->assertInstanceOf(Message::class, $message);
        $this->assertEquals(MessageRole::ASSISTANT, $message->role);
        $this->assertEquals('AI generated response', $message->content);
        $this->assertEquals(MessageStatus::COMPLETED, $message->status);
        $this->assertEquals(50, $message->prompt_tokens);
        $this->assertEquals(25, $message->completion_tokens);
        $this->assertEquals(75, $message->total_tokens);
        $this->assertEquals(0.001, $message->cost);
    }

    /** @test */
    public function it_throws_exception_when_generating_response_for_inactive_bot()
    {
        $bot = Bot::factory()->create(['status' => 'draft']);
        $conversation = Conversation::factory()->create(['bot_id' => $bot->id]);

        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('Bot is not active.');

        $this->messageService->generateAIResponse($conversation);
    }

    /** @test */
    public function it_marks_message_as_failed_when_ai_generation_fails()
    {
        $bot = Bot::factory()->create(['status' => 'active']);
        $conversation = Conversation::factory()->create(['bot_id' => $bot->id]);

        // Mock AI service to throw exception
        $this->mockAIService
            ->shouldReceive('generateResponse')
            ->once()
            ->andThrow(new \Exception('API Error'));

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('API Error');

        $this->messageService->generateAIResponse($conversation);

        // Check that a failed message was created
        $failedMessage = Message::where('conversation_id', $conversation->id)
            ->where('status', MessageStatus::FAILED)
            ->first();

        $this->assertNotNull($failedMessage);
        $this->assertEquals('API Error', $failedMessage->error_message);
    }

    /** @test */
    public function it_can_get_conversation_messages()
    {
        $conversation = Conversation::factory()->create();
        
        Message::factory()->count(3)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::USER,
        ]);
        
        Message::factory()->count(2)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::ASSISTANT,
        ]);

        $messages = $this->messageService->getConversationMessages($conversation->id, [], 10);

        $this->assertCount(5, $messages->items());
    }

    /** @test */
    public function it_can_filter_messages_by_role()
    {
        $conversation = Conversation::factory()->create();
        
        Message::factory()->count(3)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::USER,
        ]);
        
        Message::factory()->count(2)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::ASSISTANT,
        ]);

        $userMessages = $this->messageService->getConversationMessages(
            $conversation->id,
            ['role' => MessageRole::USER],
            10
        );

        $this->assertCount(3, $userMessages->items());
        $this->assertTrue($userMessages->items()->every(fn($msg) => $msg->role === MessageRole::USER));
    }

    /** @test */
    public function it_can_update_message()
    {
        $message = Message::factory()->create(['content' => 'Original content']);

        $updatedMessage = $this->messageService->updateMessage($message, [
            'content' => 'Updated content',
            'quality_score' => 5,
        ]);

        $this->assertEquals('Updated content', $updatedMessage->content);
        $this->assertEquals(5, $updatedMessage->quality_score);
    }

    /** @test */
    public function it_can_delete_message()
    {
        $conversation = Conversation::factory()->create();
        $message = Message::factory()->create(['conversation_id' => $conversation->id]);

        $result = $this->messageService->deleteMessage($message);

        $this->assertTrue($result);
        $this->assertDatabaseMissing('messages', ['id' => $message->id]);
    }

    /** @test */
    public function it_can_retry_failed_message()
    {
        $message = Message::factory()->create([
            'status' => MessageStatus::FAILED,
            'role' => MessageRole::USER,
        ]);

        $retriedMessage = $this->messageService->retryMessage($message);

        $this->assertEquals(MessageStatus::PENDING, $retriedMessage->status);
        $this->assertNull($retriedMessage->error_message);
        $this->assertNull($retriedMessage->error_code);
        $this->assertNotNull($retriedMessage->started_at);
    }

    /** @test */
    public function it_throws_exception_when_retrying_non_failed_message()
    {
        $message = Message::factory()->create(['status' => MessageStatus::COMPLETED]);

        $this->expectException(BusinessException::class);
        $this->expectExceptionMessage('Only failed messages can be retried.');

        $this->messageService->retryMessage($message);
    }

    /** @test */
    public function it_can_get_message_statistics()
    {
        $conversation = Conversation::factory()->create();
        
        // Create user messages
        Message::factory()->count(3)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::USER,
        ]);
        
        // Create assistant messages with tokens and cost
        Message::factory()->count(2)->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::COMPLETED,
            'total_tokens' => 100,
            'cost' => 0.01,
            'response_time_ms' => 1000,
        ]);

        $stats = $this->messageService->getMessageStats(['conversation_id' => $conversation->id]);

        $this->assertEquals(5, $stats['total_messages']);
        $this->assertEquals(3, $stats['user_messages']);
        $this->assertEquals(2, $stats['assistant_messages']);
        $this->assertEquals(200, $stats['total_tokens']);
        $this->assertEquals(0.02, $stats['total_cost']);
        $this->assertEquals(1000, $stats['average_response_time']);
        $this->assertEquals(100.0, $stats['success_rate']); // All assistant messages are completed
    }

    /** @test */
    public function it_can_build_conversation_context()
    {
        $bot = Bot::factory()->create(['system_prompt' => 'You are a helpful assistant.']);
        $conversation = Conversation::factory()->create(['bot_id' => $bot->id]);
        
        Message::factory()->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::USER,
            'content' => 'Hello',
            'status' => MessageStatus::COMPLETED,
        ]);
        
        Message::factory()->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::ASSISTANT,
            'content' => 'Hi there!',
            'status' => MessageStatus::COMPLETED,
        ]);

        $context = $this->messageService->buildConversationContext($conversation);

        $this->assertCount(3, $context); // System + User + Assistant
        $this->assertEquals('system', $context[0]['role']);
        $this->assertEquals('You are a helpful assistant.', $context[0]['content']);
        $this->assertEquals('user', $context[1]['role']);
        $this->assertEquals('Hello', $context[1]['content']);
        $this->assertEquals('assistant', $context[2]['role']);
        $this->assertEquals('Hi there!', $context[2]['content']);
    }
}
