<?php

return [
    'name' => 'ChatBot',

    /*
    |--------------------------------------------------------------------------
    | Facades Configuration
    |--------------------------------------------------------------------------
    */
    'facades' => [
        'Message' => \Modules\ChatBot\Facades\MessageFacade::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Async Processing Configuration
    |--------------------------------------------------------------------------
    */
    'async' => [
        'enabled' => env('CHATBOT_ASYNC_ENABLED', true),
        'default_queue' => env('CHATBOT_DEFAULT_QUEUE', 'ai-processing'),

        'timeout' => env('CHATBOT_PROCESSING_TIMEOUT', 300), // 5 minutes
        'retry_attempts' => env('CHATBOT_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Webhook Configuration
    |--------------------------------------------------------------------------
    */
    'webhook' => [
        'url' => env('CHATBOT_WEBHOOK_URL'),
        'secret' => env('CHATBOT_WEBHOOK_SECRET'),
        'timeout' => env('CHATBOT_WEBHOOK_TIMEOUT', 10),
        'retry_attempts' => env('CHATBOT_WEBHOOK_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Python Service Configuration
    |--------------------------------------------------------------------------
    */
    'python' => [
        'embedding_url' => env('CHATBOT_PYTHON_EMBEDDING_URL'),
        'timeout' => env('CHATBOT_PYTHON_TIMEOUT', 30),
        'retry_attempts' => env('CHATBOT_PYTHON_RETRY_ATTEMPTS', 2),
    ],

    /*
    |--------------------------------------------------------------------------
    | RAG Processing Configuration
    |--------------------------------------------------------------------------
    */
    'rag' => [
        'queue_name' => env('CHATBOT_RAG_QUEUE_NAME', 'rag-processing'),
        'timeout' => env('CHATBOT_RAG_TIMEOUT', 300),
        'retry_attempts' => env('CHATBOT_RAG_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | LLM Processing Configuration
    |--------------------------------------------------------------------------
    */
    'llm' => [
        'queue_name' => env('CHATBOT_LLM_QUEUE_NAME', 'llm-processing'),
        'endpoint' => env('CHATBOT_LLM_ENDPOINT', 'https://api.openai.com/v1/chat/completions'),
        'api_key' => env('CHATBOT_LLM_API_KEY'),
        'model' => env('CHATBOT_LLM_MODEL', 'gpt-3.5-turbo'),
        'max_tokens' => env('CHATBOT_LLM_MAX_TOKENS', 1000),
        'temperature' => env('CHATBOT_LLM_TEMPERATURE', 0.7),
        'timeout' => env('CHATBOT_LLM_TIMEOUT', 120),
        'retry_attempts' => env('CHATBOT_LLM_RETRY_ATTEMPTS', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Embedding Configuration
    |--------------------------------------------------------------------------
    */
    'embedding' => [
        'enabled' => env('CHATBOT_EMBEDDING_ENABLED', false),
        'default_model' => env('CHATBOT_EMBEDDING_MODEL', 'text-embedding-ada-002'),
        'max_context_tokens' => env('CHATBOT_EMBEDDING_MAX_TOKENS', 4000),
        'similarity_threshold' => env('CHATBOT_EMBEDDING_SIMILARITY_THRESHOLD', 0.7),
    ],

    /*
    |--------------------------------------------------------------------------
    | Real-time Broadcasting Configuration
    |--------------------------------------------------------------------------
    */
    'broadcasting' => [
        'enabled' => env('CHATBOT_BROADCASTING_ENABLED', true),
        'redis_prefix' => env('CHATBOT_REDIS_PREFIX', 'chatbot:broadcast:'),
        'connection_timeout' => env('CHATBOT_CONNECTION_TIMEOUT', 3600), // 1 hour
        'chunk_storage_ttl' => env('CHATBOT_CHUNK_STORAGE_TTL', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    */
    'rate_limits' => [
        'message_creation' => env('CHATBOT_MESSAGE_RATE_LIMIT', '60,1'), // 60 per minute
        'ai_generation' => env('CHATBOT_AI_RATE_LIMIT', '20,1'), // 20 per minute

    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Configuration
    |--------------------------------------------------------------------------
    */
    'performance' => [
        'max_context_messages' => env('CHATBOT_MAX_CONTEXT_MESSAGES', 50),

        'cleanup_interval' => env('CHATBOT_CLEANUP_INTERVAL', 3600), // 1 hour
    ],
];
