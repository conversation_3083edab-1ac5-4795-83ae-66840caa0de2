<?php

namespace Modules\ChatBot\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\ChatBot\Models\Message>
 */
class MessageFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Message::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $role = $this->faker->randomElement(MessageRole::cases());
        $status = $this->faker->randomElement(MessageStatus::cases());
        $conversation = Conversation::factory()->create();

        return [
            'uuid' => Str::uuid(),
            'conversation_id' => $conversation->id,
            'role' => $role,
            'content' => $this->generateContentForRole($role),
            'content_type' => ContentType::TEXT,
            'tool_calls' => null,
            'tool_call_id' => null,
            'attachments' => null,
            'model_used' => $role === MessageRole::ASSISTANT ? $this->faker->randomElement(['gpt-4-turbo', 'claude-3-sonnet', 'gemini-pro']) : null,
            'status' => $status,
            'prompt_tokens' => $role === MessageRole::ASSISTANT ? $this->faker->numberBetween(50, 500) : null,
            'completion_tokens' => $role === MessageRole::ASSISTANT ? $this->faker->numberBetween(20, 200) : null,
            'total_tokens' => null, // Will be calculated
            'cost' => $role === MessageRole::ASSISTANT ? $this->faker->randomFloat(6, 0.001, 0.1) : null,
            'response_time_ms' => $role === MessageRole::ASSISTANT ? $this->faker->numberBetween(500, 5000) : null,
            'started_at' => $role === MessageRole::ASSISTANT ? $this->faker->dateTimeBetween('-1 hour', 'now') : null,
            'completed_at' => $status === MessageStatus::COMPLETED ? $this->faker->dateTimeBetween('-1 hour', 'now') : null,

            'error_message' => $status === MessageStatus::FAILED ? $this->faker->sentence() : null,
            'error_code' => $status === MessageStatus::FAILED ? $this->faker->randomElement(['API_ERROR', 'TIMEOUT', 'RATE_LIMIT']) : null,
            'quality_score' => $this->faker->optional(0.3)->numberBetween(1, 5),
            'is_helpful' => $this->faker->optional(0.3)->boolean(),
            'metadata' => null,
        ];
    }

    /**
     * Configure the model factory.
     */
    public function configure(): static
    {
        return $this->afterMaking(function (Message $message) {
            // Calculate total tokens
            if ($message->prompt_tokens && $message->completion_tokens) {
                $message->total_tokens = $message->prompt_tokens + $message->completion_tokens;
            }
        });
    }

    /**
     * Generate content based on message role.
     */
    private function generateContentForRole(MessageRole $role): string
    {
        return match ($role) {
            MessageRole::USER => $this->generateUserContent(),
            MessageRole::ASSISTANT => $this->generateAssistantContent(),
            MessageRole::SYSTEM => $this->generateSystemContent(),
            MessageRole::TOOL => $this->generateToolContent(),
        };
    }

    /**
     * Generate user message content.
     */
    private function generateUserContent(): string
    {
        $userMessages = [
            'Xin chào, tôi cần hỗ trợ về sản phẩm của bạn.',
            'Làm thế nào để tôi có thể reset mật khẩu?',
            'Tôi gặp lỗi khi đăng nhập vào hệ thống.',
            'Bạn có thể giải thích tính năng này không?',
            'Tôi muốn hủy đơn hàng của mình.',
            'Khi nào sản phẩm mới sẽ ra mắt?',
            'Tôi cần hướng dẫn sử dụng chi tiết.',
            'Có thể tùy chỉnh giao diện không?',
            'Làm sao để xuất dữ liệu ra file Excel?',
            'Tôi muốn nâng cấp gói dịch vụ.',
        ];

        return $this->faker->randomElement($userMessages);
    }

    /**
     * Generate assistant message content.
     */
    private function generateAssistantContent(): string
    {
        $assistantMessages = [
            'Xin chào! Tôi rất vui được hỗ trợ bạn. Bạn có thể mô tả chi tiết vấn đề bạn đang gặp phải không?',
            'Để reset mật khẩu, bạn có thể làm theo các bước sau: 1) Truy cập trang đăng nhập, 2) Nhấp vào "Quên mật khẩu", 3) Nhập email của bạn.',
            'Tôi hiểu bạn đang gặp khó khăn với việc đăng nhập. Hãy thử xóa cache trình duyệt và thử lại.',
            'Tính năng này cho phép bạn tự động hóa quy trình làm việc. Bạn có muốn tôi hướng dẫn chi tiết không?',
            'Tôi sẽ giúp bạn hủy đơn hàng. Vui lòng cung cấp mã đơn hàng để tôi có thể xử lý.',
            'Sản phẩm mới dự kiến sẽ ra mắt vào quý tới. Tôi sẽ thông báo cho bạn khi có thông tin cụ thể.',
            'Tôi sẽ gửi cho bạn hướng dẫn sử dụng chi tiết qua email. Bạn có thể kiểm tra hộp thư trong vài phút.',
            'Có, bạn hoàn toàn có thể tùy chỉnh giao diện theo ý muốn. Tôi sẽ hướng dẫn bạn cách làm.',
            'Để xuất dữ liệu ra Excel, bạn vào menu "Báo cáo" > "Xuất dữ liệu" > chọn định dạng Excel.',
            'Tuyệt vời! Tôi sẽ giúp bạn nâng cấp gói dịch vụ. Bạn muốn nâng cấp lên gói nào?',
        ];

        return $this->faker->randomElement($assistantMessages);
    }

    /**
     * Generate system message content.
     */
    private function generateSystemContent(): string
    {
        return 'Bạn là một trợ lý AI hữu ích, thân thiện và chuyên nghiệp. Hãy trả lời các câu hỏi một cách chính xác và hữu ích.';
    }

    /**
     * Generate tool message content.
     */
    private function generateToolContent(): string
    {
        $toolResults = [
            '{"status": "success", "data": {"temperature": 25, "humidity": 60}}',
            '{"status": "success", "results": [{"title": "Kết quả tìm kiếm 1"}, {"title": "Kết quả tìm kiếm 2"}]}',
            '{"status": "error", "message": "API không khả dụng"}',
            '{"status": "success", "image_url": "https://example.com/generated-image.jpg"}',
        ];

        return $this->faker->randomElement($toolResults);
    }

    /**
     * Indicate that the message is from a user.
     */
    public function fromUser(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => MessageRole::USER,
            'content' => $this->generateUserContent(),
            'model_used' => null,
            'prompt_tokens' => null,
            'completion_tokens' => null,
            'total_tokens' => null,
            'cost' => null,
            'response_time_ms' => null,
            'started_at' => null,
        ]);
    }

    /**
     * Indicate that the message is from an assistant.
     */
    public function fromAssistant(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => MessageRole::ASSISTANT,
            'content' => $this->generateAssistantContent(),
            'model_used' => $this->faker->randomElement(['gpt-4-turbo', 'claude-3-sonnet', 'gemini-pro']),
            'prompt_tokens' => $this->faker->numberBetween(50, 500),
            'completion_tokens' => $this->faker->numberBetween(20, 200),
            'cost' => $this->faker->randomFloat(6, 0.001, 0.1),
            'response_time_ms' => $this->faker->numberBetween(500, 5000),
            'started_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    /**
     * Indicate that the message is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => MessageStatus::COMPLETED,
            'completed_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'error_message' => null,
            'error_code' => null,
        ]);
    }

    /**
     * Indicate that the message has failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => MessageStatus::FAILED,
            'error_message' => $this->faker->sentence(),
            'error_code' => $this->faker->randomElement(['API_ERROR', 'TIMEOUT', 'RATE_LIMIT']),
            'completed_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    /**
     * Indicate that the message has attachments.
     */
    public function withAttachments(): static
    {
        return $this->state(fn (array $attributes) => [
            'content_type' => ContentType::MIXED,
            'attachments' => [
                [
                    'type' => 'image',
                    'url' => $this->faker->imageUrl(),
                    'name' => 'screenshot.png',
                    'size' => $this->faker->numberBetween(100000, 5000000),
                    'mime_type' => 'image/png',
                ],
            ],
        ]);
    }

    /**
     * Indicate that the message has tool calls.
     */
    public function withToolCalls(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => MessageRole::ASSISTANT,
            'tool_calls' => [
                [
                    'id' => 'call_' . $this->faker->uuid(),
                    'type' => 'function',
                    'function' => [
                        'name' => 'web_search',
                        'arguments' => json_encode(['query' => $this->faker->sentence()]),
                    ],
                ],
            ],
        ]);
    }

    /**
     * Indicate that the message belongs to a specific conversation.
     */
    public function forConversation(Conversation $conversation): static
    {
        return $this->state(fn (array $attributes) => [
            'conversation_id' => $conversation->id,
        ]);
    }
}
