<?php

namespace Modules\Blog\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Enums\BlogStatus;
use Modules\User\Models\User;

class BlogPostFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = BlogPost::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'layout' => null,
            'author_id' => User::factory(),
            'status' => $this->faker->randomElement(BlogStatus::cases()),
            'published_at' => $this->faker->optional(0.7)->dateTimeBetween('-1 year', 'now'),
        ];
    }

    /**
     * Indicate that the post is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BlogStatus::Published,
            'published_at' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ]);
    }

    /**
     * Indicate that the post is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BlogStatus::Draft,
            'published_at' => null,
        ]);
    }

    /**
     * Indicate that the post is archived.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BlogStatus::Archived,
            'published_at' => $this->faker->dateTimeBetween('-2 years', '-6 months'),
        ]);
    }

    /**
     * Indicate that the post has a layout.
     */
    public function withLayout(): static
    {
        return $this->state(fn (array $attributes) => [
            'layout' => $this->faker->randomElement(['default', 'full-width', 'sidebar', 'minimal']),
        ]);
    }

    /**
     * Indicate that the post is scheduled for future publishing.
     */
    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => BlogStatus::Published,
            'published_at' => $this->faker->dateTimeBetween('now', '+3 months'),
        ]);
    }
}
