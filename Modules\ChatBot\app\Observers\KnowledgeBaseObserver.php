<?php

namespace Modules\ChatBot\Observers;

use Illuminate\Support\Facades\Cache;
use Modules\ChatBot\Models\KnowledgeBase;

class KnowledgeBaseObserver
{
    /**
     * Cache tags for knowledge bases
     */
    private const CACHE_TAGS = ['knowledge-bases', 'bots', 'conversations'];

    /**
     * Handle the KnowledgeBase "created" event.
     */
    public function created(KnowledgeBase $knowledgeBase): void
    {
        $this->clearCache($knowledgeBase);
    }

    /**
     * Handle the KnowledgeBase "updated" event.
     */
    public function updated(KnowledgeBase $knowledgeBase): void
    {
        $this->clearCache($knowledgeBase);
    }

    /**
     * Handle the KnowledgeBase "deleted" event.
     */
    public function deleted(KnowledgeBase $knowledgeBase): void
    {
        $this->clearCache($knowledgeBase);
    }

    /**
     * Handle the KnowledgeBase "restored" event.
     */
    public function restored(KnowledgeBase $knowledgeBase): void
    {
        $this->clearCache($knowledgeBase);
    }

    /**
     * Handle the KnowledgeBase "force deleted" event.
     */
    public function forceDeleted(KnowledgeBase $knowledgeBase): void
    {
        $this->clearCache($knowledgeBase);
    }

    /**
     * Clear knowledge base related cache.
     */
    private function clearCache(KnowledgeBase $knowledgeBase): void
    {
        try {
            if (function_exists('enabledCache') && enabledCache()) {
                // Clear tagged cache
                Cache::tags(self::CACHE_TAGS)->flush();
                
                // Clear specific cache keys related to knowledge bases
                $cacheKeys = [
                    'knowledge_bases.all',
                    'knowledge_bases.ready',
                    'knowledge_bases.pending',
                    'knowledge_bases.processing',
                    'knowledge_bases.error',
                    "knowledge_bases.owner.{$knowledgeBase->owner_type}.{$knowledgeBase->owner_id}",
                    "knowledge_bases.type.{$knowledgeBase->type}",
                    "knowledge_bases.status.{$knowledgeBase->status}",
                    "knowledge_base.{$knowledgeBase->id}.stats",
                    "knowledge_base.{$knowledgeBase->uuid}.details",
                ];

                foreach ($cacheKeys as $key) {
                    Cache::forget($key);
                }

                // Clear paginated cache for different parameters
                for ($page = 1; $page <= 10; $page++) {
                    for ($limit = 10; $limit <= 50; $limit += 10) {
                        Cache::forget("knowledge_bases.paginated.{$page}.{$limit}");
                        Cache::forget("knowledge_bases.owner.{$knowledgeBase->owner_type}.{$knowledgeBase->owner_id}.paginated.{$page}.{$limit}");
                        Cache::forget("knowledge_bases.type.{$knowledgeBase->type}.paginated.{$page}.{$limit}");
                        Cache::forget("knowledge_bases.status.{$knowledgeBase->status}.paginated.{$page}.{$limit}");
                    }
                }

                // Clear bot-related cache that might be affected
                $this->clearBotRelatedCache($knowledgeBase);

                // Clear conversation-related cache that might be affected
                $this->clearConversationRelatedCache($knowledgeBase);
            }
        } catch (\Exception $e) {
            // Silently handle cache errors
            \Log::warning('Failed to clear KnowledgeBase cache', [
                'knowledge_base_id' => $knowledgeBase->id,
                'knowledge_base_uuid' => $knowledgeBase->uuid,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Clear bot-related cache that might be affected by knowledge base changes.
     */
    private function clearBotRelatedCache(KnowledgeBase $knowledgeBase): void
    {
        // Get all bots that use this knowledge base
        $botIds = $knowledgeBase->bots()->pluck('bots.id')->toArray();

        foreach ($botIds as $botId) {
            $botCacheKeys = [
                "bot.{$botId}.knowledge_bases",
                "bot.{$botId}.ready_knowledge_bases",
                "bot.{$botId}.knowledge_base_stats",
                "bot.{$botId}.stats",
            ];

            foreach ($botCacheKeys as $key) {
                Cache::forget($key);
            }
        }

        // Clear general bot cache that might include knowledge base data
        Cache::forget('bots.with_knowledge_bases');
        Cache::forget('bots.knowledge_base_counts');
    }

    /**
     * Clear conversation-related cache that might be affected by knowledge base changes.
     */
    private function clearConversationRelatedCache(KnowledgeBase $knowledgeBase): void
    {
        // Get all conversations that use this knowledge base
        $conversationIds = $knowledgeBase->conversations()->pluck('conversations.id')->toArray();

        foreach ($conversationIds as $conversationId) {
            $conversationCacheKeys = [
                "conversation.{$conversationId}.knowledge_bases",
                "conversation.{$conversationId}.ready_knowledge_bases",
                "conversation.{$conversationId}.knowledge_base_stats",
                "conversation.{$conversationId}.stats",
            ];

            foreach ($conversationCacheKeys as $key) {
                Cache::forget($key);
            }
        }

        // Clear general conversation cache that might include knowledge base data
        Cache::forget('conversations.with_knowledge_bases');
        Cache::forget('conversations.knowledge_base_counts');
    }
}
