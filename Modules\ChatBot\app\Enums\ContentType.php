<?php

namespace Modules\ChatBot\Enums;

enum ContentType: string
{
    case TEXT = 'text';
    case IMAGE = 'image';
    case FILE = 'file';
    case AUDIO = 'audio';
    case VIDEO = 'video';
    case MIXED = 'mixed';

    /**
     * Get all enum values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get the label for the content type.
     */
    public function label(): string
    {
        return match ($this) {
            self::TEXT => 'Văn bản',
            self::IMAGE => 'Hình ảnh',
            self::FILE => 'Tệp tin',
            self::AUDIO => 'Âm thanh',
            self::VIDEO => 'Video',
            self::MIXED => 'Hỗn hợp',
        };
    }

    /**
     * Get the icon for the content type.
     */
    public function icon(): string
    {
        return match ($this) {
            self::TEXT => 'fas fa-font',
            self::IMAGE => 'fas fa-image',
            self::FILE => 'fas fa-file',
            self::AUDIO => 'fas fa-volume-up',
            self::VIDEO => 'fas fa-video',
            self::MIXED => 'fas fa-layer-group',
        };
    }

    /**
     * Get the MIME types for the content type.
     */
    public function mimeTypes(): array
    {
        return match ($this) {
            self::TEXT => ['text/plain', 'text/markdown', 'text/html'],
            self::IMAGE => ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
            self::FILE => ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
            self::AUDIO => ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4'],
            self::VIDEO => ['video/mp4', 'video/avi', 'video/mov', 'video/webm'],
            self::MIXED => [],
        };
    }

    /**
     * Check if the content type supports attachments.
     */
    public function supportsAttachments(): bool
    {
        return $this !== self::TEXT;
    }

    /**
     * Check if the content type is media.
     */
    public function isMedia(): bool
    {
        return in_array($this, [self::IMAGE, self::AUDIO, self::VIDEO]);
    }

    /**
     * Check if the content type requires special processing.
     */
    public function requiresProcessing(): bool
    {
        return in_array($this, [self::IMAGE, self::FILE, self::AUDIO, self::VIDEO]);
    }

    /**
     * Get the maximum file size for the content type (in bytes).
     */
    public function maxFileSize(): int
    {
        return match ($this) {
            self::TEXT => 1024 * 1024, // 1MB
            self::IMAGE => 10 * 1024 * 1024, // 10MB
            self::FILE => 50 * 1024 * 1024, // 50MB
            self::AUDIO => 100 * 1024 * 1024, // 100MB
            self::VIDEO => 500 * 1024 * 1024, // 500MB
            self::MIXED => 100 * 1024 * 1024, // 100MB
        };
    }

    /**
     * Get content types that support vision models.
     */
    public static function visionSupportedTypes(): array
    {
        return [self::IMAGE, self::MIXED];
    }

    /**
     * Get content types that are text-based.
     */
    public static function textBasedTypes(): array
    {
        return [self::TEXT];
    }
}
