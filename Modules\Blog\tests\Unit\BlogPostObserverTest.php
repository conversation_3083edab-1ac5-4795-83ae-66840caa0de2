<?php

namespace Modules\Blog\Tests\Unit;

use Tests\TestCase;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Enums\BlogStatus;
use Modules\User\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use PHPUnit\Framework\Attributes\Test;

class BlogPostObserverTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    #[Test]
    public function it_clears_cache_when_post_created()
    {
        Cache::shouldReceive('tags')
            ->with(['blog'])
            ->andReturnSelf()
            ->atLeast(1);

        Cache::shouldReceive('flush')
            ->atLeast(1);

        BlogPost::factory()->create(['author_id' => $this->user->id]);

        // Just verify the post was created
        $this->assertTrue(true);
    }

    #[Test]
    public function it_clears_cache_when_post_updated()
    {
        $post = BlogPost::factory()->create(['author_id' => $this->user->id]);

        Cache::shouldReceive('tags')
            ->with(['blog'])
            ->andReturnSelf()
            ->atLeast(1);

        Cache::shouldReceive('flush')
            ->atLeast(1);

        $post->update(['featured' => true]);

        // Just verify the update worked
        $this->assertTrue($post->featured);
    }

    #[Test]
    public function it_clears_cache_when_post_deleted()
    {
        $post = BlogPost::factory()->create(['author_id' => $this->user->id]);

        Cache::shouldReceive('tags')
            ->with(['blog'])
            ->andReturnSelf()
            ->atLeast(1);

        Cache::shouldReceive('flush')
            ->atLeast(1);

        $post->delete();

        // Just verify the delete worked
        $this->assertTrue($post->trashed());
    }

    #[Test]
    public function it_handles_cache_errors_gracefully()
    {
        Cache::shouldReceive('tags')
            ->with(['blog'])
            ->andThrow(new \Exception('Cache error'));

        // Should not throw exception
        $post = BlogPost::factory()->create(['author_id' => $this->user->id]);

        $this->assertInstanceOf(BlogPost::class, $post);
    }
}
