<?php

namespace Modules\ChatBot\Http\Controllers\Auth;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\ChatBot\Http\Requests\MessageRequest;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Facades\MessageFacade;
use Modules\Core\Traits\ResponseTrait;


class MessageController extends Controller
{
    use ResponseTrait;

    /**
     * Display a listing of conversation messages.
     */
    public function index(Request $request, string $conversationUuid): JsonResponse
    {
        $messages = MessageFacade::getConversationMessages($conversationUuid);

        $transformedMessages = collect($messages->items())
            ->reverse()
            ->values()
            ->map(function ($message) {
                return $this->transformMessage($message);
            });

        return $this->successResponse($transformedMessages, 'Messages retrieved successfully.');
    }

    /**
     * Send message and get AI response.
     */
    public function sendAndRespond(MessageRequest $request, string $conversationUuid): JsonResponse
    {
        try {
            $user = auth()->user();
            $data = $request->validated();

            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            // Check permissions
            if (!$this->canWriteToConversation($conversation, $user) ||
                !$this->canUseBot($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            // Add conversation_id to data
            $data['conversation_id'] = $conversation->id;

            // Create user message
            $userMessage = MessageFacade::createUserMessage($data);

            // Generate AI response (no user options for security)
            MessageFacade::generateAIResponse($conversation, $userMessage);

            return $this->successResponse($userMessage,
                'Message sent and response generated successfully',
                201
            );

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Send query with advanced RAG options.
     */
    public function sendQuery(Request $request, string $conversationUuid): JsonResponse
    {
        try {
            // Validate query request
            $request->validate([
                'question' => 'required|string|max:2000',
                'file_ids' => 'nullable|array|max:20',
                'file_ids.*' => 'integer|exists:knowledge_bases,id',
                'top_k' => 'nullable|integer|min:1|max:20',
                'collection' => 'nullable|string|max:100',
            ]);

            $user = auth()->user();
            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            // Check permissions
            if (!$this->canWriteToConversation($conversation, $user) ||
                !$this->canUseBot($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            // Create user message for the question
            $messageData = [
                'conversation_id' => $conversation->id,
                'content' => $request->input('question'),
                'role' => 'user',
                'content_type' => 'text',
            ];
            $userMessage = MessageFacade::createUserMessage($messageData);

            // Create query record
            $query = Query::createFromUserInput(
                $request->input('question'),
                $user->id,
                get_class($user),
                $conversation->bot_id,
                $conversation->id,
                $userMessage->id,
                $request->input('file_ids'),
                $request->input('top_k', 5),
                $request->input('collection', 'documents')
            );

            // Dispatch query processing job
            QueryProcessingJob::dispatch($query);

            return $this->successResponse([
                'message' => $this->transformMessage($userMessage),
                'query' => [
                    'uuid' => $query->uuid,
                    'status' => $query->status,
                    'top_k' => $query->top_k,
                    'file_ids' => $query->file_ids,
                ],
            ], 'Query sent for processing', 201);

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get failed messages.
     */
    public function failedMessages(Request $request, string $conversationUuid): JsonResponse
    {
        $request->validate([
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $user = auth()->user();
            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            if (!$this->canAccessConversation($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $perPage = $request->get('per_page', 50);
            $messages = MessageFacade::getConversationMessages(
                $conversationUuid,
                ['status' => 'failed'],
                $perPage
            );

            return $this->paginatedResponse(
                $messages,
                'Failed messages retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Bulk retry failed messages.
     */
    public function bulkRetry(Request $request, string $conversationUuid): JsonResponse
    {
        $request->validate([
            'message_ids' => 'nullable|array',
            'message_ids.*' => 'string|exists:messages,uuid',
        ]);

        try {
            $user = auth()->user();
            $conversation = Conversation::where('uuid', $conversationUuid)->firstOrFail();

            if (!$this->canWriteToConversation($conversation, $user)) {
                return $this->errorResponse('Access denied', 403);
            }

            $messageIds = $request->get('message_ids');
            $results = [];

            if ($messageIds) {
                // Retry specific messages
                foreach ($messageIds as $messageUuid) {
                    $message = Message::where('uuid', $messageUuid)
                                    ->where('conversation_id', $conversation->id)
                                    ->first();

                    if ($message && $this->canEditMessage($message, $user)) {
                        try {
                            $retriedMessage = MessageFacade::retryMessage($message);
                            $results[] = [
                                'message_id' => $messageUuid,
                                'success' => true,
                                'message' => $retriedMessage,
                            ];
                        } catch (\Exception $e) {
                            $results[] = [
                                'message_id' => $messageUuid,
                                'success' => false,
                                'error' => $this->sanitizeErrorMessage($e),
                            ];
                        }
                    }
                }
            } else {
                // Retry all failed messages in conversation
                $failedMessages = Message::where('conversation_id', $conversation->id)
                                        ->where('status', MessageStatus::FAILED)
                                        ->get();

                foreach ($failedMessages as $message) {
                    try {
                        $retriedMessage = MessageFacade::retryMessage($message);
                        $results[] = [
                            'message_id' => $message->uuid,
                            'success' => true,
                            'message' => $retriedMessage,
                        ];
                    } catch (\Exception $e) {
                        $results[] = [
                            'message_id' => $message->uuid,
                            'success' => false,
                            'error' => $this->sanitizeErrorMessage($e),
                        ];
                    }
                }
            }

            return $this->successResponse(
                $results,
                'Bulk retry completed'
            );

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get message statistics.
     */
    public function stats(): JsonResponse
    {
        try {
            $user = auth()->user();

            $stats = [
                'total_messages' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->count(),

                'messages_by_role' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })
                ->selectRaw('role, COUNT(*) as count')
                ->groupBy('role')
                ->pluck('count', 'role')
                ->toArray(),

                'messages_by_status' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray(),

                'total_tokens' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->sum('total_tokens'),

                'total_cost' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })->sum('cost'),

                'average_response_time' => Message::whereHas('conversation', function ($q) use ($user) {
                    $q->where('user_id', $user->id);
                })
                ->where('role', MessageRole::ASSISTANT)
                ->whereNotNull('response_time_ms')
                ->avg('response_time_ms'),
            ];

            return $this->successResponse(
                $stats,
                'Message statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->safeErrorResponse($e, 500);
        }
    }

    /**
     * Get pagination metadata.
     */
    private function getPaginationMeta(LengthAwarePaginator $paginator): array
    {
        return [
            'current_page' => $paginator->currentPage(),
            'per_page' => $paginator->perPage(),
            'total' => $paginator->total(),
            'last_page' => $paginator->lastPage(),
            'from' => $paginator->firstItem(),
            'to' => $paginator->lastItem(),
        ];
    }





    /**
     * Transform message for API response (hide sensitive data).
     */
    private function transformMessage($message): array
    {
        return [
            'uuid' => $message->uuid,
            'role' => $message->role,
            'content' => $message->content,
            'content_type' => $message->content_type,
            'attachments' => $message->attachments,
            /*'tool_calls' => $message->tool_calls,
            'tool_call_id' => $message->tool_call_id,
            'model_used' => $message->model_used,
            'status' => $message->status,
            'started_at' => $message->started_at,
            'completed_at' => $message->completed_at,
            'quality_score' => $message->quality_score,
            'is_helpful' => $message->is_helpful,
            'metadata' => $message->metadata,
            'created_at' => $message->created_at,
            'updated_at' => $message->updated_at,*/
        ];
    }

    /**
     * Check if user can write to conversation.
     */
    private function canWriteToConversation($conversation, $user): bool
    {
        // Check if user owns the conversation
        if ($conversation->owner_id === $user->id && $conversation->owner_type === get_class($user)) {
            return true;
        }

        // Check if user has write access to the bot
        return $conversation->bot->userCan($user->id, 'write');
    }

    /**
     * Check if user can use the bot.
     */
    private function canUseBot($conversation, $user): bool
    {
        // Check if user can access the bot
        return $conversation->bot->canBeAccessedBy($user->id);
    }
}
