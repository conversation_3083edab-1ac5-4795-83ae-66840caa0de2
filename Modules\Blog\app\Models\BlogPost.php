<?php

namespace Modules\Blog\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Modules\Blog\Enums\BlogStatus;
use Modules\Blog\Database\Factories\BlogPostFactory;
use Modules\Blog\Http\Filters\BlogPostFilter;
use Modules\Blog\Http\Filters\PublicBlogPostFilter;
use Modules\User\Models\User;

class BlogPost extends Model
{
    use HasFactory, SoftDeletes, Translatable;

    /**
     * The table associated with the model.
     */
    protected $table = 'blog_posts';

    /**
     * The attributes that are translatable.
     *
     * @var array<string>
     */
    public array $translatedAttributes = [
        'title',
        'slug',
        'summary',
        'content',
        'image',
        'meta_title',
        'meta_description'
    ];

    /**
     * The foreign key for translations.
     */
    protected $translationForeignKey = 'post_id';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'featured',
        'layout',
        'author_id',
        'status',
        'published_at',
        'views',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'featured' => 'boolean',
        'author_id' => 'integer',
        'published_at' => 'datetime',
        'status' => BlogStatus::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<string>
     */
    protected $hidden = [
        'deleted_at'
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): BlogPostFactory
    {
        return BlogPostFactory::new();
    }

    /**
     * Get the author of the blog post.
     */
    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    /**
     * Get categories for this blog post.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(BlogCategory::class, 'blog_category_posts', 'post_id', 'category_id');
    }


    /**
     * Scope a query to filter by status.
     */
    public function scopeStatus(Builder $query, BlogStatus|string $status): Builder
    {
        if ($status instanceof BlogStatus) {
            return $query->where('status', $status);
        }

        return $query->where('status', $status);
    }

    /**
     * Scope a query to filter by multiple categories.
     */
    public function scopeInCategories(Builder $query, array $categoryIds): Builder
    {
        return $query->whereHas('categories', function ($q) use ($categoryIds) {
            $q->whereIn('blog_categories.id', $categoryIds);
        });
    }

    /**
     * Scope a query to order by published date (latest first).
     */
    public function scopeLatest(Builder $query): Builder
    {
        return $query->orderByDesc('published_at');
    }

    /**
     * Scope a query to order by oldest published date.
     */
    public function scopeOldest(Builder $query): Builder
    {
        return $query->orderBy('published_at');
    }

    /**
     * Check if the post is published.
     */
    public function isPublished(): bool
    {
        return $this->status === BlogStatus::Published
            && $this->published_at !== null
            && $this->published_at <= now();
    }

    /**
     * Check if the post is draft.
     */
    public function isDraft(): bool
    {
        return $this->status === BlogStatus::Draft;
    }

    /**
     * Check if the post is featured.
     */
    public function scopeFeatured(Builder $query): Builder
    {
        return $query->where('featured', true);
    }

    /**
     * Check if the post has a layout.
     */
    public function hasLayout(): bool
    {
        return !empty($this->layout);
    }

    public function scopePublished(Builder $query): Builder
    {
        return $query->where('status', BlogStatus::Published)
            ->where('published_at', '<=', now());
    }

    public static function getPostsByCategoryId(int $categoryId, string $locale): LengthAwarePaginator
    {
        return BlogPost::whereHas('translations', fn(Builder $query) => $query->where('locale', $locale))
            ->when($categoryId, function (Builder $query) use ($categoryId) {
                $query->whereHas('categories', fn($q) => $q->where('blog_categories.id', $categoryId));
            })
            ->with([
                'author:id,first_name,last_name',
                'translations' => fn($query) => $query->where('locale', $locale)
            ])
            ->filter(new PublicBlogPostFilter(request()))
            ->published()
            ->paginate(min((int)request('limit', 10), 100));
    }

    public static function getPostBySlug(string $slug, string $locale): ?BlogPost
    {
        $post = self::whereHas('translations', fn(Builder $query) => $query->where('slug', $slug)->where('locale', $locale))
            ->with(['author:id,first_name,last_name', 'translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->published()
            ->first();

        if ($post && $translation = $post->translations->first()) {
            $post->fill($translation->only([
                'title', 'slug', 'content', 'summary', 'image', 'meta_title', 'meta_description'
            ]));
        }
        return $post?->makeHidden('translations');
    }


    /*
     *
     * Get posts by feature.
     * This method retrieves posts that are marked as featured.
     *
     * @param mixed $categoryId
     * @param string $locale
     * @return Collection
     *
     */
    public static function getPostsByFeature(string $locale, int $categoryId = null): Collection
    {

        return self::whereHas('translations', fn(Builder $query) => $query->where('locale', $locale))
            ->with(['author:id,first_name,last_name', 'translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->when($categoryId, function (Builder $query) use ($categoryId) {
                $query->whereHas('categories', fn($q) => $q->where('blog_categories.id', $categoryId));
            })
            ->featured()
            ->published()
            ->orderByDesc('published_at')
            ->limit(min((int)request('limit', 10), 20))
            ->get();
    }

    /*
     * Get posts by top views.
     * This method retrieves posts ordered by the number of views.
     *
     * @param string $locale
     * @param int|null $categoryId
     * @return Collection
     *
     */
    public static function getPostsByTopViews(string $locale, int $categoryId = null): Collection
    {
        return self::whereHas('translations', fn(Builder $query) => $query->where('locale', $locale))
            ->with(['author:id,first_name,last_name', 'translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->when($categoryId, function (Builder $query) use ($categoryId) {
                $query->whereHas('categories', fn($q) => $q->where('blog_categories.id', $categoryId));
            })
            ->published()
            ->orderByDesc('views')
            ->limit(min((int)request('limit', 10), 20))
            ->get();
    }

    /**
     * Get the latest posts.
     * This method retrieves the latest posts based on the published date.
     *
     * @param string $locale
     * @param int|null $categoryId
     * @return Collection
     */

    public static function getLatestPosts(string $locale, int $categoryId = null): Collection
    {
        return self::whereHas('translations', fn(Builder $query) => $query->where('locale', $locale))
            ->with(['author:id,first_name,last_name', 'translations' => function ($query) use ($locale) {
                $query->where('locale', $locale);
            }])
            ->when($categoryId, function (Builder $query) use ($categoryId) {
                $query->whereHas('categories', fn($q) => $q->where('blog_categories.id', $categoryId));
            })
            ->published()
            ->latest()
            ->limit(min((int)request('limit', 10), 20))
            ->get();
    }

    /**
     * Get the reading time estimate.
     */
    public function getReadingTimeAttribute(): int
    {
        $content = $this->content;
        if (!$content) {
            return 1;
        }

        $wordCount = str_word_count(strip_tags($content));
        return max(1, ceil($wordCount / 200)); // Assuming 200 words per minute
    }
}
