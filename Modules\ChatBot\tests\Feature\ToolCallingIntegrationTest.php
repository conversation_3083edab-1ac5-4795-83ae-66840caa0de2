<?php

namespace Modules\ChatBot\Tests\Feature;

use Illuminate\Support\Facades\Http;
use Modules\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ModelAI\Models\ModelTool;

class ToolCallingIntegrationTest extends ChatBotTestCase
{
    protected ModelTool $webSearchTool;
    protected ModelTool $calculatorTool;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setupToolsForTesting();
    }

    protected function setupToolsForTesting(): void
    {
        // Create test tools
        $this->webSearchTool = ModelTool::factory()->create([
            'key' => 'web_search',
            'name' => 'Web Search',
            'description' => 'Search the web for information',
            'type' => 'custom',
            'provider' => 'custom',
            'api_endpoint' => 'https://api.example.com/search',
            'input_schema' => [
                'type' => 'object',
                'properties' => [
                    'query' => [
                        'type' => 'string',
                        'description' => 'Search query',
                    ],
                ],
                'required' => ['query'],
            ],
            'status' => 'active',
            'is_enabled' => true,
        ]);

        $this->calculatorTool = ModelTool::factory()->create([
            'key' => 'calculator',
            'name' => 'Calculator',
            'description' => 'Perform mathematical calculations',
            'type' => 'custom',
            'provider' => 'custom',
            'api_endpoint' => 'https://api.example.com/calculate',
            'input_schema' => [
                'type' => 'object',
                'properties' => [
                    'expression' => [
                        'type' => 'string',
                        'description' => 'Mathematical expression to evaluate',
                    ],
                ],
                'required' => ['expression'],
            ],
            'status' => 'active',
            'is_enabled' => true,
        ]);

        // Attach tools to AI model
        $this->aiModel->tools()->attach($this->webSearchTool->id, [
            'is_enabled' => true,
            'priority' => 1,
            'max_usage_per_request' => 5,
            'rate_limit_per_minute' => 10,
            'configuration' => ['timeout' => 30],
        ]);

        $this->aiModel->tools()->attach($this->calculatorTool->id, [
            'is_enabled' => true,
            'priority' => 2,
            'max_usage_per_request' => 3,
            'rate_limit_per_minute' => 20,
            'configuration' => ['precision' => 2],
        ]);

        // Update bot to enable tool calling
        $this->bot->update([
            'tool_calling_mode' => 'auto',
        ]);
    }

    /** @test */
    public function it_can_handle_ai_response_with_tool_calls()
    {
        $this->authenticateUser();

        // Mock AI response with tool calls
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'role' => 'assistant',
                            'content' => null,
                            'tool_calls' => [
                                [
                                    'id' => 'call_123',
                                    'type' => 'function',
                                    'function' => [
                                        'name' => 'web_search',
                                        'arguments' => json_encode(['query' => 'Laravel framework']),
                                    ],
                                ],
                            ],
                        ],
                        'finish_reason' => 'tool_calls',
                    ],
                ],
                'usage' => [
                    'prompt_tokens' => 60,
                    'completion_tokens' => 30,
                    'total_tokens' => 90,
                ],
            ], 200),

            // Mock tool execution
            'api.example.com/search' => Http::response([
                'results' => [
                    'Laravel is a PHP web framework',
                    'Created by Taylor Otwell',
                    'First released in 2011',
                ],
                'status' => 'success',
            ], 200),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Search for information about Laravel framework',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        $aiMessage = $response->json('data');
        $this->assertEquals('assistant', $aiMessage['role']);
        $this->assertNotNull($aiMessage['tool_calls']);
        $this->assertCount(1, $aiMessage['tool_calls']);

        $toolCall = $aiMessage['tool_calls'][0];
        $this->assertEquals('call_123', $toolCall['id']);
        $this->assertEquals('function', $toolCall['type']);
        $this->assertEquals('web_search', $toolCall['function']['name']);

        // Verify tool response message was created
        $toolMessage = Message::where('conversation_id', $this->conversation->id)
            ->where('role', MessageRole::TOOL)
            ->where('tool_call_id', 'call_123')
            ->first();

        $this->assertNotNull($toolMessage);
        $this->assertEquals('tool', $toolMessage->role->value);
        
        $toolContent = json_decode($toolMessage->content, true);
        $this->assertEquals('success', $toolContent['status']);
        $this->assertArrayHasKey('results', $toolContent);
    }

    /** @test */
    public function it_can_handle_multiple_tool_calls()
    {
        $this->authenticateUser();

        // Mock AI response with multiple tool calls
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'role' => 'assistant',
                            'content' => null,
                            'tool_calls' => [
                                [
                                    'id' => 'call_search',
                                    'type' => 'function',
                                    'function' => [
                                        'name' => 'web_search',
                                        'arguments' => json_encode(['query' => 'PHP 8 features']),
                                    ],
                                ],
                                [
                                    'id' => 'call_calc',
                                    'type' => 'function',
                                    'function' => [
                                        'name' => 'calculator',
                                        'arguments' => json_encode(['expression' => '2 + 2']),
                                    ],
                                ],
                            ],
                        ],
                        'finish_reason' => 'tool_calls',
                    ],
                ],
                'usage' => [
                    'prompt_tokens' => 80,
                    'completion_tokens' => 40,
                    'total_tokens' => 120,
                ],
            ], 200),

            // Mock tool executions
            'api.example.com/search' => Http::response([
                'results' => ['PHP 8 features search results'],
                'status' => 'success',
            ], 200),

            'api.example.com/calculate' => Http::response([
                'result' => 4,
                'expression' => '2 + 2',
                'status' => 'success',
            ], 200),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Search for PHP 8 features and calculate 2 + 2',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        $aiMessage = $response->json('data');
        $this->assertCount(2, $aiMessage['tool_calls']);

        // Verify both tool response messages were created
        $toolMessages = Message::where('conversation_id', $this->conversation->id)
            ->where('role', MessageRole::TOOL)
            ->get();

        $this->assertCount(2, $toolMessages);

        $searchToolMessage = $toolMessages->where('tool_call_id', 'call_search')->first();
        $calcToolMessage = $toolMessages->where('tool_call_id', 'call_calc')->first();

        $this->assertNotNull($searchToolMessage);
        $this->assertNotNull($calcToolMessage);
    }

    /** @test */
    public function it_handles_tool_execution_failures()
    {
        $this->authenticateUser();

        // Mock AI response with tool call
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'role' => 'assistant',
                            'content' => null,
                            'tool_calls' => [
                                [
                                    'id' => 'call_fail',
                                    'type' => 'function',
                                    'function' => [
                                        'name' => 'web_search',
                                        'arguments' => json_encode(['query' => 'test']),
                                    ],
                                ],
                            ],
                        ],
                        'finish_reason' => 'tool_calls',
                    ],
                ],
                'usage' => ['prompt_tokens' => 50, 'completion_tokens' => 25, 'total_tokens' => 75],
            ], 200),

            // Mock failed tool execution
            'api.example.com/search' => Http::response([
                'error' => 'Service temporarily unavailable',
            ], 503),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'This tool call will fail',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        // Verify tool error message was created
        $toolMessage = Message::where('conversation_id', $this->conversation->id)
            ->where('role', MessageRole::TOOL)
            ->where('tool_call_id', 'call_fail')
            ->first();

        $this->assertNotNull($toolMessage);
        
        $toolContent = json_decode($toolMessage->content, true);
        $this->assertArrayHasKey('error', $toolContent);
    }

    /** @test */
    public function it_generates_follow_up_response_after_tool_execution()
    {
        $this->authenticateUser();

        // Mock initial AI response with tool call
        Http::fake([
            'api.openai.com/*' => Http::sequence()
                ->push([
                    'choices' => [
                        [
                            'message' => [
                                'role' => 'assistant',
                                'content' => null,
                                'tool_calls' => [
                                    [
                                        'id' => 'call_search',
                                        'type' => 'function',
                                        'function' => [
                                            'name' => 'web_search',
                                            'arguments' => json_encode(['query' => 'Laravel']),
                                        ],
                                    ],
                                ],
                            ],
                            'finish_reason' => 'tool_calls',
                        ],
                    ],
                    'usage' => ['prompt_tokens' => 50, 'completion_tokens' => 25, 'total_tokens' => 75],
                ], 200)
                ->push([
                    'choices' => [
                        [
                            'message' => [
                                'role' => 'assistant',
                                'content' => 'Based on the search results, Laravel is a popular PHP framework.',
                            ],
                            'finish_reason' => 'stop',
                        ],
                    ],
                    'usage' => ['prompt_tokens' => 100, 'completion_tokens' => 50, 'total_tokens' => 150],
                ], 200),

            // Mock tool execution
            'api.example.com/search' => Http::response([
                'results' => ['Laravel is a PHP web framework'],
                'status' => 'success',
            ], 200),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Tell me about Laravel',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        // Should have created multiple messages:
        // 1. Assistant message with tool calls
        // 2. Tool response message
        // 3. Follow-up assistant message

        $messages = Message::where('conversation_id', $this->conversation->id)
            ->orderBy('created_at')
            ->get();

        $assistantMessages = $messages->where('role', MessageRole::ASSISTANT);
        $toolMessages = $messages->where('role', MessageRole::TOOL);

        $this->assertGreaterThanOrEqual(1, $assistantMessages->count());
        $this->assertEquals(1, $toolMessages->count());

        // Verify follow-up message was created
        $followUpMessage = $assistantMessages->where('metadata.is_follow_up', true)->first();
        if ($followUpMessage) {
            $this->assertEquals('Based on the search results, Laravel is a popular PHP framework.', $followUpMessage->content);
        }
    }

    /** @test */
    public function it_respects_tool_usage_limits()
    {
        $this->authenticateUser();

        // Update tool to have very low rate limit
        $this->aiModel->tools()->updateExistingPivot($this->webSearchTool->id, [
            'rate_limit_per_minute' => 1,
        ]);

        // Mock AI response with tool call
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'role' => 'assistant',
                            'content' => null,
                            'tool_calls' => [
                                [
                                    'id' => 'call_limit_test',
                                    'type' => 'function',
                                    'function' => [
                                        'name' => 'web_search',
                                        'arguments' => json_encode(['query' => 'test']),
                                    ],
                                ],
                            ],
                        ],
                        'finish_reason' => 'tool_calls',
                    ],
                ],
                'usage' => ['prompt_tokens' => 50, 'completion_tokens' => 25, 'total_tokens' => 75],
            ], 200),

            'api.example.com/search' => Http::response([
                'results' => ['Test results'],
                'status' => 'success',
            ], 200),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'First search request',
        ]);

        // First request should succeed
        $response1 = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());
        $this->assertApiResponse($response1, 201);

        // Second request should be rate limited (in a real implementation)
        // For this test, we're just verifying the system handles it gracefully
        $userMessage2 = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Second search request (should be limited)',
        ]);

        $response2 = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());
        
        // Should still return a response, but tool might not be executed
        $this->assertTrue(in_array($response2->status(), [200, 201]));
    }

    /** @test */
    public function it_handles_unknown_tool_calls()
    {
        $this->authenticateUser();

        // Mock AI response with unknown tool
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'role' => 'assistant',
                            'content' => null,
                            'tool_calls' => [
                                [
                                    'id' => 'call_unknown',
                                    'type' => 'function',
                                    'function' => [
                                        'name' => 'unknown_tool',
                                        'arguments' => json_encode(['param' => 'value']),
                                    ],
                                ],
                            ],
                        ],
                        'finish_reason' => 'tool_calls',
                    ],
                ],
                'usage' => ['prompt_tokens' => 50, 'completion_tokens' => 25, 'total_tokens' => 75],
            ], 200),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Call unknown tool',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        // Verify error tool message was created
        $toolMessage = Message::where('conversation_id', $this->conversation->id)
            ->where('role', MessageRole::TOOL)
            ->where('tool_call_id', 'call_unknown')
            ->first();

        $this->assertNotNull($toolMessage);
        
        $toolContent = json_decode($toolMessage->content, true);
        $this->assertArrayHasKey('error', $toolContent);
        $this->assertStringContainsString('Tool not found', $toolContent['error']);
    }

    /** @test */
    public function it_includes_tool_metadata_in_responses()
    {
        $this->authenticateUser();

        // Mock AI response with tool call
        Http::fake([
            'api.openai.com/*' => Http::response([
                'choices' => [
                    [
                        'message' => [
                            'role' => 'assistant',
                            'content' => null,
                            'tool_calls' => [
                                [
                                    'id' => 'call_meta',
                                    'type' => 'function',
                                    'function' => [
                                        'name' => 'web_search',
                                        'arguments' => json_encode(['query' => 'metadata test']),
                                    ],
                                ],
                            ],
                        ],
                        'finish_reason' => 'tool_calls',
                    ],
                ],
                'usage' => ['prompt_tokens' => 50, 'completion_tokens' => 25, 'total_tokens' => 75],
            ], 200),

            'api.example.com/search' => Http::response([
                'results' => ['Metadata test results'],
                'status' => 'success',
            ], 200),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Test tool metadata',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        // Verify tool message includes metadata
        $toolMessage = Message::where('conversation_id', $this->conversation->id)
            ->where('role', MessageRole::TOOL)
            ->first();

        $this->assertNotNull($toolMessage);
        $this->assertNotNull($toolMessage->metadata);
        $this->assertEquals('web_search', $toolMessage->metadata['tool_name']);
        $this->assertArrayHasKey('parent_message_id', $toolMessage->metadata);
    }
}
