<?php

namespace Modules\Blog\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use PHPUnit\Framework\Attributes\Test;
use Modules\Blog\Facades\BlogFacade;
use Modules\Blog\Services\BlogService;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Models\BlogCategoryTranslation;
use Modules\Blog\Models\BlogPostTranslation;
use Modules\Blog\Enums\BlogStatus;
use Modules\User\Models\User;

class BlogFacadeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Run migrations for Blog module
        $this->artisan('migrate', ['--path' => 'Modules/Blog/database/migrations']);
        $this->artisan('migrate', ['--path' => 'database/migrations']);
    }

    #[Test]
    public function it_resolves_to_blog_service()
    {
        $service = BlogFacade::getFacadeRoot();
        
        $this->assertInstanceOf(BlogService::class, $service);
    }

    #[Test]
    public function it_can_get_root_categories()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);
        BlogCategoryTranslation::factory()->create(['category_id' => $category->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = BlogFacade::getRootCategories('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1, $result);
        $this->assertEquals($category->id, $result->first()->id);
    }

    #[Test]
    public function it_can_get_active_category_hierarchy()
    {
        $parent = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);
        $child = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => $parent->id]);

        BlogCategoryTranslation::factory()->create(['category_id' => $parent->id, 'locale' => 'en']);
        BlogCategoryTranslation::factory()->create(['category_id' => $child->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = BlogFacade::getActiveCategoryHierarchy('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1, $result); // One root category
    }

    #[Test]
    public function it_can_get_category_by_slug()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active]);
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'en',
            'slug' => 'test-category'
        ]);

        App::setLocale('en');
        $result = BlogFacade::getCategoryBySlug('test-category', 'en');

        $this->assertInstanceOf(BlogCategory::class, $result);
        $this->assertEquals($category->id, $result->id);
    }

    #[Test]
    public function it_returns_null_when_category_not_found_by_slug()
    {
        App::setLocale('en');
        $result = BlogFacade::getCategoryBySlug('non-existent-slug', 'en');

        $this->assertNull($result);
    }

    #[Test]
    public function it_can_get_posts_by_category_id()
    {
        $user = User::factory()->create();
        $category = BlogCategory::factory()->create();
        $post = BlogPost::factory()->create(['status' => BlogStatus::Published, 'author_id' => $user->id, 'published_at' => now()->subDay()]);

        $post->categories()->attach($category->id);
        BlogPostTranslation::factory()->create(['post_id' => $post->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = BlogFacade::getPostsByCategoryId($category->id, 'en');

        $this->assertInstanceOf(LengthAwarePaginator::class, $result);
        $this->assertCount(1, $result->items());
        $this->assertEquals($post->id, $result->items()[0]->id);
    }

    #[Test]
    public function it_can_get_feature_posts()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDay(),
            'featured' => true
        ]);

        BlogPostTranslation::factory()->create(['post_id' => $post->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = BlogFacade::getFeaturePosts('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1, $result);
        $this->assertEquals($post->id, $result->first()->id);
    }

    #[Test]
    public function it_can_get_top_posts()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDay()
        ]);

        BlogPostTranslation::factory()->create(['post_id' => $post->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = BlogFacade::getTopPosts('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1, $result);
        $this->assertEquals($post->id, $result->first()->id);
    }

    #[Test]
    public function it_can_get_latest_posts()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create([
            'status' => BlogStatus::Published,
            'author_id' => $user->id,
            'published_at' => now()->subDay()
        ]);

        BlogPostTranslation::factory()->create(['post_id' => $post->id, 'locale' => 'en']);

        App::setLocale('en');
        $result = BlogFacade::getLatestPosts('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(1, $result);
        $this->assertEquals($post->id, $result->first()->id);
    }

    #[Test]
    public function it_can_get_post_by_slug()
    {
        $user = User::factory()->create();
        $post = BlogPost::factory()->create(['status' => BlogStatus::Published, 'author_id' => $user->id, 'published_at' => now()->subDay()]);
        BlogPostTranslation::factory()->create([
            'post_id' => $post->id,
            'locale' => 'en',
            'slug' => 'test-post'
        ]);

        App::setLocale('en');
        $result = BlogFacade::getPostBySlug('test-post', 'en');

        $this->assertInstanceOf(BlogPost::class, $result);
        $this->assertEquals($post->id, $result->id);
    }

    #[Test]
    public function it_returns_null_when_post_not_found_by_slug()
    {
        App::setLocale('en');
        $result = BlogFacade::getPostBySlug('non-existent-slug', 'en');

        $this->assertNull($result);
    }

    #[Test]
    public function it_handles_empty_results_gracefully()
    {
        App::setLocale('en');
        $result = BlogFacade::getFeaturePosts('en');

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(0, $result);
    }

    #[Test]
    public function it_delegates_method_calls_to_service()
    {
        // Mock the service
        $mockService = $this->createMock(BlogService::class);
        $mockService->expects($this->once())
            ->method('getRootCategories')
            ->with('en')
            ->willReturn(collect());

        // Replace the service in the container
        $this->app->instance('blog.service', $mockService);

        // Call the facade method
        App::setLocale('en');
        $result = BlogFacade::getRootCategories('en');

        $this->assertInstanceOf(Collection::class, $result);
    }

    #[Test]
    public function it_maintains_method_signatures()
    {
        $reflection = new \ReflectionClass(BlogFacade::class);
        $docComment = $reflection->getDocComment();

        // Check that all expected methods are documented
        $this->assertStringContainsString('@method static Collection getRootCategories', $docComment);
        $this->assertStringContainsString('@method static Collection getActiveCategoryHierarchy', $docComment);
        $this->assertStringContainsString('@method static BlogCategory|null getCategoryBySlug', $docComment);
        $this->assertStringContainsString('@method static LengthAwarePaginator getPostsByCategoryId', $docComment);
        $this->assertStringContainsString('@method static Collection getFeaturePosts', $docComment);
        $this->assertStringContainsString('@method static Collection getTopPosts', $docComment);
        $this->assertStringContainsString('@method static Collection getLatestPosts', $docComment);
        $this->assertStringContainsString('@method static BlogPost|null getPostBySlug', $docComment);
    }

    #[Test]
    public function it_passes_parameters_correctly()
    {
        $category = BlogCategory::factory()->create(['status' => BlogStatus::Active, 'parent_id' => null]);
        BlogCategoryTranslation::factory()->create([
            'category_id' => $category->id,
            'locale' => 'vi'
        ]);

        App::setLocale('vi');
        $result = BlogFacade::getRootCategories('vi');

        $this->assertCount(1, $result);
        $this->assertEquals('vi', $result->first()->translations->first()->locale);
    }
}
