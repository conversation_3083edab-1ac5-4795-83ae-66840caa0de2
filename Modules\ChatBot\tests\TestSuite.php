<?php

namespace Modules\ChatBot\Tests;

use PHPUnit\Framework\TestSuite as PHPUnitTestSuite;

/**
 * ChatBot Module Test Suite
 * 
 * This class organizes all tests for the ChatBot module into logical groups
 * for easier test execution and reporting.
 */
class TestSuite extends PHPUnitTestSuite
{
    /**
     * Create the test suite for ChatBot module.
     */
    public static function suite(): self
    {
        $suite = new self('ChatBot Module Tests');
        
        // Add Unit Tests
        $suite->addTestSuite(\Modules\ChatBot\Tests\Unit\Models\ConversationTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Unit\Models\MessageTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Unit\Services\ConversationServiceTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Unit\Services\MessageServiceTest::class);
        
        // Add Integration Tests
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\ConversationApiTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\MessageApiTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\AIIntegrationTest::class);

        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\ToolCallingIntegrationTest::class);
        
        return $suite;
    }

    /**
     * Create unit tests suite only.
     */
    public static function unitSuite(): self
    {
        $suite = new self('ChatBot Unit Tests');
        
        $suite->addTestSuite(\Modules\ChatBot\Tests\Unit\Models\ConversationTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Unit\Models\MessageTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Unit\Services\ConversationServiceTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Unit\Services\MessageServiceTest::class);
        
        return $suite;
    }

    /**
     * Create integration tests suite only.
     */
    public static function integrationSuite(): self
    {
        $suite = new self('ChatBot Integration Tests');
        
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\ConversationApiTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\MessageApiTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\AIIntegrationTest::class);

        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\ToolCallingIntegrationTest::class);
        
        return $suite;
    }

    /**
     * Create API tests suite only.
     */
    public static function apiSuite(): self
    {
        $suite = new self('ChatBot API Tests');
        
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\ConversationApiTest::class);
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\MessageApiTest::class);
        
        return $suite;
    }

    /**
     * Create AI-specific tests suite only.
     */
    public static function aiSuite(): self
    {
        $suite = new self('ChatBot AI Tests');
        
        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\AIIntegrationTest::class);

        $suite->addTestSuite(\Modules\ChatBot\Tests\Feature\ToolCallingIntegrationTest::class);
        
        return $suite;
    }
}
