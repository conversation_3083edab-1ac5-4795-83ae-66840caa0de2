<?php

namespace Modules\ChatBot\Services;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Cache;
use Modules\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Models\Bot;

class BotService
{
    /**
     * Cache tag for bots
     */
    private const CACHE_TAG = 'bots';

    // ========================================
    // PUBLIC METHODS (exposed via facade)
    // ========================================

    /**
     * Get all active bots for public use.
     * This is a public-facing method with caching.
     */
    public function getActiveBots(): \Illuminate\Support\Collection
    {
        if (!enabledCache()) {
            return Bot::active()
                ->with(['aiModel.categories', 'aiModel.service', 'owner'])
                ->orderBy('name')
                ->get()
                ->map(function (Bot $bot) {
                    // Ensure sensitive data is hidden via model's $hidden array
                    return $bot;
                });
        }

        return Cache::tags(self::CACHE_TAG)->remember(
            key: 'bots.active.list',
            ttl: cacheTTL(),
            callback: fn() => Bot::active()
                ->with(['aiModel.categories', 'aiModel.service', 'owner'])
                ->orderBy('name')
                ->get()
                ->map(function (Bot $bot) {
                    // Ensure sensitive data is hidden via model's $hidden array
                    return $bot;
                })
        );
    }

    /**
     * Get all publicly visible bots (active status).
     * This is a public-facing method with caching.
     */
    public function getPublicBots(): \Illuminate\Support\Collection
    {
        if (!enabledCache()) {
            return Bot::public()
                ->with(['aiModel.categories', 'aiModel.service', 'owner'])
                ->orderBy('name')
                ->get()
                ->map(function (Bot $bot) {
                    // Ensure sensitive data is hidden via model's $hidden array
                    return $bot;
                });
        }

        return Cache::tags(self::CACHE_TAG)->remember(
            key: 'bots.public.list',
            ttl: cacheTTL(),
            callback: fn() => Bot::public()
                ->with(['aiModel.categories', 'aiModel.service', 'owner'])
                ->orderBy('name')
                ->get()
                ->map(function (Bot $bot) {
                    // Ensure sensitive data is hidden via model's $hidden array
                    return $bot;
                })
        );
    }

    /**
     * Get bot by UUID (only public bots).
     * This is a public-facing method with caching.
     */
    public function getByUuid(string $uuid): ?Bot
    {
        if (!enabledCache()) {
            return Bot::public()
                ->with(['aiModel.categories', 'aiModel.service', 'owner'])
                ->where('uuid', $uuid)
                ->first();
        }

        return Cache::tags(self::CACHE_TAG)->remember(
            key: "bots.uuid.{$uuid}",
            ttl: cacheTTL(),
            callback: fn() => Bot::public()
                ->with(['aiModel.categories', 'aiModel.service', 'owner'])
                ->where('uuid', $uuid)
                ->first()
        );
    }

    /**
     * Get bots for dropdown/select options.
     * This is a public-facing method with caching.
     */
    public function getBotsForDropdown(): Collection
    {
        if (!enabledCache()) {
            return Bot::active()
                ->select('id', 'name', 'uuid')
                ->orderBy('name')
                ->get();
        }

        return Cache::tags(self::CACHE_TAG)->remember(
            key: 'bots.dropdown',
            ttl: cacheTTL(),
            callback: fn() => Bot::active()
                ->select('id', 'name', 'uuid')
                ->orderBy('name')
                ->get()
        );
    }

    /**
     * Search bots by name or description.
     * This is a public-facing method.
     */
    public function searchBots(string $query, int $limit = 10): Collection
    {
        return Bot::public()
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('description', 'like', "%{$query}%");
            })
            ->with(['aiModel.categories', 'aiModel.service', 'owner'])
            ->limit($limit)
            ->get();
    }

    // ========================================
    // ADMINISTRATIVE METHODS (not exposed via facade)
    // ========================================

    /**
     * Get bot by ID with relationships.
     * Administrative method for backend operations.
     */
    public function getById(int $id): ?Bot
    {
        return Bot::with(['aiModel.categories', 'aiModel.service', 'owner'])
            ->find($id);
    }

    /**
     * Get bots owned by a specific entity.
     * Administrative method for backend operations.
     */
    public function getBotsByOwner(string $ownerType, int $ownerId): Collection
    {
        return Bot::ownedBy($ownerType, $ownerId)
            ->with(['aiModel.categories', 'aiModel.service'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get active bots owned by a specific entity.
     * Administrative method for backend operations.
     */
    public function getActiveBotsByOwner(string $ownerType, int $ownerId): Collection
    {
        return Bot::active()
            ->ownedBy($ownerType, $ownerId)
            ->with(['aiModel.categories', 'aiModel.service'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get bots by status.
     * Administrative method for backend operations.
     */
    public function getBotsByStatus(BotStatus $status): Collection
    {
        return Bot::status($status)
            ->with(['aiModel.categories', 'aiModel.service', 'owner'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get bots owned by entity for dropdown.
     * Administrative method for backend operations.
     */
    public function getBotsByOwnerForDropdown(string $ownerType, int $ownerId): Collection
    {
        return Bot::active()
            ->ownedBy($ownerType, $ownerId)
            ->select('id', 'name', 'uuid')
            ->orderBy('name')
            ->get();
    }

    /**
     * Create a new bot.
     * Administrative method for backend operations.
     */
    public function createBot(array $data): Bot
    {
        $bot = Bot::create($data);

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $bot;
    }

    /**
     * Update an existing bot.
     * Administrative method for backend operations.
     */
    public function updateBot(Bot $bot, array $data): Bot
    {
        $bot->update($data);

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $bot->fresh(['aiModel.categories', 'aiModel.service', 'owner']);
    }

    /**
     * Delete a bot (soft delete).
     * Administrative method for backend operations.
     */
    public function deleteBot(Bot $bot): bool
    {
        $result = $bot->delete();

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $result;
    }

    /**
     * Restore a soft-deleted bot.
     * Administrative method for backend operations.
     */
    public function restoreBot(Bot $bot): bool
    {
        $result = $bot->restore();

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $result;
    }

    /**
     * Force delete a bot.
     * Administrative method for backend operations.
     */
    public function forceDeleteBot(Bot $bot): bool
    {
        $result = $bot->forceDelete();

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $result;
    }

    /**
     * Bulk delete bots.
     * Administrative method for backend operations.
     */
    public function bulkDeleteBots(array $ids): int
    {
        $count = Bot::whereIn('id', $ids)->delete();

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $count;
    }

    /**
     * Bulk restore bots.
     * Administrative method for backend operations.
     */
    public function bulkRestoreBots(array $ids): int
    {
        $count = Bot::onlyTrashed()->whereIn('id', $ids)->restore();

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $count;
    }

    /**
     * Bulk force delete bots.
     * Administrative method for backend operations.
     */
    public function bulkForceDeleteBots(array $ids): int
    {
        $count = Bot::onlyTrashed()->whereIn('id', $ids)->forceDelete();

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $count;
    }

    /**
     * Change bot status.
     * Administrative method for backend operations.
     */
    public function changeBotStatus(Bot $bot, BotStatus $status): Bot
    {
        $bot->update(['status' => $status]);

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $bot->fresh(['aiModel.categories', 'aiModel.service', 'owner']);
    }

    /**
     * Bulk change bot status.
     * Administrative method for backend operations.
     */
    public function bulkChangeBotStatus(array $ids, BotStatus $status): int
    {
        $count = Bot::whereIn('id', $ids)->update(['status' => $status]);

        // Clear cache
        if (enabledCache()) {
            Cache::tags(self::CACHE_TAG)->flush();
        }

        return $count;
    }

    /**
     * Get bot statistics.
     * Administrative method for backend operations.
     */
    public function getBotStatistics(): array
    {
        $total = Bot::count();
        $active = Bot::active()->count();
        $draft = Bot::status(BotStatus::Draft)->count();
        $review = Bot::status(BotStatus::Review)->count();
        $paused = Bot::status(BotStatus::Paused)->count();
        $banned = Bot::status(BotStatus::Banned)->count();
        $trashed = Bot::onlyTrashed()->count();

        return [
            'total' => $total,
            'active' => $active,
            'draft' => $draft,
            'review' => $review,
            'paused' => $paused,
            'banned' => $banned,
            'trashed' => $trashed,
            'active_percentage' => $total > 0 ? round(($active / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Get paginated bots with optional filtering.
     * Administrative method for backend operations.
     */
    public function getPaginatedBots(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Bot::with(['aiModel.categories', 'aiModel.service', 'owner']);

        // Apply filters
        if (isset($filters['status'])) {
            $query->status($filters['status']);
        }

        if (isset($filters['owner_type']) && isset($filters['owner_id'])) {
            $query->ownedBy($filters['owner_type'], $filters['owner_id']);
        }

        if (isset($filters['ai_model_id'])) {
            $query->where('ai_model_id', $filters['ai_model_id']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', "%{$filters['search']}%")
                  ->orWhere('description', 'like', "%{$filters['search']}%");
            });
        }

        return $query->orderBy('name')->paginate($perPage);
    }

    // ========================================
    // USER-FOCUSED METHODS (for Auth controllers)
    // ========================================

    /**
     * Get bots accessible by a user.
     */
    public function getUserAccessibleBots(int $userId, string $userType, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Bot::accessibleBy($userId)
                   ->with(['aiModel', 'owner'])
                   ->active();

        // Apply filters
        if (!empty($filters['visibility'])) {
            $query->where('visibility', $filters['visibility']);
        }

        if (!empty($filters['bot_type'])) {
            $query->where('bot_type', $filters['bot_type']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get bots owned by a user.
     */
    public function getUserOwnedBots(int $userId, string $userType, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Bot::ownedBy($userType, $userId)
                   ->with(['aiModel'])
                   ->active();

        // Apply filters
        if (!empty($filters['visibility'])) {
            $query->where('visibility', $filters['visibility']);
        }

        if (!empty($filters['bot_type'])) {
            $query->where('bot_type', $filters['bot_type']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get bots shared with a user.
     */
    public function getUserSharedBots(int $userId, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Bot::whereHas('shares', function ($q) use ($userId) {
                    $q->where('user_id', $userId);
                })
                ->with(['aiModel', 'owner'])
                ->active();

        // Apply filters
        if (!empty($filters['visibility'])) {
            $query->where('visibility', $filters['visibility']);
        }

        if (!empty($filters['bot_type'])) {
            $query->where('bot_type', $filters['bot_type']);
        }

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get public bots with pagination.
     */
    public function getPublicBotsWithPagination(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = Bot::publicVisibility()
                   ->with(['aiModel', 'owner'])
                   ->active();

        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('description', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Search bots accessible by user with pagination.
     */
    public function searchBotsForUser(string $query, int $userId, string $userType, bool $includePublic = true, int $perPage = 15): LengthAwarePaginator
    {
        $botQuery = Bot::where(function ($q) use ($query) {
                        $q->where('name', 'like', '%' . $query . '%')
                          ->orWhere('description', 'like', '%' . $query . '%');
                    })
                    ->with(['aiModel', 'owner'])
                    ->active();

        if ($includePublic) {
            $botQuery->accessibleBy($userId);
        } else {
            $botQuery->ownedBy($userType, $userId);
        }

        return $botQuery->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get bot statistics for user context.
     */
    public function getBotStats(Bot $bot): array
    {
        $stats = [
            'total_conversations' => $bot->conversations()->count(),
            'active_conversations' => $bot->conversations()->where('status', 'active')->count(),
            'total_messages' => 0,
            'total_tokens' => 0,
            'total_cost' => 0,
            'shares_count' => $bot->userShares()->count(),
            'usage_by_day' => [],
        ];

        // Get message statistics
        $messageStats = $bot->conversations()
                           ->withCount('messages')
                           ->get()
                           ->reduce(function ($carry, $conversation) {
                               $carry['total_messages'] += $conversation->messages_count;
                               $carry['total_tokens'] += $conversation->total_tokens ?? 0;
                               $carry['total_cost'] += $conversation->total_cost ?? 0;
                               return $carry;
                           }, ['total_messages' => 0, 'total_tokens' => 0, 'total_cost' => 0]);

        $stats = array_merge($stats, $messageStats);

        // Get usage by day (last 30 days)
        $usageByDay = $bot->conversations()
                         ->selectRaw('DATE(created_at) as date, COUNT(*) as count')
                         ->where('created_at', '>=', now()->subDays(30))
                         ->groupBy('date')
                         ->orderBy('date')
                         ->get()
                         ->pluck('count', 'date')
                         ->toArray();

        $stats['usage_by_day'] = $usageByDay;

        return $stats;
    }



    /**
     * Share a bot with multiple users.
     */
    public function shareBotWithUsers(Bot $bot, array $userIds): array
    {
        $results = [];

        foreach ($userIds as $userId) {
            try {
                $share = $bot->shareWith($userId);
                $results[] = [
                    'user_id' => $userId,
                    'success' => $share !== null,
                    'share_id' => $share?->id,
                    'message' => $share ? 'Bot shared successfully' : 'Failed to share bot',
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'user_id' => $userId,
                    'success' => false,
                    'share_id' => null,
                    'message' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Unshare a bot from multiple users.
     */
    public function unshareBotFromUsers(Bot $bot, array $userIds): array
    {
        $results = [];

        foreach ($userIds as $userId) {
            try {
                $success = $bot->unshareFrom($userId);
                $results[] = [
                    'user_id' => $userId,
                    'success' => $success,
                    'message' => $success ? 'Bot unshared successfully' : 'Bot was not shared with this user',
                ];
            } catch (\Exception $e) {
                $results[] = [
                    'user_id' => $userId,
                    'success' => false,
                    'message' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Get bots that can be shared by a user.
     */
    public function getShareableBots(int $userId, string $userType): Collection
    {
        return Bot::where('owner_id', $userId)
                  ->where('owner_type', $userType)
                  ->personal()
                  ->active()
                  ->where('is_shareable', true)
                  ->with(['aiModel'])
                  ->get();
    }


}
