<?php

namespace Modules\ChatBot\Tests\Feature\Controllers;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Enums\BotType;
use Modules\ChatBot\Enums\BotStatus;
use Modules\ChatBot\Enums\BotVisibility;
use Modules\ModelAI\Models\ModelAI;
use Modules\User\Models\User;

class BotControllerTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $aiModel;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->aiModel = ModelAI::factory()->create([
            'status' => 'active',
            'function_calling' => true,
        ]);
    }

    /** @test */
    public function authenticated_user_can_list_their_bots()
    {
        $this->actingAs($this->user);

        // Create bots for this user
        Bot::factory()->count(3)->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
        ]);

        // Create bot for another user
        $otherUser = User::factory()->create();
        Bot::factory()->create([
            'owner_id' => $otherUser->id,
            'owner_type' => get_class($otherUser),
        ]);

        $response = $this->getJson('/api/v1/auth/bots');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'data' => [
                            '*' => [
                                'uuid',
                                'name',
                                'description',
                                'status',
                                'visibility',
                                'bot_type',
                                'is_shareable',
                                'created_at',
                                'updated_at',
                            ]
                        ],
                        'meta' => [
                            'current_page',
                            'per_page',
                            'total',
                        ]
                    ]
                ]);

        $this->assertCount(3, $response->json('data.data'));
    }

    /** @test */
    public function authenticated_user_can_create_bot()
    {
        $this->actingAs($this->user);

        $botData = [
            'name' => 'Test Bot',
            'description' => 'A test bot for testing',
            'ai_model_id' => $this->aiModel->id,
            'system_prompt' => 'You are a helpful assistant.',
            'visibility' => BotVisibility::PRIVATE->value,
            'is_shareable' => true,
        ];

        $response = $this->postJson('/api/v1/auth/bots', $botData);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'uuid',
                        'name',
                        'description',
                        'bot_type',
                        'ai_model',
                    ]
                ]);

        $this->assertDatabaseHas('bots', [
            'name' => 'Test Bot',
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'bot_type' => BotType::PERSONAL->value,
        ]);
    }

    /** @test */
    public function authenticated_user_can_view_their_bot()
    {
        $this->actingAs($this->user);

        $bot = Bot::factory()->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'ai_model_id' => $this->aiModel->id,
        ]);

        $response = $this->getJson("/api/v1/auth/bots/{$bot->uuid}");

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'message',
                    'data' => [
                        'uuid',
                        'name',
                        'description',
                        'ai_model',
                        'user_permission',
                    ]
                ]);

        $this->assertEquals($bot->uuid, $response->json('data.uuid'));
        $this->assertEquals('admin', $response->json('data.user_permission'));
    }

    /** @test */
    public function authenticated_user_can_update_their_bot()
    {
        $this->actingAs($this->user);

        $bot = Bot::factory()->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'name' => 'Original Name',
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'description' => 'Updated description',
        ];

        $response = $this->putJson("/api/v1/auth/bots/{$bot->uuid}", $updateData);

        $response->assertStatus(200);

        $this->assertDatabaseHas('bots', [
            'uuid' => $bot->uuid,
            'name' => 'Updated Name',
            'description' => 'Updated description',
        ]);
    }

    /** @test */
    public function authenticated_user_can_delete_their_bot()
    {
        $this->actingAs($this->user);

        $bot = Bot::factory()->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
        ]);

        $response = $this->deleteJson("/api/v1/auth/bots/{$bot->uuid}");

        $response->assertStatus(200);

        $this->assertSoftDeleted('bots', [
            'uuid' => $bot->uuid,
        ]);
    }

    /** @test */
    public function authenticated_user_can_share_their_bot()
    {
        $this->actingAs($this->user);

        $bot = Bot::factory()->create([
            'owner_id' => $this->user->id,
            'owner_type' => get_class($this->user),
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'is_shareable' => true,
        ]);

        $targetUser = User::factory()->create();

        $response = $this->postJson("/api/v1/auth/bots/{$bot->uuid}/share", [
            'user_email' => $targetUser->email,
        ]);

        $response->assertStatus(201);

        $this->assertDatabaseHas('bot_shares', [
            'bot_id' => $bot->id,
            'user_id' => $targetUser->id,
            'shareable_id' => $targetUser->id,
            'shareable_type' => get_class($targetUser),
        ]);
    }

    /** @test */
    public function user_cannot_access_other_users_private_bot()
    {
        $this->actingAs($this->user);

        $otherUser = User::factory()->create();
        $bot = Bot::factory()->create([
            'owner_id' => $otherUser->id,
            'owner_type' => get_class($otherUser),
            'visibility' => BotVisibility::PRIVATE,
        ]);

        $response = $this->getJson("/api/v1/auth/bots/{$bot->uuid}");

        $response->assertStatus(403);
    }

    /** @test */
    public function user_can_access_public_bots()
    {
        $this->actingAs($this->user);

        $otherUser = User::factory()->create();
        $bot = Bot::factory()->create([
            'owner_id' => $otherUser->id,
            'owner_type' => get_class($otherUser),
            'visibility' => BotVisibility::PUBLIC,
            'status' => BotStatus::Active,
        ]);

        $response = $this->getJson("/api/v1/auth/bots/{$bot->uuid}");

        $response->assertStatus(200);
    }

    /** @test */
    public function user_can_access_shared_bots()
    {
        $this->actingAs($this->user);

        $otherUser = User::factory()->create();
        $bot = Bot::factory()->create([
            'owner_id' => $otherUser->id,
            'owner_type' => get_class($otherUser),
            'visibility' => BotVisibility::PRIVATE,
            'bot_type' => BotType::PERSONAL,
            'status' => BotStatus::Active,
            'is_shareable' => true,
        ]);

        // Share bot with current user
        $bot->shareWith($this->user->id);

        $response = $this->getJson("/api/v1/auth/bots/{$bot->uuid}");

        $response->assertStatus(200);
        $this->assertEquals('read', $response->json('data.user_permission'));
    }

    /** @test */
    public function unauthenticated_user_cannot_access_auth_endpoints()
    {
        $response = $this->getJson('/api/v1/auth/bots');
        $response->assertStatus(401);

        $response = $this->postJson('/api/v1/auth/bots', []);
        $response->assertStatus(401);
    }

    /** @test */
    public function validation_errors_are_returned_for_invalid_bot_data()
    {
        $this->actingAs($this->user);

        $response = $this->postJson('/api/v1/auth/bots', [
            'name' => '', // Required field
            'ai_model_id' => 999, // Non-existent model
        ]);

        $response->assertStatus(422)
                ->assertJsonValidationErrors(['name', 'ai_model_id', 'system_prompt']);
    }
}
