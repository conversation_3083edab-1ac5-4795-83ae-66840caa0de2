<?php

namespace Modules\Blog\Http\Requests;

use Illuminate\Validation\Rule;
use Modules\Core\Http\Requests\BaseFormRequest;
use Modules\Blog\Enums\BlogStatus;
use Modules\Blog\Models\BlogCategory;
use Modules\Blog\Models\BlogCategoryTranslation;
use Modules\Language\Models\Language;

/**
 * @property mixed $id
 */
class BlogCategoryRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Handle authorization in controllers or middleware
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $categoryId = $this->route('category')?->id;

        return [
            'parent_id' => [
                'nullable',
                'integer',
                'exists:blog_categories,id',
                function ($attribute, $value, $fail) use ($categoryId) {
                    if ($value && $categoryId && $this->hasCircularParentReference($value, $categoryId)) {
                        $fail('The selected parent would create a circular reference.');
                    }
                }
            ],
            'layout' => ['nullable', 'string', 'max:255'],
            'status' => ['required', Rule::enum(BlogStatus::class)],
            'published_at' => ['nullable', 'date'],

            // Translations validation
            'translations' => ['required', 'array', 'min:1'],
            'translations.*.locale' => [
                'required', 'string', 'max:10',
                function ($attribute, $value, $fail) {
                    $language = Language::where('code', $value)->first();
                    if ($language && $language->status !== 'active') {
                        $fail("The language '{$value}' is not active.");
                    }
                },
            ],
            'translations.*.name' => ['required', 'string', 'max:255'],
            'translations.*.slug' => [
                'nullable',
                'string',
                'max:512',
                'alpha_dash',
                function ($attribute, $value, $fail) {
                    $index = (int)str_replace(['translations.', '.slug'], '', $attribute);
                    $locale = $this->input("translations.{$index}.locale");

                    if ($locale && $value) {
                        $query = BlogCategoryTranslation::query()
                            ->where('slug', $value)
                            ->where('locale', $locale);

                        if ($this->id) {
                            $query->where('category_id', '!=', $this->id);
                        }

                        if ($query->exists()) {
                            $fail(__("The slug ':value' is already used for locale '$locale'.", ['value' => $value, 'locale' => $locale]));
                        }
                    }
                },
            ],
            'translations.*.image' => ['nullable', 'string'],
            'translations.*.description' => ['nullable', 'string'],
            'translations.*.meta_title' => ['nullable', 'string', 'max:512'],
            'translations.*.meta_description' => ['nullable', 'string', 'max:1000'],
        ];
    }
    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'parent_id' => 'category parent',
            'layout' => 'layout',
            'translations.*.locale' => 'locale',
            'translations.*.name' => 'category name',
            'translations.*.slug' => 'slug',
            'translations.*.image' => 'image',
            'translations.*.description' => 'description',
            'translations.*.meta_title' => 'meta title',
            'translations.*.meta_description' => 'meta description',
        ];
    }


    /**
     * Check for circular parent reference.
     */
    protected function hasCircularParentReference(int $parentId, int $categoryId, array $visited = []): bool
    {
        if (in_array($parentId, $visited)) {
            return true;
        }

        if ($parentId == $categoryId) {
            return true;
        }

        $visited[] = $parentId;
        $parent = BlogCategory::find($parentId);

        if ($parent && $parent->parent_id) {
            return $this->hasCircularParentReference($parent->parent_id, $categoryId, $visited);
        }

        return false;
    }

}
