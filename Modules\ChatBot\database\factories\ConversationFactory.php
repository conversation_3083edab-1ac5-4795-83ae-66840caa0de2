<?php

namespace Modules\ChatBot\Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;
use Modules\ChatBot\Models\Bot;
use Modules\ChatBot\Models\Conversation;
use Modules\ChatBot\Enums\ConversationStatus;
use Modules\User\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\Modules\ChatBot\Models\Conversation>
 */
class ConversationFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     */
    protected $model = Conversation::class;

    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        $bot = Bot::active()->first() ?? Bot::factory()->withoutParameters()->create();
        $user = User::factory();

        return [
            'uuid' => Str::uuid(),
            'title' => $this->faker->sentence(3),
            'bot_id' => $bot->id,
            'owner_id' => $user,
            'owner_type' => \Modules\User\Models\User::class,
            'status' => $this->faker->randomElement(ConversationStatus::cases()),
            'message_count' => $this->faker->numberBetween(0, 50),
            'last_message_at' => $this->faker->optional(0.8)->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the conversation is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ConversationStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the conversation is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ConversationStatus::COMPLETED,
        ]);
    }

    /**
     * Indicate that the conversation is archived.
     */
    public function archived(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => ConversationStatus::ARCHIVED,
        ]);
    }

    /**
     * Indicate that the conversation has messages.
     */
    public function withMessages(int $count = 5): static
    {
        return $this->state(fn (array $attributes) => [
            'message_count' => $count,
            'last_message_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the conversation is recent.
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'last_message_at' => $this->faker->dateTimeBetween('-1 day', 'now'),
            'status' => ConversationStatus::ACTIVE,
        ]);
    }

    /**
     * Indicate that the conversation belongs to a specific bot.
     */
    public function forBot(Bot $bot): static
    {
        return $this->state(fn (array $attributes) => [
            'bot_id' => $bot->id,
        ]);
    }

    /**
     * Indicate that the conversation belongs to a specific user.
     */
    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'owner_id' => $user->id,
            'owner_type' => get_class($user),
        ]);
    }

    /**
     * Generate conversation with realistic title based on bot.
     */
    public function withRealisticTitle(): static
    {
        return $this->state(function (array $attributes) {
            $titles = [
                'Hỗ trợ khách hàng về sản phẩm',
                'Tư vấn kỹ thuật',
                'Giải đáp thắc mắc',
                'Hướng dẫn sử dụng',
                'Báo cáo lỗi hệ thống',
                'Yêu cầu tính năng mới',
                'Thảo luận về dự án',
                'Brainstorming ý tưởng',
                'Review code',
                'Phân tích dữ liệu',
            ];

            return [
                'title' => $this->faker->randomElement($titles) . ' - ' . $this->faker->date(),
            ];
        });
    }
}
