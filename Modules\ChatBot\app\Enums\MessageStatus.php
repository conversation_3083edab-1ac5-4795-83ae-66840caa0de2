<?php

namespace Modules\ChatBot\Enums;

enum MessageStatus: string
{
    case PENDING = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED = 'completed';
    case FAILED = 'failed';
    case CANCELLED = 'cancelled';

    /**
     * Get all enum values.
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get the label for the status.
     */
    public function label(): string
    {
        return match ($this) {
            self::PENDING => 'Đang chờ',
            self::PROCESSING => 'Đang xử lý',
            self::COMPLETED => 'Hoàn thành',
            self::FAILED => 'Thất bại',
            self::CANCELLED => 'Đã hủy',
        };
    }

    /**
     * Get the color for the status.
     */
    public function color(): string
    {
        return match ($this) {
            self::PENDING => 'warning',
            self::PROCESSING => 'info',
            self::COMPLETED => 'success',
            self::FAILED => 'danger',
            self::CANCELLED => 'secondary',
        };
    }

    /**
     * Get the icon for the status.
     */
    public function icon(): string
    {
        return match ($this) {
            self::PENDING => 'fas fa-clock',
            self::PROCESSING => 'fas fa-spinner',
            self::COMPLETED => 'fas fa-check-circle',
            self::FAILED => 'fas fa-exclamation-triangle',
            self::CANCELLED => 'fas fa-times-circle',
        };
    }

    /**
     * Check if the status indicates the message is processing.
     */
    public function isProcessing(): bool
    {
        return in_array($this, [self::PENDING, self::PROCESSING]);
    }

    /**
     * Check if the status indicates the message is finished.
     */
    public function isFinished(): bool
    {
        return in_array($this, [self::COMPLETED, self::FAILED, self::CANCELLED]);
    }

    /**
     * Check if the status indicates success.
     */
    public function isSuccessful(): bool
    {
        return $this === self::COMPLETED;
    }

    /**
     * Check if the status indicates failure.
     */
    public function isFailure(): bool
    {
        return in_array($this, [self::FAILED, self::CANCELLED]);
    }

    /**
     * Get statuses that can be retried.
     */
    public static function retryableStatuses(): array
    {
        return [self::FAILED, self::CANCELLED];
    }

    /**
     * Get statuses that are in progress.
     */
    public static function inProgressStatuses(): array
    {
        return [self::PENDING, self::PROCESSING];
    }
}
