<?php

namespace Modules\ChatBot\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\ChatBot\Database\Factories\MessageFactory;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Illuminate\Support\Str;
use Modules\ChatBot\Enums\ContentType;

class Message extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'conversation_id',
        'role',
        'content',
        'content_type',
        'tool_calls',
        'tool_call_id',
        'attachments',
        'model_used',
        'status',
        'prompt_tokens',
        'completion_tokens',
        'total_tokens',
        'cost',
        'response_time_ms',
        'started_at',
        'completed_at',
        'is_streaming',
        'partial_content',
        'stream_sequence',
        'error_message',
        'error_code',
        'quality_score',
        'is_helpful',
        'metadata',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [
        'id',
        'conversation_id',
        'model_used',
        'tool_calls',
        'tool_call_id',
        'prompt_tokens',
        'completion_tokens',
        'total_tokens',
        'cost',
        'response_time_ms',
        'started_at',
        'completed_at',
        'error_message',
        'error_code',
        'quality_score',
        'is_helpful',
        'metadata',
        'is_streaming',
        'partial_content',
        'stream_sequence',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'role' => MessageRole::class,
        'content_type' => ContentType::class,
        'status' => MessageStatus::class,
        'tool_calls' => 'array',
        'attachments' => 'array',
        'metadata' => 'array',
        'prompt_tokens' => 'integer',
        'completion_tokens' => 'integer',
        'total_tokens' => 'integer',
        'cost' => 'decimal:6',
        'response_time_ms' => 'integer',

        'quality_score' => 'integer',
        'is_helpful' => 'boolean',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     */
    protected $appends = [
        'has_error',
        'processing_status',
    ];

    /**
     * Get safe error status (without exposing error details).
     */
    public function getHasErrorAttribute(): bool
    {
        return !empty($this->error_message) || !empty($this->error_code);
    }

    /**
     * Get safe processing status.
     */
    public function getProcessingStatusAttribute(): string
    {
        if ($this->has_error) {
            return 'failed';
        }

        return match($this->status) {
            MessageStatus::PENDING => 'pending',
            MessageStatus::PROCESSING => 'processing',
            MessageStatus::COMPLETED => 'completed',
            MessageStatus::FAILED => 'failed',
            default => 'unknown'
        };
    }

    /**
     * Boot the model.
     */
    protected static function boot(): void
    {
        parent::boot();

        // Generate UUID on creation
        static::creating(function ($message) {
            if (empty($message->uuid)) {
                $message->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName(): string
    {
        return 'uuid';
    }

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): \Illuminate\Database\Eloquent\Factories\Factory|MessageFactory
    {
        return MessageFactory::new();
    }

    /**
     * Get the conversation that owns this message.
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(Conversation::class, 'conversation_id', 'id');
    }

    /**
     * Scope a query to only include user messages.
     */
    public function scopeFromUser(Builder $query): Builder
    {
        return $query->where('role', MessageRole::USER);
    }

    /**
     * Scope a query to only include assistant messages.
     */
    public function scopeFromAssistant(Builder $query): Builder
    {
        return $query->where('role', MessageRole::ASSISTANT);
    }

    /**
     * Scope a query to only include system messages.
     */
    public function scopeFromSystem(Builder $query): Builder
    {
        return $query->where('role', MessageRole::SYSTEM);
    }

    /**
     * Scope a query to only include tool messages.
     */
    public function scopeFromTool(Builder $query): Builder
    {
        return $query->where('role', MessageRole::TOOL);
    }

    /**
     * Scope a query to filter by status.
     */
    public function scopeWithStatus(Builder $query, MessageStatus $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include completed messages.
     */
    public function scopeCompleted(Builder $query): Builder
    {
        return $query->where('status', MessageStatus::COMPLETED);
    }

    /**
     * Scope a query to only include pending messages.
     */
    public function scopePending(Builder $query): Builder
    {
        return $query->where('status', MessageStatus::PENDING);
    }

    /**
     * Scope a query to only include failed messages.
     */
    public function scopeFailed(Builder $query): Builder
    {
        return $query->where('status', MessageStatus::FAILED);
    }

    /**
     * Scope a query to filter by conversation.
     */
    public function scopeForConversation(Builder $query, int $conversationId): Builder
    {
        return $query->where('conversation_id', $conversationId);
    }

    /**
     * Scope a query to filter by model used.
     */
    public function scopeByModel(Builder $query, string $model): Builder
    {
        return $query->where('model_used', $model);
    }

    /**
     * Check if message has tool calls.
     */
    public function hasToolCalls(): bool
    {
        return !empty($this->tool_calls);
    }

    /**
     * Check if message has attachments.
     */
    public function hasAttachments(): bool
    {
        return !empty($this->attachments);
    }



    /**
     * Check if message is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === MessageStatus::COMPLETED;
    }

    /**
     * Check if message has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === MessageStatus::FAILED;
    }



    /**
     * Get total cost for this message.
     */
    public function getTotalCost(): float
    {
        return (float) $this->cost ?? 0.0;
    }

    /**
     * Get token efficiency (tokens per second).
     */
    public function getTokenEfficiency(): ?float
    {
        if ($this->total_tokens && $this->response_time_ms) {
            return $this->total_tokens / ($this->response_time_ms / 1000);
        }

        return null;
    }
}
