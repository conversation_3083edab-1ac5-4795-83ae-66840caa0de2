<?php

namespace Modules\ChatBot\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\ChatBot\Models\Message;

class MessageReceived implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Message $message;

    public function __construct(Message $message)
    {
        $this->message = $message->load(['conversation']);
    }

    public function broadcastOn(): PrivateChannel
    {
        return new PrivateChannel('conversation.' . $this->message->conversation->uuid);
    }

    public function broadcastWith(): array
    {
        return [
            'uuid' => $this->message->uuid,
            'conversationId' => $this->message->conversation->uuid,
            'content' => $this->message->content,
            'contentType' => $this->message->type,
            'role' => $this->message->role,
        ];
    }

    public function broadcastAs(): string
    {
        return 'message.received';
    }
}
