<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->uuid()->unique();
            // Conversation reference
            $table->unsignedBigInteger('conversation_id');
            $table->foreign('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
            
            // Message basic info
            $table->enum('role', ['user', 'assistant', 'system', 'tool'])
                  ->index();
            $table->longText('content')->nullable(); // Main message content
            $table->enum('content_type', ['text', 'image', 'file', 'audio', 'video', 'mixed'])
                  ->default('text');
            
            // Tool Integration
            $table->json('tool_calls')->nullable(); // Array of tool calls made
            $table->string('tool_call_id')->nullable(); // For tool response messages
            
            // Attachments
            $table->json('attachments')->nullable(); // Files, images, documents
            
            // AI Response Tracking
            $table->string('model_used')->nullable(); // Which AI model was used
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])
                  ->default('completed')
                  ->index();
            
            // Token Usage & Cost
            $table->unsignedInteger('prompt_tokens')->nullable();
            $table->unsignedInteger('completion_tokens')->nullable();
            $table->unsignedInteger('total_tokens')->nullable();
            $table->decimal('cost', 10, 6)->nullable(); // Cost per message
            
            // Performance Metrics
            $table->unsignedInteger('response_time_ms')->nullable();
            $table->timestamp('started_at')->nullable(); // When processing started
            $table->timestamp('completed_at')->nullable(); // When processing completed
            

            
            // Error Handling
            $table->text('error_message')->nullable();
            $table->string('error_code')->nullable();
            
            // Message Quality (for future)
            $table->unsignedTinyInteger('quality_score')->nullable(); // 1-5
            $table->boolean('is_helpful')->nullable(); // User feedback
            
            // Metadata
            $table->json('metadata')->nullable(); // Custom data, debug info
            
            // Timestamps
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['conversation_id', 'role']);
            $table->index(['conversation_id', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['model_used', 'created_at']);
            $table->index('tool_call_id');
            
            // Analytics indexes
            $table->index(['created_at', 'total_tokens']); // For usage analytics
            $table->index(['created_at', 'cost']); // For cost analytics
        });



    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
