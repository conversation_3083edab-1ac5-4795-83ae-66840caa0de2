<?php

namespace Modules\Blog\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Core\Traits\ResponseTrait;
use Modules\Blog\Facades\BlogFacade;

class BlogCategoryController extends Controller
{
    use ResponseTrait;

    public function index(Request $request): JsonResponse
    {
        $categories = BlogFacade::getActiveCategoryHierarchy(currentLocale());

        return $this->successResponse($categories, __('Blog categories retrieved successfully.'));
    }

    public function show(Request $request, string $slug): JsonResponse
    {
        $category = BlogFacade::getCategoryBySlug($slug, currentLocale());

        if (!$category) {
            return $this->notFoundResponse(null, __('Blog category not found.'));
        }

        $posts = BlogFacade::getPostsByCategoryId($category->id, currentLocale());

        return response()->json([
            'success' => true,
            'message' => __('Blog category retrieved successfully.'),
            'data' => [
                'category' => $category,
                'posts' => collect($posts->items())->map(fn($post) => [
                    'id' => $post['id'] ?? null,
                    'title' => $post['title'] ?? '',
                    'slug' => $post['slug'] ?? '',
                    'summary' => $post['summary'] ?? '',
                    'image' => $post['image'] ?? null,
                    'published_at' => $post['published_at'] ?? null,
                    'author' => isset($post['author']) && $post['author'] ? [
                        'id' => $post['author']['id'] ?? null,
                        'name' => $post['author']['name'] ?? ''
                    ] : null
                ]),
                'posts_count' => $posts->total()
            ]
        ]);
    }



}
