<?php

namespace Modules\ChatBot\Tests\Feature;

use Illuminate\Support\Facades\Http;
use Mo<PERSON>les\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Services\AIService;
use Modules\ChatBot\Services\MessageService;

class AIIntegrationTest extends ChatBotTestCase
{
    /** @test */
    public function it_can_generate_ai_response_with_openai()
    {
        $this->authenticateUser();

        // Add user message
        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'What is artificial intelligence?',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [
            'temperature' => 0.7,
            'max_tokens' => 1000,
        ], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        $aiMessage = $response->json('data');
        $this->assertEquals('assistant', $aiMessage['role']);
        $this->assertEquals('completed', $aiMessage['status']);
        $this->assertEquals('gpt-4-turbo', $aiMessage['model_used']);
        $this->assertNotNull($aiMessage['content']);
        $this->assertGreaterThan(0, $aiMessage['prompt_tokens']);
        $this->assertGreaterThan(0, $aiMessage['completion_tokens']);
        $this->assertGreaterThan(0, $aiMessage['total_tokens']);

        // Verify HTTP call was made to OpenAI
        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.openai.com/v1/chat/completions' &&
                   $request['model'] === 'gpt-4-turbo' &&
                   $request['temperature'] === 0.7 &&
                   $request['max_tokens'] === 1000;
        });
    }

    /** @test */
    public function it_handles_ai_api_failures_gracefully()
    {
        $this->authenticateUser();
        $this->mockFailedAIResponse();

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'This will fail',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 500, false);

        // Check that a failed message was created
        $failedMessage = Message::where('conversation_id', $this->conversation->id)
            ->where('role', MessageRole::ASSISTANT)
            ->where('status', MessageStatus::FAILED)
            ->first();

        $this->assertNotNull($failedMessage);
        $this->assertNotNull($failedMessage->error_message);
    }

    /** @test */
    public function it_can_handle_tool_calling_workflow()
    {
        $this->authenticateUser();
        $this->mockAIResponseWithTools();

        // Mock tool execution
        Http::fake([
            'https://api.example.com/search' => Http::response([
                'results' => ['Tool execution successful'],
            ], 200),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Search for information about Laravel',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        $aiMessage = $response->json('data');
        $this->assertEquals('assistant', $aiMessage['role']);
        $this->assertNotNull($aiMessage['tool_calls']);

        // Verify tool call structure
        $toolCalls = $aiMessage['tool_calls'];
        $this->assertCount(1, $toolCalls);
        $this->assertEquals('call_123', $toolCalls[0]['id']);
        $this->assertEquals('function', $toolCalls[0]['type']);
        $this->assertEquals('web_search', $toolCalls[0]['function']['name']);
    }

    /** @test */
    public function it_can_process_conversation_context_correctly()
    {
        $this->authenticateUser();

        // Create conversation history
        $this->createTestMessage([
            'role' => MessageRole::SYSTEM,
            'content' => $this->bot->system_prompt,
        ]);
        $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Hello, my name is John',
        ]);
        $this->createTestMessage([
            'role' => MessageRole::ASSISTANT,
            'content' => 'Hello John! How can I help you today?',
        ]);
        $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'What is my name?',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        // Verify that the context was sent to AI
        Http::assertSent(function ($request) {
            $messages = $request['messages'];
            
            // Should include system prompt, user messages, and assistant responses
            $this->assertGreaterThanOrEqual(4, count($messages));
            
            // First message should be system
            $this->assertEquals('system', $messages[0]['role']);
            $this->assertEquals($this->bot->system_prompt, $messages[0]['content']);
            
            // Should include conversation history
            $userMessages = array_filter($messages, fn($msg) => $msg['role'] === 'user');
            $this->assertGreaterThanOrEqual(2, count($userMessages));
            
            return true;
        });
    }

    /** @test */
    public function it_can_calculate_costs_correctly()
    {
        $this->authenticateUser();

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Calculate the cost for this request',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        $aiMessage = $response->json('data');
        $this->assertNotNull($aiMessage['cost']);
        $this->assertGreaterThan(0, $aiMessage['cost']);
        
        // Cost should be calculated based on tokens
        $expectedCost = ($aiMessage['prompt_tokens'] * 0.000001) + ($aiMessage['completion_tokens'] * 0.000002);
        $this->assertEquals(round($expectedCost, 6), $aiMessage['cost']);
    }

    /** @test */
    public function it_can_handle_different_content_types()
    {
        $this->authenticateUser();

        // Test with image content
        $messageData = [
            'conversation_id' => $this->conversation->id,
            'content' => 'Please analyze this image',
            'content_type' => 'mixed',
            'attachments' => [
                [
                    'type' => 'image',
                    'url' => 'https://example.com/image.jpg',
                    'name' => 'analysis.jpg',
                    'mime_type' => 'image/jpeg',
                ],
            ],
        ];

        $createResponse = $this->postJson('/api/v1/messages', $messageData, $this->getApiHeaders());
        $this->assertApiResponse($createResponse, 201);

        $generateResponse = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());
        $this->assertApiResponse($generateResponse, 201);

        // Verify the AI can handle mixed content
        $aiMessage = $generateResponse->json('data');
        $this->assertEquals('assistant', $aiMessage['role']);
        $this->assertNotNull($aiMessage['content']);
    }

    /** @test */
    public function it_can_handle_rate_limiting()
    {
        $this->authenticateUser();

        // Mock rate limit response
        Http::fake([
            'api.openai.com/*' => Http::response([
                'error' => [
                    'message' => 'Rate limit exceeded',
                    'type' => 'rate_limit_error',
                ],
            ], 429),
        ]);

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'This will hit rate limit',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 500, false);
        $response->assertJsonFragment(['message' => 'Failed to generate AI response: API call failed: {"error":{"message":"Rate limit exceeded","type":"rate_limit_error"}}']);
    }



    /** @test */
    public function it_updates_conversation_stats_after_ai_response()
    {
        $this->authenticateUser();

        $initialMessageCount = $this->conversation->message_count;
        $initialLastMessageAt = $this->conversation->last_message_at;

        $userMessage = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Update stats test',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        // Refresh conversation from database
        $this->conversation->refresh();

        // Message count should be updated
        $this->assertGreaterThan($initialMessageCount, $this->conversation->message_count);
        
        // Last message timestamp should be updated
        if ($initialLastMessageAt) {
            $this->assertGreaterThan($initialLastMessageAt, $this->conversation->last_message_at);
        } else {
            $this->assertNotNull($this->conversation->last_message_at);
        }
    }

    /** @test */
    public function it_can_handle_multiple_ai_providers()
    {
        $this->authenticateUser();

        // Test with Anthropic
        $anthropicModel = ModelAI::factory()->create([
            'key' => 'claude-3-sonnet',
            'provider' => 'anthropic',
            'api_endpoint' => 'https://api.anthropic.com/v1/messages',
        ]);

        $anthropicBot = Bot::factory()->create([
            'model_ai_id' => $anthropicModel->id,
            'status' => 'active',
        ]);

        $anthropicConversation = Conversation::factory()->create([
            'bot_id' => $anthropicBot->id,
            'owner_id' => $this->user->id,
            'owner_type' => User::class,
        ]);

        $userMessage = Message::factory()->create([
            'conversation_id' => $anthropicConversation->id,
            'role' => MessageRole::USER,
            'content' => 'Test Anthropic integration',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$anthropicConversation->id}/generate-response", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);

        $aiMessage = $response->json('data');
        $this->assertEquals('claude-3-sonnet', $aiMessage['model_used']);

        // Verify Anthropic API was called
        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'api.anthropic.com');
        });
    }

    /** @test */
    public function it_can_retry_failed_ai_generation()
    {
        $this->authenticateUser();

        // First, create a failed message
        $failedMessage = $this->createTestMessage([
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::FAILED,
            'error_message' => 'Previous API call failed',
        ]);

        // Now retry should work with our mocked successful response
        $response = $this->postJson("/api/v1/messages/{$failedMessage->id}/retry", [], $this->getApiHeaders());

        $this->assertApiResponse($response);

        $retriedMessage = $response->json('data');
        $this->assertEquals('pending', $retriedMessage['status']);
        $this->assertNull($retriedMessage['error_message']);
    }

    /** @test */
    public function it_validates_ai_model_configuration()
    {
        $this->authenticateUser();

        // Create bot with inactive AI model
        $inactiveModel = ModelAI::factory()->create(['status' => 'inactive']);
        $botWithInactiveModel = Bot::factory()->create([
            'model_ai_id' => $inactiveModel->id,
            'status' => 'active',
        ]);

        $conversation = Conversation::factory()->create([
            'bot_id' => $botWithInactiveModel->id,
            'owner_id' => $this->user->id,
            'owner_type' => User::class,
        ]);

        $userMessage = Message::factory()->create([
            'conversation_id' => $conversation->id,
            'role' => MessageRole::USER,
            'content' => 'This should fail due to inactive model',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$conversation->id}/generate-response", [], $this->getApiHeaders());

        // Should handle gracefully (implementation dependent)
        // This test ensures the system doesn't crash with invalid configurations
        $this->assertTrue(in_array($response->status(), [200, 201, 400, 500]));
    }
}
