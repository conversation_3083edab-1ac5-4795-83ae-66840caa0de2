<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Blog\Enums\BlogStatus;

return new class extends Migration
{
    public function up(): void
    {
        // Categories table
        Schema::create('blog_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('parent_id')->nullable()->constrained('blog_categories')->onDelete('set null');
            $table->string('layout')->nullable();
            $table->string('path', 512)->nullable()->index();
            $table->unsignedInteger('depth')->default(0)->index();
            $table->string('status')->default(BlogStatus::Draft->value)->index();
            $table->timestamps();
            $table->softDeletes();
        });

        // Category translations
        Schema::create('blog_category_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('category_id')->constrained('blog_categories')->onDelete('cascade');
            $table->string('locale')->index();
            $table->string('name');
            $table->string('slug', 512);
            $table->string('image')->nullable();
            $table->text('description')->nullable();
            $table->string('meta_title', 512)->nullable();
            $table->text('meta_description')->nullable();
            $table->unique(['category_id', 'locale']);
            $table->unique(['slug', 'locale']);
            $table->timestamps();
        });

        // Posts table
        Schema::create('blog_posts', function (Blueprint $table) {
            $table->id();
            $table->boolean('featured')->default(false)->index();
            $table->string('layout')->nullable();
            $table->foreignId('author_id')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('published_at')->nullable()->index();
            $table->unsignedInteger('views')->default(0)->index();
            $table->string('status')->default(BlogStatus::Draft->value)->index();
            $table->timestamps();
            $table->softDeletes();
        });

        // Post translations
        Schema::create('blog_post_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('post_id')->constrained('blog_posts')->onDelete('cascade');
            $table->string('locale')->index();
            $table->string('title');
            $table->string('slug', 512);
            $table->string('image')->nullable();
            $table->text('summary')->nullable();
            $table->longText('content')->nullable();
            $table->string('meta_title', 512)->nullable();
            $table->text('meta_description')->nullable();
            $table->unique(['slug', 'locale']);
            $table->unique(['post_id', 'locale']);
            $table->timestamps();
        });

        // Pivot table for n-n relation
        Schema::create('blog_category_posts', function (Blueprint $table) {
            $table->foreignId('category_id')->constrained('blog_categories')->onDelete('cascade');
            $table->foreignId('post_id')->constrained('blog_posts')->onDelete('cascade');
            $table->primary(['category_id', 'post_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('blog_category_posts');
        Schema::dropIfExists('blog_post_translations');
        Schema::dropIfExists('blog_posts');
        Schema::dropIfExists('blog_category_translations');
        Schema::dropIfExists('blog_categories');
    }
};
