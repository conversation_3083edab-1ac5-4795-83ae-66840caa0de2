<?php

namespace Modules\ChatBot\Tests\Feature;

use Mo<PERSON>les\ChatBot\Models\Message;
use Modules\ChatBot\Enums\MessageRole;
use Modules\ChatBot\Enums\MessageStatus;
use Modules\ChatBot\Enums\ContentType;
use Modules\ChatBot\Enums\ConversationStatus;

class MessageApiTest extends ChatBotTestCase
{
    /** @test */
    public function it_can_list_conversation_messages()
    {
        $this->authenticateUser();

        // Create messages
        $this->createTestMessage(['role' => MessageRole::USER, 'content' => 'Hello']);
        $this->createTestMessage(['role' => MessageRole::ASSISTANT, 'content' => 'Hi there!']);
        $this->createTestMessage(['role' => MessageRole::USER, 'content' => 'How are you?']);

        $response = $this->getJson("/api/v1/conversations/{$this->conversation->id}/messages", $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'id',
                    'role',
                    'content',
                    'content_type',
                    'status',
                    'created_at',
                ],
            ],
            'meta' => [
                'current_page',
                'per_page',
                'total',
            ],
        ]);

        $this->assertCount(3, $response->json('data'));
    }

    /** @test */
    public function it_can_filter_messages_by_role()
    {
        $this->authenticateUser();

        $this->createTestMessage(['role' => MessageRole::USER]);
        $this->createTestMessage(['role' => MessageRole::USER]);
        $this->createTestMessage(['role' => MessageRole::ASSISTANT]);

        $response = $this->getJson("/api/v1/conversations/{$this->conversation->id}/messages?role=user", $this->getApiHeaders());

        $this->assertApiResponse($response);
        
        $messages = $response->json('data');
        $this->assertCount(2, $messages);
        
        foreach ($messages as $message) {
            $this->assertEquals('user', $message['role']);
        }
    }

    /** @test */
    public function it_can_create_user_message()
    {
        $this->authenticateUser();

        $messageData = [
            'conversation_id' => $this->conversation->id,
            'content' => 'Hello, I need help with my account',
            'content_type' => 'text',
        ];

        $response = $this->postJson('/api/v1/messages', $messageData, $this->getApiHeaders());

        $this->assertApiResponse($response, 201);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'id',
                'conversation_id',
                'role',
                'content',
                'content_type',
                'status',
                'created_at',
            ],
        ]);

        $this->assertDatabaseHasMessage([
            'conversation_id' => $this->conversation->id,
            'role' => 'user',
            'content' => 'Hello, I need help with my account',
            'status' => 'completed',
        ]);

        $this->assertEquals('user', $response->json('data.role'));
        $this->assertEquals('completed', $response->json('data.status'));
    }

    /** @test */
    public function it_can_create_message_with_attachments()
    {
        $this->authenticateUser();

        $messageData = [
            'conversation_id' => $this->conversation->id,
            'content' => 'Here is a screenshot of the issue',
            'content_type' => 'mixed',
            'attachments' => [
                [
                    'type' => 'image',
                    'url' => 'https://example.com/screenshot.png',
                    'name' => 'screenshot.png',
                    'size' => 1024000,
                    'mime_type' => 'image/png',
                ],
            ],
        ];

        $response = $this->postJson('/api/v1/messages', $messageData, $this->getApiHeaders());

        $this->assertApiResponse($response, 201);
        
        $message = $response->json('data');
        $this->assertEquals('mixed', $message['content_type']);
        $this->assertNotNull($message['attachments']);
        $this->assertCount(1, $message['attachments']);
    }

    /** @test */
    public function it_validates_message_creation_data()
    {
        $this->authenticateUser();

        // Missing required fields
        $response = $this->postJson('/api/v1/messages', [], $this->getApiHeaders());

        $this->assertValidationError($response, 'conversation_id');
        $this->assertValidationError($response, 'content');
    }

    /** @test */
    public function it_cannot_add_message_to_non_editable_conversation()
    {
        $this->authenticateUser();

        // Set conversation to completed
        $this->conversation->update(['status' => ConversationStatus::COMPLETED]);

        $messageData = [
            'conversation_id' => $this->conversation->id,
            'content' => 'This should fail',
        ];

        $response = $this->postJson('/api/v1/messages', $messageData, $this->getApiHeaders());

        $this->assertApiResponse($response, 500, false);
        $response->assertJsonFragment(['message' => 'Cannot add messages to this conversation.']);
    }

    /** @test */
    public function it_can_show_message_details()
    {
        $this->authenticateUser();

        $message = $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Test message content',
        ]);

        $response = $this->getJson("/api/v1/messages/{$message->id}", $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'id',
                'conversation_id',
                'role',
                'content',
                'conversation',
            ],
        ]);

        $this->assertEquals($message->id, $response->json('data.id'));
        $this->assertEquals('Test message content', $response->json('data.content'));
    }

    /** @test */
    public function it_can_update_message()
    {
        $this->authenticateUser();

        $message = $this->createTestMessage(['content' => 'Original content']);

        $updateData = [
            'content' => 'Updated content',
            'quality_score' => 5,
            'is_helpful' => true,
        ];

        $response = $this->putJson("/api/v1/messages/{$message->id}", $updateData, $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonFragment([
            'content' => 'Updated content',
            'quality_score' => 5,
            'is_helpful' => true,
        ]);

        $this->assertDatabaseHasMessage([
            'id' => $message->id,
            'content' => 'Updated content',
            'quality_score' => 5,
            'is_helpful' => true,
        ]);
    }

    /** @test */
    public function it_can_delete_message()
    {
        $this->authenticateUser();

        $message = $this->createTestMessage();
        $messageId = $message->id;

        $response = $this->deleteJson("/api/v1/messages/{$messageId}", [], $this->getApiHeaders());

        $this->assertApiResponse($response);
        $this->assertDatabaseMissing('messages', ['id' => $messageId]);
    }

    /** @test */
    public function it_can_generate_ai_response()
    {
        $this->authenticateUser();

        // Add a user message first
        $this->createTestMessage([
            'role' => MessageRole::USER,
            'content' => 'Hello, can you help me?',
        ]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/generate-response", [
            'temperature' => 0.7,
            'max_tokens' => 2048,
        ], $this->getApiHeaders());

        $this->assertApiResponse($response, 201);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'id',
                'role',
                'content',
                'model_used',
                'status',
                'prompt_tokens',
                'completion_tokens',
                'total_tokens',
                'cost',
            ],
        ]);

        $message = $response->json('data');
        $this->assertEquals('assistant', $message['role']);
        $this->assertEquals('completed', $message['status']);
        $this->assertNotNull($message['content']);
        $this->assertNotNull($message['model_used']);
    }

    /** @test */
    public function it_can_send_message_and_get_response()
    {
        $this->authenticateUser();

        $requestData = [
            'conversation_id' => $this->conversation->id,
            'content' => 'What is the weather like today?',
            'temperature' => 0.8,
        ];

        $response = $this->postJson('/api/v1/messages/send-and-respond', $requestData, $this->getApiHeaders());

        $this->assertApiResponse($response, 201);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'user_message' => [
                    'id',
                    'role',
                    'content',
                ],
                'ai_message' => [
                    'id',
                    'role',
                    'content',
                    'model_used',
                ],
            ],
        ]);

        $data = $response->json('data');
        $this->assertEquals('user', $data['user_message']['role']);
        $this->assertEquals('assistant', $data['ai_message']['role']);
        $this->assertEquals('What is the weather like today?', $data['user_message']['content']);
    }

    /** @test */
    public function it_can_retry_failed_message()
    {
        $this->authenticateUser();

        $failedMessage = $this->createTestMessage([
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::FAILED,
            'error_message' => 'API timeout',
        ]);

        $response = $this->postJson("/api/v1/messages/{$failedMessage->id}/retry", [], $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonFragment(['status' => 'pending']);

        $this->assertDatabaseHasMessage([
            'id' => $failedMessage->id,
            'status' => 'pending',
            'error_message' => null,
        ]);
    }

    /** @test */
    public function it_cannot_retry_non_failed_message()
    {
        $this->authenticateUser();

        $completedMessage = $this->createTestMessage([
            'status' => MessageStatus::COMPLETED,
        ]);

        $response = $this->postJson("/api/v1/messages/{$completedMessage->id}/retry", [], $this->getApiHeaders());

        $this->assertApiResponse($response, 400, false);
        $response->assertJsonFragment(['message' => 'Only failed messages can be retried.']);
    }

    /** @test */
    public function it_can_rate_message()
    {
        $this->authenticateUser();

        $message = $this->createTestMessage([
            'role' => MessageRole::ASSISTANT,
            'content' => 'This is a helpful response',
        ]);

        $ratingData = [
            'quality_score' => 4,
            'is_helpful' => true,
            'feedback' => 'Very helpful and accurate response',
        ];

        $response = $this->patchJson("/api/v1/messages/{$message->id}/rate", $ratingData, $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonFragment([
            'quality_score' => 4,
            'is_helpful' => true,
        ]);

        $this->assertDatabaseHasMessage([
            'id' => $message->id,
            'quality_score' => 4,
            'is_helpful' => true,
        ]);

        // Check metadata contains feedback
        $updatedMessage = Message::find($message->id);
        $this->assertEquals('Very helpful and accurate response', $updatedMessage->metadata['user_feedback']);
    }

    /** @test */
    public function it_can_get_messages_by_role()
    {
        $this->authenticateUser();

        $this->createTestMessage(['role' => MessageRole::USER]);
        $this->createTestMessage(['role' => MessageRole::USER]);
        $this->createTestMessage(['role' => MessageRole::ASSISTANT]);

        $response = $this->getJson("/api/v1/conversations/{$this->conversation->id}/messages/user", $this->getApiHeaders());

        $this->assertApiResponse($response);
        
        $messages = $response->json('data');
        $this->assertCount(2, $messages);
        
        foreach ($messages as $message) {
            $this->assertEquals('user', $message['role']);
        }
    }

    /** @test */
    public function it_can_get_failed_messages()
    {
        $this->authenticateUser();

        $this->createTestMessage(['status' => MessageStatus::COMPLETED]);
        $this->createTestMessage(['status' => MessageStatus::FAILED]);
        $this->createTestMessage(['status' => MessageStatus::FAILED]);

        $response = $this->getJson("/api/v1/conversations/{$this->conversation->id}/messages/failed", $this->getApiHeaders());

        $this->assertApiResponse($response);
        
        $messages = $response->json('data');
        $this->assertCount(2, $messages);
        
        foreach ($messages as $message) {
            $this->assertEquals('failed', $message['status']);
        }
    }

    /** @test */
    public function it_can_bulk_retry_failed_messages()
    {
        $this->authenticateUser();

        $failedMessage1 = $this->createTestMessage(['status' => MessageStatus::FAILED]);
        $failedMessage2 = $this->createTestMessage(['status' => MessageStatus::FAILED]);
        $completedMessage = $this->createTestMessage(['status' => MessageStatus::COMPLETED]);

        $response = $this->postJson("/api/v1/conversations/{$this->conversation->id}/messages/bulk-retry", [
            'message_ids' => [$failedMessage1->id, $failedMessage2->id],
        ], $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'retried_count',
                'total_failed',
                'messages',
            ],
        ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['retried_count']);
        $this->assertEquals(2, $data['total_failed']);
    }

    /** @test */
    public function it_can_get_message_statistics()
    {
        $this->authenticateUser();

        // Create messages with different roles and metrics
        $this->createTestMessage(['role' => MessageRole::USER]);
        $this->createTestMessage(['role' => MessageRole::USER]);
        $this->createTestMessage([
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::COMPLETED,
            'total_tokens' => 100,
            'cost' => 0.01,
            'response_time_ms' => 1500,
        ]);
        $this->createTestMessage([
            'role' => MessageRole::ASSISTANT,
            'status' => MessageStatus::COMPLETED,
            'total_tokens' => 150,
            'cost' => 0.015,
            'response_time_ms' => 2000,
        ]);

        $response = $this->getJson("/api/v1/messages/stats?conversation_id={$this->conversation->id}", $this->getApiHeaders());

        $this->assertApiResponse($response);
        $response->assertJsonStructure([
            'success',
            'data' => [
                'total_messages',
                'user_messages',
                'assistant_messages',
                'total_tokens',
                'total_cost',
                'average_response_time',
                'success_rate',
            ],
        ]);

        $stats = $response->json('data');
        $this->assertEquals(4, $stats['total_messages']);
        $this->assertEquals(2, $stats['user_messages']);
        $this->assertEquals(2, $stats['assistant_messages']);
        $this->assertEquals(250, $stats['total_tokens']);
        $this->assertEquals(0.025, $stats['total_cost']);
        $this->assertEquals(1750, $stats['average_response_time']);
        $this->assertEquals(100.0, $stats['success_rate']);
    }
}
