<?php

namespace Modules\Blog\Facades;

use Illuminate\Support\Facades\Facade;
use Illuminate\Support\Collection;
use Modules\Blog\Models\BlogPost;
use Modules\Blog\Models\BlogCategory;
use Illuminate\Pagination\LengthAwarePaginator;

/**
 * Blog Facade for easy access to blog functionality.
 *
 * @method static Collection getRootCategories(string $locale)
 * @method static Collection getActiveCategoryHierarchy(string $locale)
 * @method static BlogCategory|null getCategoryBySlug(string $slug, string $locale)
 * @method static LengthAwarePaginator getPostsByCategoryId(int $categoryId, string $locale)
 * @method static BlogPost|null getPostBySlug(string $slug, string $locale)
 * @method static Collection getFeaturePosts(string $locale, int $categoryId = null)
 * @method static Collection getTopPosts(string $locale, int $categoryId = null)
 * @method static Collection getLatestPosts(string $locale, int $categoryId = null)
 *
 * @see \Modules\Blog\Services\BlogService
 */
class BlogFacade extends Facade
{
    /**
     * Get the registered name of the component.
     */
    protected static function getFacadeAccessor(): string
    {
        return 'blog.service';
    }
}
