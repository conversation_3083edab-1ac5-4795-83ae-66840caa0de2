<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('queries', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique()->index();
            $table->string('question', 2000);
            
            // Owner information (polymorphic)
            $table->unsignedBigInteger('owner_id');
            $table->string('owner_type');
            $table->index(['owner_id', 'owner_type']);
            
            // Related entities
            $table->unsignedBigInteger('bot_id')->nullable();
            $table->unsignedBigInteger('conversation_id')->nullable();
            $table->unsignedBigInteger('message_id')->nullable();
            
            // Query parameters
            $table->json('file_ids')->nullable(); // Array of knowledge base IDs to search in
            $table->integer('top_k')->default(5); // Number of results to return
            $table->string('collection')->default('documents');
            
            // Processing status
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->text('error_message')->nullable();
            
            // Results storage
            $table->json('results')->nullable(); // Store the similarity search results
            $table->json('metadata')->nullable(); // Additional metadata
            
            // Timestamps
            $table->timestamp('processing_started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
            
            // Foreign key constraints
            $table->foreign('bot_id')->references('id')->on('bots')->onDelete('cascade');
            $table->foreign('conversation_id')->references('id')->on('conversations')->onDelete('cascade');
            $table->foreign('message_id')->references('id')->on('messages')->onDelete('cascade');
            
            // Indexes for performance
            $table->index('status');
            $table->index('bot_id');
            $table->index('conversation_id');
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('queries');
    }
};
